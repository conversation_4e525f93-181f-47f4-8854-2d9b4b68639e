<template>
  <ContentWrap>
    <div class="h-[calc(100vh-170px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" plain @click="intelligenceFormRef?.openForm()">
            <Icon icon="ep:plus" />
            新增
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :header-cell-config="{ height: 40 }"
          :cell-config="{ height: 40 }"
          :data="list"
          border
          align="center"
          show-overflow
          @cell-click="(el) => intelligenceFormRef?.openForm(el.row.intelligenceId)"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column title="专利号" field="patentNo" width="150" />
          <vxe-column title="专利名称" field="patentName" width="200" align="left" />
          <vxe-column title="专利摘要" field="abstracts" width="300" align="left" />
          <vxe-column title="权力人" width="150" field="ownership" />
          <vxe-column title="发明人" min-width="150" field="inventor" />
          <vxe-column title="我司关注的技术点" field="follow" min-width="600" align="left" />
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <IntelligenceForm ref="intelligenceFormRef" @success="getList()" />
  </ContentWrap>
</template>
<script setup lang="ts">
import { IntelligenceApi, IntelligenceVO } from '@/api/patent/intelligence'
import IntelligenceForm from './IntelligenceForm.vue'
import { ref, onMounted, nextTick } from 'vue'

// 消息弹窗
const message = useMessage()
const intelligenceFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<IntelligenceVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  patentNo: undefined,
  patentName: undefined,
  follow: undefined
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const data = await IntelligenceApi.getIntelligencePage(queryParams.value)
    list.value = data.list
    total.value = data.total
    console.log(list.value)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
