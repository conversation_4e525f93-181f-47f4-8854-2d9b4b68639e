<template>
  <el-form label-width="100px" ref="formRef" class="custom-form">
    <el-form-item label="原因">
      <div class="text-1rem bg-amber w-full rounded-1 break-all">
        {{
          formData.modifyInfo
            ?.filter((item) => item.modifyField === 'update')
            ?.map((item) => item.modifyFieldName)
            ?.join('\n')
        }}
      </div>
    </el-form-item>
    <el-form-item label="所属项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="风险分类" prop="category">
      <div class="!w-full bg-#f5f7fa rounded-5px p-l-10px">
        {{ getCategory(formData.category!) }}
      </div>
    </el-form-item>
    <el-form-item label="描述" prop="content">
      <el-input type="textarea" v-model="formData.content" :rows="5" :disabled="true" />
    </el-form-item>
    <el-form-item label="预计后果" prop="consequence">
      <el-input type="textarea" v-model="formData.consequence" :rows="4" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="影响" prop="affect">
          <el-select v-model="formData.affect" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_risk_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="概率" prop="probability">
          <el-select v-model="formData.probability" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_risk_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="等级" prop="level">
          <el-select v-model="formData.level" :disabled="true">
            <el-option
              v-for="dict in getStrDictOptions('project_risk_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="负责人" prop="director">
          <UserAvatarList
            v-model="formData.director!"
            :user-list="userList"
            :add="false"
            :size="28"
            :limit="5"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="提出人">
          <UserAvatarList
            v-model="formData.creator!"
            :user-list="userList"
            :add="false"
            :size="28"
            :limit="5"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" :disabled="true" v-if="formData.status !== 10">
            <el-option
              v-for="dict in getIntDictOptions('project_risk_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
              v-show="dict.value !== 10"
            />
          </el-select>
          <DictTag v-else type="project_risk_status" :value="formData.status" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="发生时间">
          <el-date-picker
            v-model="formData.onsetDate"
            type="date"
            :disabled="true"
            class="!w-full"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="计划应对措施">
      <el-input type="textarea" v-model="formData.solutionsPlan" :rows="4" :disabled="true" />
    </el-form-item>
    <el-form-item label="实际措施" prop="solutionsActual">
      <el-input type="textarea" v-model="formData.solutionsActual" :rows="4" :disabled="true" />
    </el-form-item>
    <el-form-item label="修改信息">
      <vxe-table
        :data="formData.modifyInfo?.filter((item) => item.modifyField !== 'update')"
        show-overflow
        :header-cell-style="{ padding: 0, fontSize: '1rem' }"
        :cell-style="{ padding: '5px', fontSize: '1rem', height: '1.5vw' }"
        border
        stripe
        align="center"
        class="!w-full"
      >
        <vxe-column title="修改字段/原因" field="modifyFieldName" />
        <!-- 统一处理列的渲染逻辑 -->
        <template v-for="col in ['beforeValue', 'afterValue']" :key="col">
          <vxe-column :title="col === 'beforeValue' ? '修改前' : '修改后'">
            <template #default="{ row }">
              <template v-if="shouldShowDictTag(row.modifyField)">
                <el-tag>
                  {{ formatDictLabel(row.modifyField, row[col]) }}
                </el-tag>
              </template>
              <template
                v-else-if="
                  ['managers', 'director', 'coordinate'].includes(row.modifyField) ||
                  getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                    (dict) => dict.value === row.modifyField
                  )?.label
                "
              >
                {{ getUserNickName(userList, row[col]) }}
              </template>
              <!-- <template v-else-if="row.modifyField === 'categoryIds'">
                {{ getCategoryName(row[col]) }}
              </template> -->
              <template v-else>{{ row[col] }}</template>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { getStrDictOptions, getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { propTypes } from '@/utils/propTypes'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'
import { RiskFlowApi } from '@/api/bpm/risk'
import { RiskCategory } from '../../risk/util'

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})
const userList = ref<UserVO[]>([])
const formData = ref<any>({})
/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

const getCategory = (category: string) => {
  const allChildren = RiskCategory.flatMap((item) => item.children)
  const found = allChildren.find((child) => child.value === category)
  return found?.label ?? ''
}

watch(
  () => props.processInstanceId,
  async () => {
    if (!props.processInstanceId) return
    formData.value = await RiskFlowApi.getRisk(props.processInstanceId)
  },
  { immediate: true }
)

onMounted(() => {
  getUserList()
})
</script>
