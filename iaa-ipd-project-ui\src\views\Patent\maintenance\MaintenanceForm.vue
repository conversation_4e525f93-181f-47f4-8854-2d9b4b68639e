<template>
  <!-- 对话框(添加 / 修改) -->
  <NoModalDrawer :title="dialogTitle" v-model="visible" :size="'60%'">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ dialogTitle }}</div>
        <div class="flex" v-if="formType === 'edit'">
          <el-tooltip content="修改">
            <el-button link :loading="loading" @click="onModify">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除">
            <el-button link :loading="loading" @click="handleDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      class="custom-form"
      size="small"
      :disabled="!edit"
    >
      <el-row>
        <el-col :span="24">
          <card-title title="著录项信息" />
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="公开号" prop="publicNo">
            <el-input v-model="formData.publicNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="专利名称" prop="patentName">
            <el-input v-model="formData.patentName" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="专利类型" prop="patentType">
            <el-select
              v-model="formData.patentType"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="申请状态" prop="status">
            <el-select
              v-model="formData.status"
              placeholder="请选择专利申请状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_MAINTENANCE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="申请号" prop="applicationNo">
            <el-input v-model="formData.applicationNo" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="申请日期" prop="applicationDate">
            <el-date-picker
              v-model="formData.applicationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
              class="!w-100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="获取方式" prop="acquisitionMethod">
            <el-input v-model="formData.acquisitionMethod" />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="授权日期" prop="authorizationDate">
            <el-date-picker
              v-model="formData.authorizationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
              class="!w-100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="有效期(年数)" prop="validityPeriod">
            <el-input v-model="formData.validityPeriod" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="法律状态" prop="legalStatus">
            <el-select
              v-model="formData.legalStatus"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="当前权力人" prop="ownership">
            <el-input v-model="formData.ownership" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="维持评估" prop="maintenanceAssessment">
            <el-input
              v-model="formData.maintenanceAssessment"
              type="textarea"
              :rows="5"
              placeholder="请输入维持评估"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-tabs
      v-model="currentTab"
      class="position-sticky top-0 z-10 bg-#fff"
      @tab-change="onTableChange"
    >
      <el-tab-pane label="保护信息" name="function">
        <el-form label-width="100px" class="custom-form mt-10px">
          <el-form-item label="保护技术点" prop="protectionPoint">
            <el-input v-model="formData.protectionPoint" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="保护产品型号" prop="models">
            <TagsInput v-model="formData.models" :disabled="!edit" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="人员信息" name="person">
        <InventorInfo
          ref="inventorInfoRef"
          :table-type="4"
          :belongs-id="formData.id"
          :form-type="formType"
        />
        <ApplicationInfo
          ref="applicationInfoRef"
          :table-type="4"
          :belongs-id="formData.id"
          :form-type="formType"
        />
        <AgencyInfo
          ref="agencyInfoRef"
          :table-type="4"
          :belongs-id="formData.id"
          :form-type="formType"
        />
      </el-tab-pane>
      <el-tab-pane label="同族专利" name="group">
        <GroupInfo
          ref="groupInfoRef"
          :table-type="4"
          :belongs-id="formData.id"
          :form-type="formType"
        />
      </el-tab-pane>
      <el-tab-pane label="附图" name="imgs">
        <UploadImgs v-model="formData.imgs!" height="80px" width="80px" :disabled="true" />
      </el-tab-pane>
      <el-tab-pane label="专利文件" name="attachment">
        <AttachmentInfo
          class="mt-10px"
          ref="attachmentRef"
          :table-type="4"
          :belongs-id="formData.id"
          :form-type="formType"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer v-if="formType === 'create'">
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { MaintenanceApi } from '@/api/patent/maintenance'

import AttachmentInfo from '../components/AttachmentInfo.vue'
import AgencyInfo from '../components/AgencyInfo.vue'
import ApplicationInfo from '../components/ApplicationInfo.vue'
import InventorInfo from '../components/InventorInfo.vue'
import GroupInfo from '../components/GroupInfo.vue'

import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import TagsInput from '../components/TagsInput.vue'

const edit = ref(false)
const visible = ref(false)
const formData = ref({
  id: undefined,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  protectionPoint: undefined,
  imgs: [] as string[],
  models: [] as string[],
  patentType: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  validityPeriod: undefined,
  inventor: undefined,
  actualInventor: undefined,
  acquisitionMethod: undefined,
  legalStatus: 0,
  agency: undefined,
  families: undefined,
  status: 0,
  maintenanceAssessment: undefined
})

const formRules = reactive({
  applicationNo: [{ required: true, message: '请输入申请号', trigger: 'blur' }],
  patentName: [{ required: true, message: '请输入专利名称', trigger: 'blur' }],
  patentType: [{ required: true, message: '请选择专利类型', trigger: 'blur' }],
  authorizationDate: [{ required: true, message: '请选择授权日期', trigger: 'blur' }],
  validityPeriod: [{ required: true, message: '请输入有效期(年数)', trigger: 'blur' }],
  status: [{ required: true, message: '请选择申请状态', trigger: 'blur' }],
  protectionPoint: [{ required: true, message: '请输入保护点', trigger: 'blur' }]
})

const attachmentRef = ref()
const inventorInfoRef = ref()
const applicationInfoRef = ref()
const agencyInfoRef = ref()
const groupInfoRef = ref()

/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'person':
      inventorInfoRef.value?.onList()
      applicationInfoRef.value?.onList()
      agencyInfoRef.value?.onList()
      break
    case 'group':
      groupInfoRef.value?.onList()
      break
    case 'attachment':
      attachmentRef.value?.onList()
      break
  }
}

// 默认选中著录项信息
const currentTab = ref('function')

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const openForm = async (rowId?: number) => {
  visible.value = true
  if (rowId) {
    formType.value = 'edit'
    dialogTitle.value = '修改专利维保'
    const data = await MaintenanceApi.getMaintenance(rowId)
    formData.value = data
    onTableChange()
    edit.value = false
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加专利维保'
    resetForm()
    edit.value = true
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    originalApplicant: undefined,
    ownership: undefined,
    applicationNo: undefined,
    publicNo: undefined,
    patentName: undefined,
    protectionPoint: undefined,
    imgs: [] as string[],
    models: [] as string[],
    patentType: undefined,
    applicationDate: undefined,
    authorizationDate: undefined,
    validityPeriod: undefined,
    inventor: undefined,
    actualInventor: undefined,
    acquisitionMethod: undefined,
    legalStatus: 0,
    status: 0,
    agency: undefined,
    families: undefined,
    maintenanceAssessment: undefined
  }
}
const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    const attachments = attachmentRef.value?.getData()
    const agencys = agencyInfoRef.value?.getData()
    const inventors = inventorInfoRef.value?.getData()
    const groups = inventorInfoRef.value?.getData()
    const applications = applicationInfoRef.value?.getData()
    await MaintenanceApi.createMaintenance({
      ...formData.value,
      attachments,
      agencys,
      inventors,
      groups,
      applications
    })
    message.success('创建成功')
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async () => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await MaintenanceApi.deleteMaintenance(formData.value.id!)
  message.success('删除成功')
  emit('success')
}

const onModify = async () => {
  if (!edit.value) {
    edit.value = true
    return
  } else {
    await formRef.value.validate()
    await MaintenanceApi.updateMaintenance(formData.value)
    message.success('修改成功')
    edit.value = false
    emit('success')
  }
}

defineExpose({
  openForm
})
</script>

<style scoped lang="scss">
:deep(.el-tabs__header.is-top) {
  background-color: #f1f1f1;
}
:deep(.card-title) {
  padding: 10px;
  display: block;
}
</style>
