import { gantt } from 'dhtmlx-gantt' //引入模块
import { DICT_TYPE,getDictObj } from '@/utils/dict'
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css' //引入甘特图样式

gantt.config.order_branch = true //左边表格 列可以拖动排序
gantt.config.grid_width = 480
gantt.i18n.setLocale('cn') //设置语言为中文
gantt.config.date_format = '%Y-%m-%d' //时间格式
gantt.config.show_links = true //禁用连线

// 时间轴样式修改
gantt.config.scale_unit = 'month'
gantt.config.step = 1
gantt.config.date_scale = '%Y年 %F'
gantt.config.subscales = [
  { unit: 'day', step: 1, date: '%d号' },
  { unit: 'day', step: 1, date: '周%D' }
]
gantt.config.scale_height = 60
gantt.config.row_height = 30
gantt.config.min_column_width = 40
gantt.config.readonly = true
gantt.config.show_progress = true

gantt.config.xml_date = "%Y-%m-%d %H:%i:%s";
gantt.templates.task_progress = (start, end, task) => {
  return `<div class="custom-progress" style="width:${task.progress * 100}%">
            <span class="animate-bar"></span>
          </div>`;
};

// 添加今天的日期分界线
gantt.plugins({
  marker: true
})
//自定义左侧 头部列的内容(方式1)
gantt.config.columns = [
  {
    name: 'text', //tasks参数名
    tree: true, //是否开始tree联级
    width: '190', //宽度 值为string 例如 width:"75"  "*" 为auto
    resize: true, //可重置大小
    label: '活动主题', //标签名
    template: function (obj) {
      return `<div class="gantt-name-box" style='margin-left:${(obj.level - 1) * 10}px' title="${obj.text}">${obj.orderNo} ${obj.text}</div>`
    }, //返回值
    align: 'left' //对齐方式
  },
  {
    name:'status',
    tree: false,
    resize: true,
    label: '状态',
    width:80,
    template: function (obj) {
      const dict=getDictObj(DICT_TYPE.PROJECT_ACTIVITIES_STATUS,obj.status);
      if(dict?.colorType){
        return `<span class="tag-${dict?.colorType}">${dict?.label}</span>`
      }else{
         return `<span style="background-color: ${dict?.cssClass};padding: 5px;border-radius: 5px;color: #fff;">${dict?.label}</span>`
      }
      
    },
    align:'center'
  },
  {
    name: 'startDate',
    tree: false,
    resize: true,
    label: '开始日期',
    template: function (obj) {
      return obj.startDate
    },
    width: 105,
    align: 'center'
  },
  {
    name: 'endDate',
    tree: false,
    resize: true,
    label: '结束日期',
    template: function (obj) {
      return obj.endDate
    },
    width: 105,
    align: 'center'
  }
] //定义好之后需要数据tasks中也需要写与自定义label内容一致的数据

export default gantt
