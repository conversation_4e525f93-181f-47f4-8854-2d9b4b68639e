import request from '@/config/axios'

export interface DecisionVO {
  id?: number // 评审ID
  basicsId?: number // 项目ID
  category?: number // 评审分类
  problem?: string // 评审问题
  director?: number[] // 评审负责人
  hasCustom?: boolean
  creator?: number[]
}

export const DecisionApi = {
  listDecision: async (params: any) => {
    return await request.get({ url: '/project/decision/list', params })
  },
  createDecision: async (data: DecisionVO) => {
    return await request.post({ url: '/project/decision/create', data })
  },
  updateDecision: async (data: DecisionVO) => {
    return await request.post({ url: '/project/decision/update', data })
  },
  deleteDecision: async (params: any) => {
    return await request.get({ url: '/project/decision/delete', params })
  }
}
