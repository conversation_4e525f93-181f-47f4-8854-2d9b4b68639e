<template>
  <vxe-split-pane width="300px" min-width="300px" name="activities-condition">
    <div class="p-10px">
      <el-form class="custom-form" label-width="70px">
        <el-form-item label="项目">
          <el-input v-model="formData.basicsName" />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select v-model="formData.basicsStatus" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动主题">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="formData.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_activities_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="活动内容">
          <el-input type="textarea" v-model="formData.content" :rows="5" />
        </el-form-item>
        <el-form-item label="活动描述">
          <el-input type="textarea" v-model="formData.description" :rows="5" />
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            type="daterange"
            v-model="formData.startDate"
            unlink-panels
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            type="daterange"
            v-model="formData.endDate"
            unlink-panels
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="负责人">
          <UserAvatarList
            v-model="formData.director!"
            :user-list="userList"
            :size="28"
            :limit="3"
          />
        </el-form-item>
        <el-form-item label="执行人">
          <UserAvatarList
            v-model="formData.coordinate!"
            :user-list="userList"
            :size="28"
            :limit="3"
          />
        </el-form-item>
        <el-form-item label="里程碑">
          <el-radio-group size="small" v-model="formData.isCrux">
            <el-radio-button label="所有" :value="-1" />
            <el-radio-button label="里程碑" :value="1" />
            <el-radio-button label="非里程碑" :value="0" />
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div class="p-10px h-full">
      <vxe-toolbar custom size="mini" ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" @click="onList"> 查询 </el-button>
          <el-button type="warning" size="small" @click="refresh"> 重置 </el-button>
        </template>
        <template #tools>
          <el-button type="primary" size="small" @click="handleExport"> 导出 </el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        height="86%"
        :header-cell-style="{ padding: '0', height: '34px' }"
        :cell-style="{ padding: '0', height: '34px' }"
        show-overflow
        align="center"
        border
        ref="tableRef"
        :data="activitiesList"
        :loading="loading"
        :export-config="{
          remote: true,
          exportMethod: handleExport
        }"
      >
        <vxe-column title="项目名称" field="basicsName" width="200" align="left" />
        <vxe-column title="项目等级" field="basicsLevel" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.basicsLevel) }}
          </template>
        </vxe-column>
        <vxe-column title="项目状态" field="basicsStatus" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_status', row.basicsStatus) }}
          </template>
        </vxe-column>

        <vxe-column title="项目平台" field="basicsPlatform" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_platform', row.basicsPlatform) }}
          </template>
        </vxe-column>
        <vxe-column title="项目类型" field="basicsMold" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_type', row.basicsMold) }}
          </template>
        </vxe-column>
        <vxe-column title="项目经理" field="managers" width="100">
          <template #default="{ row }">
            {{ getUserNickName(row.managers) }}
          </template>
        </vxe-column>
        <vxe-column title="活动主题" field="name" width="200" align="left" />
        <vxe-column title="活动类型" field="mold" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_activities_type', row.mold) }}
          </template>
        </vxe-column>
        <vxe-column title="活动内容" field="content" width="200" align="left" />
        <vxe-column title="活动描述" field="description" width="200" align="left" />
        <vxe-column title="状态" field="status" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_activities_status', row.status) }}
          </template>
        </vxe-column>
        <vxe-column title="进度" field="progress" width="100" />
        <vxe-column title="开始时间" field="startDate" width="100" />
        <vxe-column title="结束时间" field="endDate" width="100" />
        <vxe-column title="负责人" field="director" width="100">
          <template #default="{ row }">
            {{ getUserNickName(row.director) }}
          </template>
        </vxe-column>
        <vxe-column title="执行人" field="coordinate" width="100">
          <template #default="{ row }">
            {{ getUserNickName(row.coordinate) }}
          </template>
        </vxe-column>
        <vxe-column title="里程碑活动" field="isCrux" width="100">
          <template #default="{ row }">
            {{ getDictLabel('infra_boolean_string', row.isCrux) }}
          </template>
        </vxe-column>
        <vxe-column title="实际完成时间" field="completedDate" width="120" />
        <vxe-column title="完成流程审批通过时间" field="approvalCompletedDate" width="200" />
        <vxe-column title="工时" field="workingHours" width="120" />
      </vxe-table>
      <Pagination
        :total="total"
        v-model:page="formData.pageNo"
        v-model:limit="formData.pageSize"
        @pagination="onList"
      />
    </div>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { getIntDictOptions, getDictLabel } from '@/utils/dict'
import { UserVO, getAllUser } from '@/api/system/user'
import { ActivitiesApi } from '@/api/project/activities'
import download from '@/utils/download'
const formData = ref({
  basicsName: undefined,
  basicsStatus: undefined,
  name: undefined,
  status: undefined,
  content: undefined,
  description: undefined,
  startDate: undefined,
  endDate: undefined,
  director: undefined,
  coordinate: undefined,
  isCrux: undefined,
  pageNo: 1,
  pageSize: 20
})

const userList = ref<UserVO[]>([])
const activitiesList = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const message = useMessage()
const tableRef = ref()
const toolbarRef = ref()

const onListUser = async () => {
  userList.value = await getAllUser()
}

const onList = async () => {
  loading.value = true
  try {
    const res = await ActivitiesApi.getActivitiesPage(formData.value)
    activitiesList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}
const refresh = () => {
  formData.value = {
    basicsName: undefined,
    basicsStatus: undefined,
    name: undefined,
    status: undefined,
    content: undefined,
    description: undefined,
    startDate: undefined,
    endDate: undefined,
    director: undefined,
    coordinate: undefined,
    isCrux: undefined,
    pageNo: 1,
    pageSize: 20
  }
  onList()
}

/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname + (item.status == 2 ? '(离职)' : ''))
    .join(',')
}

const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    loading.value = true
    const data = await ActivitiesApi.exportActivitiesMulti(formData.value)
    download.excel(data, '活动信息.xlsx')
  } catch {
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  onListUser()
  onList()

  nextTick(() => {
    unref(tableRef).connect(unref(toolbarRef))
  })
})
</script>
