<template>
  <vxe-toolbar size="mini" custom ref="toolbarRef">
    <template #buttons>
      <el-button type="primary" plain @click="operationFormRef?.openForm(1)">
        <Icon icon="ep:plus" />
        新增专利诉讼获取无效记录
      </el-button>
    </template>
  </vxe-toolbar>
  <!-- 表格容器 -->
  <div class="h-[calc(100vh-280px)]">
    <vxe-table
      ref="tableRef"
      height="100%"
      :header-cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        backgroundColor: '#fafafa',
        color: 'var(--primary-text-color)'
      }"
      :row-style="{
        cursor: 'pointer'
      }"
      :cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        color: 'var(--primary-text-color)'
      }"
      :header-cell-config="{ height: 40 }"
      :cell-config="{ height: 40 }"
      :data="list"
      border
      align="center"
      @cell-click="(el) => operationFormRef?.openForm(1,el.row.operationId)"
    >
      <vxe-column title="类型" field="recordType" width="120">
        <template #default="{ row }">
          <DictTag type="patent_operation_record_type" :value="row.recordType" />
        </template>
      </vxe-column>
      <vxe-column title="申请人" field="applicant" min-width="120" />
      <vxe-column title="被申请人" field="beApplicant" min-width="120" />
      <vxe-column title="涉案专利号" field="applicationNo" min-width="120" />
      <vxe-column title="涉案专利名称" field="patentName" min-width="120" />
      <vxe-column title="涉案专利保护点" field="protectionPoint" min-width="120" />
      <vxe-column title="涉及产品型号" field="patentModel" min-width="120" />
      <vxe-column title="诉讼案件来源" field="litigationSource" min-width="120" />
      <vxe-column title="内部侵权分析报告" field="analysisReport" min-width="150" />
      <vxe-column title="诉讼选择或可行性" field="litigationSelect" min-width="150" />
      <vxe-column title="诉讼或无效案件案号" field="litigationCase" min-width="200" />
      <vxe-column title="委托机构及律师" field="entrustedLawyer" min-width="120" />
      <vxe-column title="开庭时间" field="trialDate" min-width="120" />
      <vxe-column title="诉讼或无效结论" field="litigationConclusion" min-width="120" />
    </vxe-table>
  </div>
  <Pagination
    size="small"
    :total="total"
    v-model:page="queryParams.pageNo"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
  <OperationForm ref="operationFormRef" @success="getList()" />
</template>

<script lang="ts" setup>
import { OperationApi } from '@/api/patent/operation'
import OperationForm from './OperationForm.vue'
import { el } from 'element-plus/es/locale'

const operationFormRef = ref()

const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  originalType: 1
})

const list = ref<any[]>()
const total = ref(0)
const loading = ref(false)

const getList = async () => {
  loading.value = true
  try {
    const data = await OperationApi.getOperationPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})
</script>
