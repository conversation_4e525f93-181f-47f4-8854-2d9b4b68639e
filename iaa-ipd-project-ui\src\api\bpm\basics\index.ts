import request from '@/config/axios'

export type BasicsFlowVO = {
  basicsId: number // 项目ID
  number: string // 项目编号
  name: string // 项目名称
  sort: number // 显示顺序
  platform: string // 平台
  mold: string // 类型
  level: string // 等级
  points: number // 积分
  managers: number[] // 项目经理
  targetMarket: string // 针对市场
  targetCustomer: string // 针对客户
  releaseDate: Date // 发布时间
  status: number // 状态
  progress: number // 进度
  categoryIds: number[] // 分类
  remark: string // 备注
  teams: any[] // 团队
  modifyInfo: BasicsModifyInfoVO[] // 修改记录
}

export type BasicsTeamsVO = {
  id?: number
  basicsId?: number
  role?: string
  userIds?: number[]
}

export type BasicsModifyInfoVO = {
  modifyField?: string // 修改的字段
  modifyFieldName?: string // 修改的字段名称
  beforeValue?: string // 修改前的值
  afterValue?: string // 修改后的值
}

export const BasicsFlowApi = {
  /** 获取流程中的项目信息 */
  getBasicsFlow: (id: number) => {
    return request.get({ url: `/bpm/project/basics/get/${id}` })
  },
  /** 获取流程中的项目信息 */
  getBasicsFlowByProcessInstanceId: (processInstanceId: string) => {
    return request.get({ url: `/bpm/project/basics/get-process/${processInstanceId}` })
  },
  /** 修改项目信息 */
  updateBasicsFlow: (data: BasicsFlowVO) => {
    return request.post({ url: `/bpm/project/basics/update-basics`, data })
  },
  /** 删除项目 */
  deleteBasicsFlow: (data: BasicsFlowVO) => {
    return request.post({ url: `/bpm/project/basics/delete-basics`, data })
  },
  /** 创建项目 */
  createBasicsFlow: (data: BasicsFlowVO) => {
    return request.post({ url: `/bpm/project/basics/create-basics`, data })
  },
  completeBasicsFlow: (data: BasicsFlowVO) => {
    return request.post({ url: `/bpm/project/basics/complete-basics`, data })
  }
}
