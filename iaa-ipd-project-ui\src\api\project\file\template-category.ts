import request from '@/config/axios'

// 项目文件模板分类 VO
export interface FileTemplateCategoryVO {
  id: number // 分类ID
  name: string // 分类名称
  path: string // 路径，存储从根节点到当前节点的路径
  parentId: number // 父节点ID，0表示根节点
}

// 项目文件模板分类 API
export const FileTemplateCategoryApi = {
  // 查询项目文件模板分类分页
  getFileTemplateCategoryPage: async (params: any) => {
    return await request.get({ url: `/project/file-template-category/page`, params })
  },

  // 查询项目文件模板分类详情
  getFileTemplateCategory: async (id: number) => {
    return await request.get({ url: `/project/file-template-category/get?id=` + id })
  },

  // 新增项目文件模板分类
  createFileTemplateCategory: async (data: FileTemplateCategoryVO) => {
    return await request.post({ url: `/project/file-template-category/create`, data })
  },

  // 修改项目文件模板分类
  updateFileTemplateCategory: async (data: FileTemplateCategoryVO) => {
    return await request.put({ url: `/project/file-template-category/update`, data })
  },

  // 删除项目文件模板分类
  deleteFileTemplateCategory: async (id: number) => {
    return await request.delete({ url: `/project/file-template-category/delete?id=` + id })
  },

  // 导出项目文件模板分类 Excel
  exportFileTemplateCategory: async (params) => {
    return await request.download({ url: `/project/file-template-category/export-excel`, params })
  },
}
