import request from '@/config/axios'

// 编码器规则列表 VO
export interface EncoderRuleVO {
  id: number // 编号
  name: string // 规则名称
  description: string // 规则描述
}

export interface EncoderRuleSegmentVO {
  id?: number //编号
  ruleId?: number //规则编号
  name?: string //名称
  segment?: string // 段类型
  value?: string //值类型
  serialLength?: number //流水码长度
  segmentOrder?: number // 序号
}

// 编码器规则列表 API
export const EncoderRuleApi = {
  // 查询编码器规则列表分页
  getEncoderRulePage: async (params: any) => {
    return await request.get({ url: `/project/encoder-rule/page`, params })
  },

  // 查询编码器规则列表详情
  getEncoderRule: async (id: number) => {
    return await request.get({ url: `/project/encoder-rule/get?id=` + id })
  },

  // 新增编码器规则列表
  createEncoderRule: async (data: EncoderRuleVO) => {
    return await request.post({ url: `/project/encoder-rule/create`, data })
  },

  // 修改编码器规则列表
  updateEncoderRule: async (data: EncoderRuleVO) => {
    return await request.put({ url: `/project/encoder-rule/update`, data })
  },

  // 删除编码器规则列表
  deleteEncoderRule: async (id: number) => {
    return await request.delete({ url: `/project/encoder-rule/delete?id=` + id })
  },

  // 导出编码器规则列表 Excel
  exportEncoderRule: async (params) => {
    return await request.download({ url: `/project/encoder-rule/export-excel`, params })
  },

  // 保存编码器规则段
  saveEncoderRuleSegment: async (ruleId: number, list: EncoderRuleSegmentVO[]) => {
    return await request.post({ url: `/project/encoder-rule/save-segment`, data: { ruleId, list } })
  },

  // 获得编码器规则段列表
  getEncoderRuleSegmentList: async (ruleId: number) => {
    return await request.get({ url: `/project/encoder-rule/get-segment-list/` + ruleId })
  },

  // 获取编码器规则列表
  getEncoderRuleList: async () => {
    return await request.get({ url: `/project/encoder-rule/list` })
  }
}
