<template>
  <el-popover trigger="click" width="700px" v-if="!props.activitiesId && !props.problemId">
    <template #reference>
      <el-button type="primary" link>
        {{ props.name }}
      </el-button>
    </template>
    <div class="flex p-5px justify-between">
      <div class="path-item" @click="toPath('activities')">活动</div>
      <div class="path-item" @click="toPath('problem')">问题</div>
      <div class="path-item" @click="toPath('risk')">风险</div>
      <div class="path-item" @click="toPath('technical')">技术评审</div>
      <div class="path-item" @click="toPath('tcp')">决策评审</div>
      <div class="path-item" @click="toPath('conference')">会议纪要</div>
      <div class="path-item" @click="toPath('document')">输出物</div>
      <div class="path-item" @click="toPath('gantt')">甘特图</div>
      <div
        class="path-item"
        @click="toPath('expense')"
        v-if="
          getRolePermission('pm', getUser.id) ||
          getRolePermission('financial', getUser.id) ||
          getUser.id == 1 ||
          permissions.includes('project:expense:view')
        "
      >
        费用
      </div>
      <div
        class="path-item"
        @click="toPath('kanban')"
        v-if="getRolePermission('pm', getUser.id) || getUser.id == 1"
      >
        看板
      </div>
    </div>
  </el-popover>
  <el-button
    type="primary"
    link
    @click="toPath(props.activitiesId ? 'activities' : 'problem')"
    v-else
  >
    {{ props.name }}
  </el-button>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { BasicsApi, BasicsVO } from '@/api/project/basics'
import { useCache, CACHE_KEY } from '@/hooks/web/useCache'
import { getRolePermission } from '@/views/project/ProjectCenter/main/util/permission'
import { useUserStore } from '@/store/modules/user'
import { ProblemApi } from '@/api/project/problem'
import { ActivitiesApi } from '@/api/project/activities'

const props = defineProps({
  name: propTypes.string.isRequired,
  basicsId: propTypes.number.isRequired,
  activitiesId: propTypes.number,
  problemId: propTypes.number
})

const router = useRouter()
const { wsCache } = useCache()
const { getUser } = useUserStore()
const permissions = wsCache.get(CACHE_KEY.USER).permissions

const toPath = async (type: string) => {
  const res = (await BasicsApi.getSimpleBasics(props.basicsId)) as BasicsVO
  if (props.activitiesId && type === 'activities') {
    const activities = await ActivitiesApi.getActivities(props.activitiesId)
    console.log(activities)
    wsCache.set('project_page_show_form', {
      categoryId: res.categoryIds?.[0],
      basicsId: props.basicsId,
      page: type,
      stage: activities.stage,
      id: activities.id
    })
  } else if (props.problemId && type === 'problem') {
    const problem = await ProblemApi.getProblem(props.problemId)
    wsCache.set('project_page_show_form', {
      categoryId: res.categoryIds?.[0],
      basicsId: props.basicsId,
      page: type,
      stage: problem.type,
      id: problem.id
    })
  } else {
    wsCache.set('project_page_show_form', {
      categoryId: res.categoryIds?.[0],
      basicsId: props.basicsId,
      page: type
    })
  }

  router.push({
    name: 'ProjectCenter'
  })
}
</script>

<style lang="scss" scoped>
.path-item {
  cursor: pointer;
  width: 10%;
  text-align: center;
  color: var(--el-color-primary-light-3);
}

.path-item + .path-item {
  border-left: 1px solid #f1f1f1;
}

.path-item:hover {
  background-color: #f5f5f5;
}
</style>
