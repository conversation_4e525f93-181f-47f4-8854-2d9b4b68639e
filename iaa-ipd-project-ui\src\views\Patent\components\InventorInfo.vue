<template>
  <div class="flex justify-between items-center">
    <CardTitle title="发明人" />
    <el-button type="primary" size="small" plain @click="handleAddInventor" :disabled="false">
      新增
    </el-button>
  </div>
  <vxe-table
    :data="inventorList"
    border
    size="small"
    class="custom-table"
    :header-cell-config="{ height: 40 }"
    :cell-config="{ height: 40 }"
  >
    <vxe-column type="seq" />
    <vxe-column field="inventorName" title="姓名或名称" min-width="100" />
    <vxe-column field="nameEnglish" title="姓名（英文）" min-width="100" />
    <vxe-column field="nationality" title="国籍或地区" min-width="100" />
    <vxe-column field="certificateType" title="证件类型" min-width="100">
      <template #default="{ row }">
        <dict-tag :type="DICT_TYPE.PATENT_CERTIFICATE_TYPE" :value="row.certificateType" />
      </template>
    </vxe-column>
    <vxe-column title="操作" width="150" align="center">
      <template #default="{ row, rowIndex }">
        <el-button type="danger" link size="small" @click="hanldeDeleteRow(row, rowIndex)">
          删除
        </el-button>
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 发明人编辑对话框 -->
  <Dialog
    v-model="inventorDialogVisible"
    :title="inventorDialogTitle"
    width="30%"
    @close="resetInventorForm"
  >
    <el-form
      ref="inventorFormRef"
      :model="inventorForm"
      :rules="inventorRules"
      size="small"
      label-width="100px"
      style="margin-top: 20px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名或名称" prop="inventorName">
            <el-input v-model="inventorForm.inventorName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名（英文）" prop="nameEnglish">
            <el-input v-model="inventorForm.nameEnglish" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="国籍或地区" prop="nationality">
            <el-input v-model="inventorForm.nationality" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件类型" prop="certificateType">
            <el-select
              v-model="inventorForm.certificateType"
              placeholder="请选择证件类型"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_CERTIFICATE_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="saveInventor(true)">继续添加</el-button>
      <el-button type="primary" @click="saveInventor()">添加</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { SubclassApi } from '@/api/patent/subclass'

const message = useMessage()

const props = defineProps({
  /** 所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析" */
  tableType: propTypes.oneOfType<0 | 1 | 2 | 3 | 4 | 5 | 6>([]).isRequired,
  belongsId: propTypes.number.isRequired, // 主表ID
  formType: propTypes.oneOfType<'create' | 'edit'>([]).isRequired
})

interface Inventor {
  id?: number
  inventorName?: string
  nameEnglish?: string
  nationality?: string
  certificateType?: number
  isAnnounced?: number
  belongsId?: number
  tableType?: number
}

// 发明人数据
const inventorList = ref<Inventor[]>([])
const inventorForm = ref<Inventor>({})
const inventorRules = {
  inventorName: [{ required: true, message: '请输入姓名或名称', trigger: 'blur' }]
}

// 发明人表单相关
const inventorDialogVisible = ref(false)
const inventorDialogTitle = ref('')
const inventorFormRef = ref()

const loading = ref(false)
// 发明人相关方法
const handleAddInventor = () => {
  inventorDialogTitle.value = '新增发明人'
  resetInventorForm()
  inventorDialogVisible.value = true
}

const hanldeDeleteRow = async (row: Inventor, rowIndex: number) => {
  if (row?.id) {
    await SubclassApi.deleteById(row.id, 'inventor')
    onList()
  } else {
    inventorList.value.splice(rowIndex, 1)
  }
  message.success('删除成功')
}
const resetInventorForm = () => {
  inventorForm.value = {}
}

const saveInventor = async (again: boolean = false) => {
  try {
    await inventorFormRef.value?.validate()
    inventorForm.value.tableType = props.tableType
    inventorForm.value.belongsId = props.belongsId
    if (props.belongsId && props.tableType) {
      await SubclassApi.batchCreate({
        tableType: props.tableType,
        belongsId: props.belongsId,
        inventorList: [inventorForm.value]
      })
      onList()
    } else {
      inventorList.value.push({ ...inventorForm.value })
    }
    resetInventorForm()
    if (!again) {
      inventorDialogVisible.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const onList = async () => {
  if (!props.belongsId || !props.tableType) return
  loading.value = true
  try {
    const res = await SubclassApi.querySubclass({
      tableType: props.tableType,
      belongsId: props.belongsId,
      queryType: 'inventor'
    })
    inventorList.value = res.list
  } finally {
    loading.value = false
  }
}

const getData = () => {
  return inventorList.value
}

defineExpose({
  onList,
  getData
})
</script>
