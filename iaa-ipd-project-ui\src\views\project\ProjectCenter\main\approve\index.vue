<template>
  <template
    v-if="
      ['activities', 'activitiesDelete', 'activitiesCrux'].includes(
        currentInstance?.formVariables?.['type']
      )
    "
  >
    <ActivitiesModifyOrDelete :processInstanceId="tempId" />
  </template>
  <template v-else-if="currentInstance?.formVariables?.['type'] === 'activitiesComplete'">
    <ActivitiesComplete :processInstanceId="tempId" />
  </template>
  <template
    v-else-if="['problem', 'problemDelete'].includes(currentInstance?.formVariables?.['type'])"
  >
    <ProbelmModifyOrDelete :processInstanceId="tempId" />
  </template>
  <template v-else-if="currentInstance?.formVariables?.['type'].includes('Target')">
    <AttachmentApproval :id="tempId" />
  </template>
  <template v-else-if="['problemComplete','problemCreate'].includes(currentInstance?.formVariables?.['type'])">
    <ProblemComplete :processInstanceId="tempId" />
  </template>
  <template v-else-if="['risk', 'riskDelete','riskCreate'].includes(currentInstance?.formVariables?.['type'])">
    <RiskModifyOrDelete :processInstanceId="tempId" />
  </template>
  <template v-else-if="['tr', 'trDelete'].includes(currentInstance?.formVariables?.['type'])">
    <TechnicalModifyOrDelete :processInstanceId="tempId" />
  </template>
  <template v-else-if="currentInstance?.formVariables?.['type'] === 'trComplete'">
    <TechnicalComplete :process-instance-id="tempId" />
  </template>
</template>

<script lang="ts" setup>
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import ActivitiesModifyOrDelete from './components/ActivitiesModifyOrDelete.vue'
import ActivitiesComplete from './components/ActivitiesComplete.vue'
import ProbelmModifyOrDelete from './components/ProbelmModifyOrDelete.vue'
import AttachmentApproval from '../components/AttachmentApproval.vue'
import ProblemComplete from './components/ProblemComplete.vue'
import RiskModifyOrDelete from './components/RiskModifyOrDelete.vue'
import TechnicalModifyOrDelete from './components/TechnicalModifyOrDelete.vue'
import TechnicalComplete from './components/TechnicalComplete.vue'
const { query } = useRoute()

const currentInstance = ref<any>()

const props = defineProps({
  processInstanceId: {
    type: String,
    default: ''
  }
})
const tempId = ref('')
const getCurrentInstance = async () => {
  const res = await ProcessInstanceApi.getProcessInstance(tempId.value)
  currentInstance.value = res
}

watch(
  () => [props.processInstanceId, query.id],
  () => {
    tempId.value = props.processInstanceId || (query.id as string)
    if (tempId.value) {
      getCurrentInstance()
    }
  },
  { immediate: true }
)
</script>
