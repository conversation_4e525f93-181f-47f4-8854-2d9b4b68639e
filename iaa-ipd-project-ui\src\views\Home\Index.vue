<template>
  <div class="flex h-[calc(100vh-90px)]">
    <el-card class="w-100px bg-white text-center rounded-5px p-5px" shadow="never">
      <template #header>
        <CardTitle title="仪表分类" />
      </template>
      <div
        v-for="category in categoryList"
        :key="category.value"
        :class="[currentNode == category.value && 'is-active', 'category-item']"
        @click="setCurrent(category.value)"
        v-hasRole="category.role"
      >
        {{ category.label }}
      </div>
    </el-card>
    <div class="w-[calc(100%-100px)] h-full">
      <Workbench v-if="currentNode == 'workbench'" />
      <ProjectCollection v-else-if="currentNode == 'project-collection'" />
      <Reanalyze v-else-if="currentNode == 'reanalyze'" />
      <Workhours v-else-if="currentNode == 'workhours'" />
      <ChangeFlow v-else-if="currentNode == 'change'" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import Workbench from './kanban/Workbench.vue'
import ProjectCollection from './kanban/ProjectCollection.vue'
import Reanalyze from './kanban/Reanalyze.vue'
import Workhours from './kanban/Workhours.vue'
import ChangeFlow from './kanban/ChangeFlow.vue'
import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

const setCurrent = (node: string) => {
  wsCache.set('kanban_current_node', node)
  currentNode.value = node
}

const currentNode = ref('workbench')
const categoryList = ref([
  {
    label: '工作台',
    value: 'workbench',
    role: ['']
  },
  {
    label: '项目集看板',
    value: 'project-collection',
    role: ['']
  },
  {
    label: '研发管理看板',
    value: 'workhours',
    role: ['r&d_manager', 'super_admin', 'project_view', 'porject_manager']
  },
  {
    label: '复盘工作台',
    value: 'reanalyze',
    role: ['porject_manager']
  },
  {
    label: '变更记录',
    value: 'change',
    role: ['r&d_manager', 'super_admin', 'project_view']
  }
])

onMounted(() => {
  const node = wsCache.get('kanban_current_node')
  if (node) {
    currentNode.value = node
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 5px;
}
:deep(.el-card__header) {
  padding: 10px 5px;
}
.category-item {
  border-bottom: 0.3px solid #f1f1f1;
  border-radius: 5px;
  font-size: 0.85rem;
  font-weight: bold;
  color: var(--primary-text-color);
  cursor: pointer;
  padding: 10px 0;
}
.category-item + .category-item {
  margin-top: 5px;
}
.category-item:hover,
.is-active {
  background-color: var(--el-color-primary-light-9);
}
</style>
