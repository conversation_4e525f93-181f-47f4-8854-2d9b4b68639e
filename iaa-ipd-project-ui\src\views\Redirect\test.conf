user www www;
worker_processes auto;
error_log /www/wwwlogs/nginx_error.log crit;
pid /www/server/nginx/logs/nginx.pid;
worker_rlimit_nofile 51200;

stream {
    log_format tcp_format '$time_local|$remote_addr|$protocol|$status|$bytes_sent|$bytes_received|$session_time|$upstream_addr|$upstream_bytes_sent|$upstream_bytes_received|$upstream_connect_time';

    access_log /www/wwwlogs/tcp-access.log tcp_format;
    error_log /www/wwwlogs/tcp-error.log;
    include /www/server/panel/vhost/nginx/tcp/*.conf;
}

events {
    use epoll;
    worker_connections 51200;
    multi_accept on;
}

http {
    include mime.types;
    #include luawaf.conf;

    include proxy.conf;

    default_type application/octet-stream;

    server_names_hash_bucket_size 512;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 50m;

    sendfile on;
    tcp_nopush on;

    keepalive_timeout 60;

    tcp_nodelay on;

    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
    fastcgi_read_timeout 300;
    fastcgi_buffer_size 64k;
    fastcgi_buffers 4 64k;
    fastcgi_busy_buffers_size 128k;
    fastcgi_temp_file_write_size 256k;
    fastcgi_intercept_errors on;
    

    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 2;
    gzip_types text/plain application/javascript application/x-javascript text/javascript text/css application/xml;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_disable "MSIE [1-6]\.";

    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    server_tokens off;
    access_log off;
    
    upstream service-center {
      ip_hash;
      server 127.0.0.1:13137;
    }
    
    upstream monitor-admin {
      server 127.0.0.1:13140;
    }
    
    upstream powerjob-server {
      server 127.0.0.1:7700;
    }

    server {
        listen 888;
        server_name phpmyadmin;
        index index.html index.htm index.php;
        root /www/server/phpmyadmin;

        #error_page   404   /404.html;
        include enable-php.conf;

        location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
            expires 30d;
        }

        location ~ .*\.(js|css)?$ {
            expires 12h;
        }

        location ~ /\. {
            deny all;
        }

        access_log /www/wwwlogs/access.log;
    }
    server {
        listen 1888;
        server_name *************;
        location / {
            root /home/<USER>/open-ai-server/dist;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }
    
    server {
      listen 13141;
      server_name localhost;
      
      location / {
        root /home/<USER>/iaa-data-center/web/;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
      }
      
      # location /WW_verify_ySdcGqFgrZHwHTKO.txt {
      #   alias /home/<USER>/iaa-data-center/WW_verify_ySdcGqFgrZHwHTKO.txt;
      # }
      
      location /admin-api/ { ## 后端项目 - 管理后台
          proxy_pass http://localhost:13143/admin-api/;
          
          ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 I
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          
          add_header Access-Control-Allow-Private-Network "true";
      }
      
      location /app-api/ { ## 后端项目 - 用户 App
          proxy_pass http://localhost:13143/app-api/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      }
      
      location /api/ {
         #反向代理的java地址
         proxy_pass  http://127.0.0.1:13142/;
         proxy_redirect    off;

         #设置代理消息头
         proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for; 
         proxy_set_header  X-Real-IP  $remote_addr;
         proxy_set_header  Host $http_host;
         
         #设置没有缓存[此处很重要，必须设置，不然有些浏览器对get请求会缓存，引发不必要的bug]
         expires -1;
         
         #一些安全配置
         add_header Set-Cookie "Path=/; HttpOnly; Secure";
         add_header X-Content-Type-Options "nosniff";
         add_header X-XSS-Protection "1; mode=block";

          #设置跨域方法
      	 add_header X-Frame-Options "ALLOW-FROM preview.smartadmin.vip";
   	     add_header Content-Security-Policy "frame-ancestors preview.smartadmin.vip";
      }
    }
    
    server {
      listen 13136;
      server_name localhost;
      
      # 限制外网访问内网 actuator 相关路径
      location ~ ^(/[^/]*)?/actuator(/.*)?$ {
          return 403;
      }
      
      location / {
          root   /home/<USER>/web;
          try_files $uri $uri/ /index.html;
          index  index.html index.htm;
      }
      
      location /prod-api/ {
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          # websocket参数
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          proxy_pass http://service-center/;
      }
      
      # https 会拦截内链所有的 http 请求 造成功能无法使用
      # 解决方案1 将 admin 服务 也配置成 https
      # 解决方案2 将菜单配置为外链访问 走独立页面 http 访问
      location /admin/ {
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_pass http://monitor-admin/admin/;
      }
      
      # https 会拦截内链所有的 http 请求 造成功能无法使用
      # 解决方案1 将 powerjob 服务 也配置成 https
      # 解决方案2 将菜单配置为外链访问 走独立页面 http 访问
      location /powerjob/ {
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_pass http://powerjob-server/;
      }

      # 解决 powerjob 代理之后静态文件无法访问的问题 请勿修改乱动
      location ~ ^/(js|css|jpg|png|svg|woff|ttf|ico|img)/ {
          proxy_pass http://powerjob-server;
      }
      
      error_page   500 502 503 504  /50x.html;
      location = /50x.html {
          root   html;
      }
    }
        
    
    server {
      listen 7788;
      server_name *************;
      
      location / {
          root /home/<USER>/after-sale-server/dist;
          index index.html;
          try_files $uri $uri/ /index.html;
      }
                # 代理后台服务
      location /v1/api/ {
          proxy_pass http://127.0.0.1:7787/v1/api/;
      }
      
      # 映射静态资源文件
      location /after-sale-upload-file/ {
          alias /home/<USER>/upload-file/after-sale-upload-file/;
      }
    }
    
    server {
      listen 13133;
      server_name localhost;
      
      location / {
        root /home/<USER>/iaa-data-center/web/;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
      }
      
      # location /WW_verify_ySdcGqFgrZHwHTKO.txt {
      #   alias /home/<USER>/iaa-data-center/WW_verify_ySdcGqFgrZHwHTKO.txt;
      # }
      
      location /admin-api/ { ## 后端项目 - 管理后台
          proxy_pass http://localhost:13134/admin-api/;
          
          ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 I
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          
          add_header Access-Control-Allow-Private-Network "true";
      }
      
      location /app-api/ { ## 后端项目 - 用户 App
          proxy_pass http://localhost:13134/app-api/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      }
      
      location /api/ {
         #反向代理的java地址
         proxy_pass  http://127.0.0.1:13134/;
         proxy_redirect    off;

         #设置代理消息头
         proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for; 
         proxy_set_header  X-Real-IP  $remote_addr;
         proxy_set_header  Host $http_host;
         
         #设置没有缓存[此处很重要，必须设置，不然有些浏览器对get请求会缓存，引发不必要的bug]
         expires -1;
         
         #一些安全配置
         add_header Set-Cookie "Path=/; HttpOnly; Secure";
         add_header X-Content-Type-Options "nosniff";
         add_header X-XSS-Protection "1; mode=block";

          #设置跨域方法
      	 add_header X-Frame-Options "ALLOW-FROM preview.smartadmin.vip";
   	     add_header Content-Security-Policy "frame-ancestors preview.smartadmin.vip";
      }
    }
    
    server {
      listen       40111;
      server_name  *************; ## 重要！！！修改成你的外网 IP/域名
      location / { ## 前端项目
          root   /home/<USER>/mall/admin;
          index  index.html index.htm;
          try_files $uri $uri/ /index.html;
      }
    }
    server {
      listen       40110;
      server_name  *************; ## 重要！！！修改成你的外网 IP/域名

      location /admin { ## 前端项目
          root   /home/<USER>/mall/admin;
          index  index.html index.htm;
          try_files $uri $uri/ /index.html;
      }
      
      location / { ## 前端项目
          root   /home/<USER>/mall/client;
          index  index.html index.htm;
          try_files $uri $uri/ /index.html;
      }

      location /admin-api/ { ## 后端项目 - 管理后台
          proxy_pass http://localhost:48080/admin-api/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      }
      location /app-api/ { ## 后端项目 - 用户 App
          proxy_pass http://localhost:48080/app-api/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
          proxy_set_header Host $http_host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header REMOTE-HOST $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      }
      
      location /infra/ws {
        proxy_pass http://localhost:48080/infra/ws;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
      }

    }
    include /www/server/panel/vhost/nginx/*.conf;
}