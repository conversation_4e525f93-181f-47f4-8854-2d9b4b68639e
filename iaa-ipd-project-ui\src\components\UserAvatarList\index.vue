<template>
  <div class="avatar-group">
    <div v-for="user in userSelectList.slice(0, limit)" :key="user.id" class="avatar-item">
      <avatar :user="user" :size="size" :show-delete="add" @delete="deleteUser(user)" />
    </div>
    <div class="avatar-item" v-if="userSelectList.length > limit">
      <el-popover placement="bottom" :width="200" trigger="click">
        <template #reference>
          <el-avatar :size="size">
            +{{ userSelectList.slice(limit, userSelectList.length).length }}
          </el-avatar>
        </template>
        <div class="avatar-group">
          <div
            v-for="user in userSelectList.slice(limit, userSelectList.length)"
            :key="user.id"
            class="avatar-item"
          >
            <avatar
              :user="user"
              :size="size"
              :show-delete="add"
              placement="top"
              @delete="deleteUser(user)"
            />
          </div>
        </div>
      </el-popover>
    </div>

    <el-button
      :icon="Plus"
      circle
      :style="{
        height: typeof size === 'number' ? size + 'px' : size,
        width: typeof size === 'number' ? size + 'px' : size
      }"
      @click="showUserSelectedDialog"
      v-if="add"
    />
    <user-selected :userList="showUserList" ref="userSelectedRef" @checked="success" v-if="add" />
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import UserSelected from '@/components/UserSelected/index.vue'
import avatar from './avatar.vue'

interface TempUserVO extends UserVO {
  backgroundColor: string
}

const userSelectedRef = ref()
const userSelectList = ref<TempUserVO[]>([]) //选中用户列表
const props = defineProps({
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired,
  modelValue: propTypes.oneOfType([Array<number>]).isRequired,
  size: propTypes.oneOfType([Number]).isRequired,
  limit: propTypes.number.def(10),
  add: propTypes.bool.def(true),
  visiableUserList: propTypes.oneOfType([Array<number>]).def([])
})
const emit = defineEmits(['update:modelValue', 'change:msg'])

const showUserList = computed(() => {
  return props.userList.filter(
    (item) => props.visiableUserList.length === 0 || props.visiableUserList.includes(item.id)
  )
})
/** 显示用户选择dialog */
const showUserSelectedDialog = () => {
  unref(userSelectedRef).showDialog(userSelectList.value.map((item) => item.id))
}

/** 选中用户回调 */
const success = (users: UserVO[]) => {
  users.forEach((user) => userSelectList.value.push({ ...user, backgroundColor: getRandomColor() }))
  emit(
    'update:modelValue',
    userSelectList.value.map((item) => item.id)
  )
  emit('change:msg', '新增了用户' + users.map((el) => el.nickname).join(','))
}
/** 移除用户回调 */
const deleteUser = (users: UserVO) => {
  userSelectList.value = userSelectList.value.filter((item) => item.id !== users.id)
  emit(
    'update:modelValue',
    userSelectList.value.map((item) => item.id)
  )
  emit('change:msg', '移除了用户' + users.nickname)
}
/** 随机一个用户头像颜色 */
const getRandomColor = () => {
  // 生成一个较暗的颜色
  const randomHex = () =>
    Math.floor(Math.random() * 128 + 50)
      .toString(16)
      .padStart(2, '0')
  const color = `#${randomHex()}${randomHex()}${randomHex()}`
  return color
}
/** 监听传入值改变事件 */
watch(
  [() => props.modelValue, () => props.userList],
  async ([newVal, userList]) => {
    if (newVal && userList.length > 0) {
      const userList = props.userList.filter((item) => newVal.includes(item.id))
      userSelectList.value = []
      userList?.forEach((item) =>
        userSelectList.value.push({ ...item, backgroundColor: getRandomColor() })
      )
    }
    if(!newVal){
      userSelectList.value = []
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="scss" scoped>
.avatar-group {
  display: flex;
  flex-wrap: wrap;
  padding: 3px;
  align-items: center;

  .avatar-item {
    cursor: pointer;
  }
  .avatar-item + .avatar-item {
    margin-left: -5px;
  }

  .avatar-item:hover {
    z-index: 5;
  }
}
</style>
