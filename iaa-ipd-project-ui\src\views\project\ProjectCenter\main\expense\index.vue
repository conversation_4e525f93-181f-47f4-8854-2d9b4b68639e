<template>
  <ActivitiesContainer :basics-info="props.basicsInfo!" :node-list="[]">
    <template #activities-table>
      <template v-if="props.basicsInfo?.erpCode">
        <div class="h-200px flex">
          <div class="h-full w-20%">
            <el-descriptions direction="vertical" :column="3" border size="small">
              <el-descriptions-item label="目标成本">
                {{ currentBasicsExpense[0]?.planCost }}
              </el-descriptions-item>
              <el-descriptions-item label="实际成本">
                {{ currentBasicsExpense[0]?.actualCost }}
              </el-descriptions-item>
              <el-descriptions-item label="达成率">
                {{
                  (
                    (currentBasicsExpense[0]?.planCost /
                      (currentBasicsExpense[0]?.actualCost || 0))*100
                  ).toFixed(2)
                }}%
              </el-descriptions-item>
              <el-descriptions-item label="目标费用">
                {{ currentBasicsExpense[0]?.planExpense }}
              </el-descriptions-item>
              <el-descriptions-item label="实际费用">
                {{ currentBasicsExpense[0]?.actualExpense }}
              </el-descriptions-item>
              <el-descriptions-item label="达成率">
                {{
                  (
                    ((currentBasicsExpense[0]?.actualExpense || 0) /
                      currentBasicsExpense[0]?.planExpense || 0) * 100
                  ).toFixed(2)
                }}%
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div id="material-pie" class="h-full w-40%"></div>
          <div id="expense-pie" class="h-full w-40%"></div>
        </div>
        <div class="h-[calc(95%-200px)]">
          <el-tabs stretch v-model="currentTab">
            <el-tab-pane label="物料费用" name="material" />
            <el-tab-pane label="报销费用" name="expense" />
            <el-tab-pane label="项目人力投入成本" name="human" />
          </el-tabs>
          <div class="h-[calc(100%-40px)]">
            <vxe-table
              v-if="currentTab === 'material'"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :footer-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
              round
              border
              auto-resize
              :row-config="{ isCurrent: true, isHover: true }"
              :column-config="{ resizable: true, isHover: true }"
              :data="erpExpenseList"
              show-overflow
              :loading="loading"
              align="center"
              show-footer
              :footer-method="footerMethodSum"
              stripe
            >
              <vxe-column title="项目编码" field="code" />
              <vxe-column title="项目名称" field="name" />
              <vxe-column title="单号" field="docNo" />
              <vxe-column title="单据类型" field="docType" />
              <vxe-column title="料号" field="itemCode" />
              <vxe-column title="物料名称" field="itemName" />
              <vxe-column title="数量" field="qty" />
              <vxe-column title="单位" field="unit" />
              <vxe-column title="金额" field="costMny" />
            </vxe-table>
            <vxe-table
              v-else-if="currentTab === 'expense'"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :footer-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
              round
              border
              auto-resize
              :row-config="{ isCurrent: true, isHover: true }"
              :column-config="{ resizable: true, isHover: true }"
              :data="ekuaibaoExpenseList"
              show-overflow
              :loading="loading"
              align="center"
              show-footer
              :footer-method="footerMethodSum"
              stripe
            >
              <vxe-column title="项目编码" field="code" />
              <vxe-column title="项目名称" field="name" />
              <vxe-column title="一级费用类型" field="expenseType1" />
              <vxe-column title="二级费用类型" field="expenseType2" />
              <vxe-column title="分摊金额" field="amount" />
              <vxe-column title="提交日期" field="submitDate" />
            </vxe-table>

            <vxe-table
              v-else-if="currentTab === 'human'"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
              round
              border
              auto-resize
              :row-config="{ isCurrent: true, isHover: true }"
              :column-config="{ resizable: true, isHover: true }"
              :data="artificialCostList"
              show-overflow
              :loading="loading"
              align="center"
              show-footer
              :footer-method="footerMethodSum"
            >
              <vxe-column title="人员" field="manager" />
              <vxe-column title="角色" field="role" />
              <vxe-column title="阶段全称" field="stageName" />
              <vxe-column title="成本费率" field="costRate" />
              <vxe-column title="实际工时" field="actualHours" />
              <vxe-column title="实际成本" field="actualCost" />
            </vxe-table>
          </div>
        </div>
      </template>
      <el-empty description="当前项目未绑定ERP项目编码，请联系财务人员绑定" v-else />
    </template>
  </ActivitiesContainer>
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { ExpenseApi } from '@/api/docking/expense'
import { refreshChart } from '@/views/Home/kanban/pie'
import { CostApi } from '@/api/project/cost'
import * as echarts from 'echarts'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  }
})
const loading = ref(false)
const erpExpenseList = ref<any[]>([])
const ekuaibaoExpenseList = ref<any[]>([])
const currentTab = ref('material')
const currentBasicsExpense = ref<any[]>([])
const artificialCostList = ref<any[]>([])
const getExpenseList = async () => {
  loading.value = true
  try {
    if (!props.basicsInfo?.erpCode) {
      return
    }
    erpExpenseList.value = await ExpenseApi.getErpList({ code: props.basicsInfo.erpCode })
    ekuaibaoExpenseList.value = await ExpenseApi.getEkuaibaoList({
      code: props.basicsInfo.erpCode
    })
    const res = await CostApi.getArtificialCost(props.basicsInfo.id!)
    artificialCostList.value = res
    await nextTick()
    const materialPieData = calculateGroupByField(erpExpenseList.value, 'docType', 'costMny')
    const expensePieData = calculateGroupByField(
      ekuaibaoExpenseList.value,
      'expenseType2',
      'amount'
    )

    refreshChart(
      materialPieData,
      echarts.init(document.getElementById('material-pie')!),
      '物料费用'
    )
    refreshChart(expensePieData, echarts.init(document.getElementById('expense-pie')!), '报销费用')
  } finally {
    loading.value = false
  }
}

const calculateGroupByField = (
  dataList: any[],
  typeField: string,
  amountField: string
): { name: string; value: number }[] => {
  const result: { [key: string]: number } = {}
  dataList.forEach((item) => {
    const type = item[typeField]
    if (!type) return
    const amount = item[amountField] || 0
    result[type] = (result[type] || 0) + Number(amount)
  })

  // 转换为数组格式
  return Object.entries(result).map(([label, value]) => ({
    name: label,
    value: Number(value.toFixed(2))
  }))
}

/** 表尾合计函数 */
function footerMethodSum({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '合计'
      }
      if (['amount', 'costMny', 'actualHours', 'actualCost'].includes(column.field)) {
        return sumNum(data, column.field).toFixed(2)
      }
      return ''
    })
  ]
  return footerData
}

// 进行合计
function sumNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i][type]
  }
  return total
}

watch(
  () => props.basicsInfo?.erpCode,
  async () => {
    if (props.basicsInfo?.id) {
      currentBasicsExpense.value = await ExpenseApi.getExpenseList({
        basicsId: props.basicsInfo?.id
      })
    }
    if (props.basicsInfo?.erpCode) {
      getExpenseList()
    }
  },
  { immediate: true }
)
</script>
