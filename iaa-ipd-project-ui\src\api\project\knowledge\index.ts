import request from '@/config/axios'

export interface KnowledgeVO {
  id?: string
  basicsId?: number
  name?: string
  parentId?: string
  hasFolder?: boolean
  infraFileId?: number
  url?: string
  download?: boolean
  size?: number
  createTime?: Date
  updateTime?: Date
  createName?: string
  updateName?: string
  children?: KnowledgeVO[]
}

export const KnowledgeApi = {
  getKnowledgeList: async (params: any) => {
    return await request.get({ url: '/project/knowledge/list', params })
  },
  getKnowledgeCategoryList: async (params: any) => {
    return await request.get({ url: '/project/knowledge/list-category', params })
  },
  getFileAllVersion: async (id: string) => {
    return await request.get({ url: `/project/knowledge/get-file-all-version/${id}` })
  }
}
