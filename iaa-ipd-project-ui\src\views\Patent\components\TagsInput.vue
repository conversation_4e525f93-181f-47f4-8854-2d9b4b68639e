<template>
  <div :class="['tags-input-container', props.disabled && 'disabled']">
    <!-- 标签展示 -->
    <div class="tag-list">
      <span v-for="(tag, index) in localTags" :key="index" class="tag">
        {{ tag }}
        <el-button
          type="primary"
          size="small"
          link
          circle
          @click="removeTag(index)"
          v-if="!props.disabled"
          >X</el-button
        >
      </span>
    </div>

    <!-- 输入框 -->
    <el-input
      v-model="inputValue"
      @keyup.enter="addTag"
      placeholder="请输入内容后按回车新增更多功能点"
      class="tag-input"
      v-if="!props.disabled"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const localTags = ref<any[]>([])
const inputValue = ref('')
// 监听外部传入的值变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      localTags.value = [...newVal]
    }
  },
  { immediate: true }
)
// 添加标签
const addTag = () => {
  const value = inputValue.value.trim()
  if (value && !localTags.value.includes(value)) {
    localTags.value.push(value)
    inputValue.value = ''
    emit('update:modelValue', [...localTags.value])
  }
}

// 删除标签
const removeTag = (index: number) => {
  localTags.value.splice(index, 1)
  emit('update:modelValue', [...localTags.value])
}
</script>

<style lang="scss" scoped>
.tags-input-container.disabled {
  background-color: var(--el-disabled-bg-color);
  cursor: not-allowed;
  .tag {
    background-color: var(--el-color-primary-light-3);
    color:#fff;
  }
  .tag:hover{
    background-color: var(--el-color-primary-light-3);
  }
}

.tags-input-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px 5px;
  min-height: 30px;
  width: 100%;

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-right: 8px;
  }

  .tag {
    background-color: var(--el-color-primary-light-9);
    height: 24px;
    min-width: 30px;
    padding: 2px 5px;
    border-radius: 8px;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e6e8eb;
    }

    .el-icon-close {
      font-size: 12px;
    }
  }
  :deep(.el-input__wrapper) {
    box-shadow: none !important;
    padding: 0;
  }

  .tag-input {
    border: none;
    outline: none;
    flex: 1;
    font-size: 13px;
    width: 100%;
    &::placeholder {
      color: var(--placeholder-text-color);
      font-style: italic;
    }
    &::-webkit-input-placeholder {
      color: var(--placeholder-text-color);
      font-style: italic;
    }
  }
}
</style>
