<template>
  <ContentWrap>
    <div class="h-[calc(100vh-140px)]">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="专利许可与转让" name="license">
          <LicenseTransfer />
        </el-tab-pane>
        <el-tab-pane label="专利诉讼与无效" name="lawsuit">
          <LawsuitInvalid/>
        </el-tab-pane>
      </el-tabs>
    </div>
  </ContentWrap>
</template>
<script setup lang="ts">
import LicenseTransfer from './LicenseTransfer.vue'
import LawsuitInvalid from './LawsuitInvalid.vue'

// 默认选中"针对专利许可、转让"标签
const activeTab = ref('license')

</script>

<style lang="scss" scoped>
:deep(.el-tabs__header.is-top) {
  margin-bottom: 0 !important;
}
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
