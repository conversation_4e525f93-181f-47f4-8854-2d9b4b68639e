<template>
  <ContentWrap>
    <div class="h-[calc(100vh-160px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" plain @click="searchWordFormRef?.openForm()">
            <Icon icon="ep:plus" />
            新增
          </el-button>
          <template v-if="showBatchButton">
            <el-button type="danger" plain @click="handleBatchDelete">批量删除</el-button>
            <el-button type="warning" plain @click="handleBatchRefresh">批量重审</el-button>
          </template>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-60px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          border
          align="center"
          :data="searchWordList"
          :header-cell-config="{height: 40}"
          :cell-config="{height: 40}"
          stripe
          :checkbox-config="{ highlight: true, range: true }"
          :filter-config="{ remote: true }"
          @filter-change="filterChangeEvent"
        >
          <vxe-column type="checkbox" width="50" />
          <vxe-column
            title="查询词"
            field="word"
            align="left"
            min-width="240"
            :filters="searchWordOptions"
            :filter-render="FilterValue.textFilterRender"
          >
            <template #default="{ row }">
              <el-button type="primary" link @click="toBpm(row.processInstanceId)">{{
                row.word
              }}</el-button>
            </template>
          </vxe-column>
          <vxe-column title="开始时间" field="startDate" width="120" />
          <vxe-column title="结束时间" field="endDate" width="120" />
          <vxe-column title="审批状态" field="approvalStatus" width="120">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column title="操作" width="140">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="searchWordFormRef?.openForm(row)"
                v-if="row.approvalStatus === 2"
              >
                <Icon icon="ep:edit" />
                编辑
              </el-button>
              <el-dropdown @command="handleMoreClick">
                <el-button type="warning" link><Icon icon="ep:d-arrow-right" /> 更多</el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="['delete', row]">
                      <Icon icon="ep:delete" />
                      删除
                    </el-dropdown-item>
                    <el-dropdown-item :command="['refresh', row]" v-if="row.approvalStatus === 2">
                      <Icon icon="ep:refresh" />
                      重新审签
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <SearchWordForm ref="searchWordFormRef" @success="getList" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { SearchWordApi } from '@/api/patent/search-word'
import { SearchWordFlowApi } from '@/api/bpm/patent/search-word'
import SearchWordForm from './SearchWordForm.vue'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { ElMessageBox } from 'element-plus'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'

const searchWordOptions = ref([{ data: '' }])

const searchWordFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const searchWordList = ref<any[]>([])
const loading = ref(false)
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  word: undefined
})

const router = useRouter()
const message = useMessage()

const showBatchButton = computed(() => {
  return unref(tableRef)?.getCheckboxRecords(false).length > 0
})

const getList = async () => {
  loading.value = true
  try {
    const res = await SearchWordApi.getSearchWordPage(queryParams.value)
    searchWordList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}
/** 操作：编辑、删除 */
const handleMoreClick = async (data) => {
  const type = data[0]
  const searchWord = data[1]
  switch (type) {
    case 'delete':
      if (searchWord.approvalStatus === 0) {
        // 二次确认
        const { value } = await ElMessageBox.prompt('请输入删除原因', '取消查询词确认流程', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
          inputErrorMessage: '删除原因不能为空'
        })
        await ProcessInstanceApi.cancelProcessInstanceByStartUser(
          searchWord.processInstanceId,
          value
        )
      }
      await SearchWordApi.deleteSearchWordByIds([searchWord.id])
      message.success('删除成功')
      handleList()
      break
    case 'refresh':
      await SearchWordFlowApi.createSearchWord(searchWord)
      handleList()
      break
  }
}

const handleBatchDelete = async () => {
  const ids = unref(tableRef)
    ?.getCheckboxRecords(false)
    .map((item) => item.id)
  const process = unref(tableRef)
    ?.getCheckboxRecords(false)
    .some((item) => item.approvalStatus === 0)
  if (process) {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入删除原因', '取消查询词确认流程', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '删除原因不能为空'
    })
    const processIds = unref(tableRef)
      ?.getCheckboxRecords(false)
      .filter((item) => item.approvalStatus === 0)
      .map((item) => item.id)
    await processIds.forEach(async (item) => {
      await ProcessInstanceApi.cancelProcessInstanceByStartUser(item, value)
    })
  }
  await SearchWordApi.deleteSearchWordByIds(ids)
  message.success(`已清除${ids.length}条数据`)
  handleList()
}

const handleBatchRefresh = async () => {
  const ids = unref(tableRef)
    ?.getCheckboxRecords(false)
    .filter((item) => item.approvalStatus === 2)
  await ids.forEach(async (item) => {
    await SearchWordFlowApi.createSearchWord(item)
  })
  message.success(`检测到${ids.length}条数据满足重审条件，已完成重审`)
  handleList()
}

const filterChangeEvent = ({ filterList }) => {
  for (const data of filterList) {
    queryParams.value[data.field] = data.datas[0] || data.values[0]
  }
  handleList()
}

onMounted(async () => {
  getList()
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
