<template>
  <vxe-table
    height="91%"
    :header-cell-style="{
      padding: '0',
      height: '2.5rem',
      fontSize: '.9rem',
      backgroundColor: '#fafafa',
      color: 'var(--primary-text-color)'
    }"
    :row-style="{
      cursor: 'pointer'
    }"
    :cell-style="{
      padding: '0',
      height: '2.5rem',
      fontSize: '.9rem',
      color: 'var(--primary-text-color)'
    }"
    round
    auto-resize
    border
    :row-config="{ drag: true, isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
    :column-config="{ resizable: true, isHover: true }"
    :export-config="{ remote: true, exportMethod: onExport }"
    :data="props.list"
    show-overflow
    ref="riskTableRef"
    @cell-click="(el: any) => emits('show:form', el.row)"
  >
    <vxe-column title="序号" field="sort" width="70" align="center" drag-sort />
    <vxe-column title="分类" field="category" width="135" align="center">
      <template #default="{ row }">
        <el-tag>{{ getCategory(row.category) }}</el-tag>
      </template>
    </vxe-column>
    <vxe-column
      title="描述"
      field="content"
      width="200"
      :filters="contentOptions"
      :filter-render="FilterValue.textFilterRender"
    />
    <vxe-column
      title="预计后果"
      field="consequence"
      width="200"
      :filters="consequenceOptions"
      :filter-render="FilterValue.textFilterRender"
    />
    <vxe-column title="影响" field="affect" width="60" align="center">
      <template #default="{ row }">
        <DictTag type="project_risk_level" :value="row.affect" />
      </template>
    </vxe-column>
    <vxe-column title="概率" field="probability" width="60" align="center">
      <template #default="{ row }">
        <DictTag type="project_risk_level" :value="row.probability" />
      </template>
    </vxe-column>
    <vxe-column
      title="等级"
      field="level"
      width="80"
      align="center"
      :filters="FilterValue.riskLevelOptions"
    >
      <template #default="{ row }">
        <DictTag type="project_risk_level" :value="row.level" />
      </template>
    </vxe-column>
    <vxe-column title="计划应对措施" field="solutionsPlan" width="200" />
    <vxe-column
      title="提出人"
      field="creator"
      width="120"
      align="center"
      :filters="coordinateOptions"
      :filter-render="FilterValue.userFilterRender"
    >
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.creator"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="false"
        />
      </template>
    </vxe-column>
    <vxe-column
      title="负责人"
      field="director"
      width="120"
      align="center"
      :filters="directorOptions"
      :filter-render="FilterValue.userFilterRender"
    >
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.director"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="false"
        />
      </template>
    </vxe-column>
    <vxe-column title="状态" field="status" width="90" align="center">
      <template #default="{ row }">
        <DictTag type="project_risk_status" :value="row.status" />
      </template>
    </vxe-column>
    <vxe-column title="发生时间" field="onsetDate" width="120" :formatter="dateFormatter3" />
    <vxe-column title="实际应对措施" field="solutionsActual" width="200" />
  </vxe-table>
  <!-- :export-config="{ remote: true, exportMethod: onExport }" -->
  <!-- @row-dragend="rowDragendEvent" -->
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { RiskApi, RiskVO } from '@/api/project/risk'
import { UserVO } from '@/api/system/user'
import { dateFormatter3 } from '@/utils/formatTime'
import { RiskCategory } from './util'
import download from '@/utils/download'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'

const contentOptions = ref([{ data: '' }])
const consequenceOptions = ref([{ data: '' }])
const directorOptions = ref([{ data: [] }])
const coordinateOptions = ref([{ data: [] }])

const props = defineProps({
  list: propTypes.oneOfType([Array<RiskVO>]).isRequired,
  userList: propTypes.oneOfType([Array<UserVO>]).def([]),
  toolbar: propTypes.any.isRequired,
  basicsId: propTypes.number.isRequired
})
const riskTableRef = ref()

const emits = defineEmits(['show:form'])
const message = useMessage()

const getCategory = (category: string) => {
  const allChildren = RiskCategory.flatMap((item) => item.children)
  const found = allChildren.find((child) => child.value === category)
  return found?.label ?? ''
}

/** 导出方法 */
const onExport = async ({ options }: any) => {
  try {
    if (!props.basicsId) return
    // 导出的二次确认
    await message.exportConfirm()
    const columns = options.columns.map((item) => item.field)
    if (columns.length === 0) {
      message.warning('未选择需要导出的列')
      return
    }
    // if (columns.includes('date')) {
    //   columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
    // }
    const data = await RiskApi.exportRisk({
      basicsId: props.basicsId,
      filename: options.filename,
      column: columns
    })
    download.excel(data, `${options.filename}.xlsx`)
  } catch {}
}

/** 监听改变用户组件大小 */
const _windowWidth = ref(28)
const userSize = computed({
  get: () => _windowWidth.value,
  set: (value) => (_windowWidth.value = value)
})

const handleResize = () => {
  const decimalPercentage = 1.6 / 100
  _windowWidth.value = window.innerWidth * decimalPercentage
}
onMounted(() => {
  nextTick(() => {
    unref(riskTableRef)?.connect(props.toolbar)
  })
  handleResize()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
:deep(.vxe-cell) {
  padding: 0 5px;
}

:deep(.el-tag) {
  --el-tag-font-size: 0.9rem;
  height: 1.5rem;
}

:deep(.vxe-cell) {
  height: 2.5rem !important;
}
</style>
