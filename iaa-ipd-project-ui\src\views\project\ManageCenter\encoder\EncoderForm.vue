<template>
  <Dialog title="编码生成器" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="编码规则" prop="pattern">
        <el-select v-model="formData.pattern">
          <el-option
            v-for="dict in getStrDictOptions('system_encoder')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="前缀">
        <el-input v-model="formData.prefix" placeholder="请输入前缀" />
      </el-form-item>
      <el-form-item label="日期格式" prop="dateFormat">
        <el-select v-model="formData.dateFormat">
          <el-option label="2025-01-01" value="yyyy-MM-dd" />
          <el-option label="20250101" value="yyyyMMdd" />
          <el-option label="250101" value="yyMMdd" />
          <el-option label="2025" value="yyyy" />
        </el-select>
      </el-form-item>
      <el-form-item label="流水号长度" prop="serialLength">
        <el-input-number v-model="formData.serialLength" :min="1" :max="10" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="saveEncoder">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { getStrDictOptions } from '@/utils/dict'
import { EncoderApi } from '@/api/infra/encoder'

const dialogVisible = ref(false)
const message = useMessage()
const formRef = ref()
const emit = defineEmits(['success'])

const formData = ref({
  id: undefined,
  name: undefined,
  pattern: undefined,
  prefix: undefined,
  dateFormat: undefined,
  serialLength: undefined
})

const formRules = ref({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  pattern: [{ required: true, message: '请选择编码规则', trigger: 'blur' }],
  dateFormat: [{ required: true, message: '请选择日期格式', trigger: 'blur' }],
  serialLength: [{ required: true, message: '请输入流水号长度', trigger: 'blur' }]
})

const open = (row?: any) => {
  dialogVisible.value = true
  if (row) {
    Object.assign(formData.value, row)
  }
}

const saveEncoder = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  await EncoderApi.saveEncoder(formData.value)
  message.success('保存成功')
  dialogVisible.value = false
  emit('success')
  formData.value = {
    id: undefined,
    name: undefined,
    pattern: undefined,
    prefix: undefined,
    dateFormat: undefined,
    serialLength: undefined
  }
}

defineExpose({
  open
})
</script>
