import request from '@/config/axios'

export interface MaintenanceVO {
  id: any // 主键
  originalApplicant: any
  ownership: any
  applicationNo: any
  publicNo: any
  patentName: any
  protectionPoint: any
  attachmentIds: any
  models: any
  patentType: any
  applicationDate: any
  authorizationDate: any
  validityPeriod: any
  inventor: any
  actualInventor: any
  acquisitionMethod: any
  legalStatus: any
  agency: any
  families: any
  maintenanceAssessment: any
}

export const MaintenanceApi = {
  /** 分页获取知识产权运营 */
  getMaintenancePage: (data: any) => {
    return request.post({ url: '/patent/maintenance/page', data })
  },
  /** 创建知识产权运营 */
  createMaintenance: (data: any) => {
    return request.post({ url: '/patent/maintenance/create', data })
  },
  /** 更新知识产权运营 */
  updateMaintenance: (data: any) => {
    return request.post({ url: '/patent/maintenance/update', data })
  },
  /** 删除知识产权运营 */
  deleteMaintenance: (id: number) => {
    return request.get({ url: `/patent/maintenance/delete/${id}` })
  },
  /** 查询知识产权运营详情 */
  getMaintenance: async (id: any) => {
    return await request.get({ url: `/patent/maintenance/get?id=` + id })
  }
}
