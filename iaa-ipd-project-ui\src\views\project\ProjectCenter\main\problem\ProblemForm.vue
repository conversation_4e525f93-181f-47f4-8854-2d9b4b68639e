<template>
  <NoModalDrawer
    id="problem"
    v-model="dialogVisible"
    :title="currentTitle"
    size="50%"
    :show-modal="showModal"
    destroy-on-close
  >
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">
          <el-switch
            v-model="formData.share"
            active-text="公开"
            inactive-text="不公开"
            v-if="formData.progress == 100"
            size="small"
            inline-prompt
            style="--el-switch-on-color: #32cd32"
            class="w-54px"
            @change="onShare"
            :disabled="!getRolePermission('pqa', getUser.id)"
          />
          {{ currentTitle }}
        </div>
        <div class="flex">
          <template v-if="formData.progress !== 100 && formData?.status != 10">
            <el-tooltip
              content="修改问题"
              v-if="
                formData.id &&
                (hasPermission('problem_modify') || formData.proposal?.includes(getUser.id))
              "
            >
              <el-button :loading="loading" link @click="onModify">
                <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
              </el-button>
            </el-tooltip>
            <el-tooltip
              content="删除问题"
              v-if="
                formData.id &&
                (hasPermission('problem_delete') || formData.proposal?.includes(getUser.id))
              "
            >
              <el-button :loading="loading" link @click="onDelete">
                <Icon icon="ep:delete" :size="22" />
              </el-button>
            </el-tooltip>
          </template>
        </div>
      </div>
    </template>
    <el-tooltip
      content="任务完成确认"
      v-if="
        !edit &&
        allowToComplete &&
        formData.status !== 10 &&
        formData.progress !== 100 &&
        allowPermission
      "
    >
      <el-button
        type="success"
        class="position-fixed w-20px !h-80px !p-0"
        style="left: 50%; top: calc(50% + 50px)"
        @click="onComplete"
        :loading="loading"
      >
        <Icon icon="ep:check" :size="22" />
      </el-button>
    </el-tooltip>
    <el-alert
      v-if="formData.status === 10"
      type="warning"
      show-icon
      description="当前问题有相关流程审批中"
      close-text="查看流程"
      @close="toBpm(formData.processInstanceId!)"
    />
    <el-alert
      v-if="!allowToComplete && !edit"
      type="warning"
      show-icon
      description="当前问题有输出物正审签中，暂不能提交完成申请"
    />
    <el-tabs
      v-model="currentTab"
      class="drawer-tabs position-sticky top-0 z-10 bg-#fff"
      @tab-change="onTabChange"
    >
      <el-tab-pane name="problem" label="问题" />
      <el-tab-pane name="target" label="输出物" v-if="!edit" />
      <el-tab-pane name="log" label="日志" v-if="!edit" />
    </el-tabs>
    <el-collapse
      v-model="activeNames"
      ref="collapseRef"
      class="custom-collapse"
      v-if="currentTab === 'problem'"
    >
      <el-collapse-item title="基础信息" name="1">
        <el-form
          label-width="120px"
          ref="formRef"
          class="custom-form"
          :rules="formRules"
          :model="formData"
        >
          <el-form-item label="描述" prop="content">
            <el-input
              type="textarea"
              :rows="4"
              v-model="formData.content"
              :disabled="!edit"
              @change="
                formDataChangeString(
                  'content',
                  '描述',
                  formData.content!,
                  tempRow?.content,
                  modifyInfo
                )
              "
            />
          </el-form-item>
          <el-form-item label="图片" prop="imgIds">
            <UploadImgs v-model="formData.imgIds!" height="60px" width="60px" :disabled="!edit" />
          </el-form-item>

          <el-row>
            <el-col :span="8">
              <el-form-item label="等级" prop="level">
                <el-select
                  v-model="formData.level"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'problemLevel',
                      '等级',
                      formData.level!,
                      tempRow?.level,
                      modifyInfo
                    )
                  "
                >
                  <el-option
                    v-for="dict in getStrDictOptions('project_problem_level')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分类" prop="category">
                <el-select
                  v-model="formData.category"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'problemCategory',
                      '分类',
                      formData.category!,
                      tempRow?.category,
                      modifyInfo
                    )
                  "
                >
                  <el-option
                    v-for="dict in getStrDictOptions('project_problem_category')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="责任模块" prop="module">
                <el-select
                  v-model="formData.module"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'problemModule',
                      '责任模块',
                      formData.module!,
                      tempRow?.module,
                      modifyInfo
                    )
                  "
                >
                  <el-option
                    v-for="dict in getStrDictOptions('project_problem_module')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="提出部门" prop="proposingDepartment">
                <el-select
                  v-model="formData.proposingDepartment"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'proposingDepartment',
                      '提出部门',
                      formData.proposingDepartment!,
                      tempRow?.proposingDepartment,
                      modifyInfo
                    )
                  "
                >
                  <el-option
                    v-for="dict in getStrDictOptions('project_problem_proposing_department')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="阶段" prop="stage">
                <el-select
                  v-model="formData.stage"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'stage',
                      '阶段',
                      String(formData.stage)!,
                      tempRow?.stage,
                      modifyInfo
                    )
                  "
                >
                  <el-option
                    v-for="item in props.templateNode"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="不良比例(%)" prop="rejectRatio">
                <el-input-number
                  v-model="formData.rejectRatio"
                  :min="1"
                  :max="100"
                  :disabled="!edit"
                  class="!w-full"
                  @change="
                    formDataChangeString(
                      'rejectRatio',
                      '不良比例(%)',
                      String(formData.rejectRatio)!,
                      tempRow?.rejectRatio,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="状态">
                <DictTag type="project_activities_status" :value="formData.status!" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提出时间" prop="timeOfProposal">
                <el-date-picker
                  type="date"
                  v-model="formData.timeOfProposal"
                  :disabled="!edit"
                  :disabled-date="disabledDate"
                  @change="
                    formDataChangeString(
                      'timeOfProposal',
                      '提出时间',
                      formatDate(formData.timeOfProposal!),
                      formatDate(tempRow?.timeOfProposal),
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="计划完成" prop="timeOfPlan">
                <el-date-picker
                  type="date"
                  v-model="formData.timeOfPlan"
                  :disabled="!edit"
                  :disabled-date="disabledDate"
                  @change="
                    formDataChangeString(
                      'timeOfPlan',
                      '计划完成',
                      formatDate(formData.timeOfPlan!),
                      formatDate(tempRow?.timeOfPlan),
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="提出人" prop="proposal" v-if="formData?.id">
            <user-avatar-list
              v-model="formData.proposal!"
              :user-list="props.userList"
              :size="28"
              :limit="10"
              :add="edit"
              :visiable-user-list="getVisiableUserList()"
              @change:msg="
                formDataChangeArray(
                  'proposal',
                  '提出人',
                  formData.proposal!,
                  tempRow.proposal!,
                  modifyInfo
                )
              "
            />
          </el-form-item>
          <el-form-item label="责任人" prop="director">
            <user-avatar-list
              v-model="formData.director!"
              :user-list="props.userList"
              :size="28"
              :limit="10"
              :add="edit"
              :visiable-user-list="getVisiableUserList()"
              @change:msg="
                formDataChangeArray(
                  'director',
                  '责任人',
                  formData.director!,
                  tempRow.director!,
                  modifyInfo
                )
              "
            />
          </el-form-item>
          <el-form-item label="执行人">
            <user-avatar-list
              v-model="formData.coordinate!"
              :user-list="props.userList"
              :size="28"
              :limit="10"
              :add="edit"
              :visiable-user-list="getVisiableUserList()"
              @change:msg="
                formDataChangeArray(
                  'director',
                  '责任人',
                  formData.coordinate!,
                  tempRow.coordinate!,
                  modifyInfo
                )
              "
            />
          </el-form-item>
          <el-form-item label="进度" v-if="formData.id">
            <el-progress
              :percentage="formData.progress"
              class="w-100% no-radius"
              :text-inside="true"
              :stroke-width="20"
              status="success"
            />
          </el-form-item>
          <el-form-item label="原因分析" prop="reason">
            <el-input
              type="textarea"
              :rows="4"
              v-model="formData.reason"
              :disabled="!edit"
              @change="
                formDataChangeString(
                  'reason',
                  '原因分析',
                  formData.reason!,
                  tempRow?.reason,
                  modifyInfo
                )
              "
            />
          </el-form-item>

          <el-form-item label="解决措施" prop="measures">
            <el-input
              type="textarea"
              :rows="4"
              v-model="formData.measures"
              :disabled="!edit"
              @change="
                formDataChangeString(
                  'measures',
                  '解决措施',
                  formData.measures!,
                  tempRow?.measures,
                  modifyInfo
                )
              "
            />
          </el-form-item>

          <template v-if="formData.id && !edit">
            <el-button
              type="warning"
              plain
              class="w-full"
              @click="onShowFeedbackForm"
              v-if="formData.progress !== 100"
            >
              填写原因分析/解决措施
            </el-button>

            <el-row>
              <el-col :span="8">
                <el-form-item label="验证时间">
                  <el-date-picker
                    type="date"
                    v-model="formData.timeOfVerification"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="效果确认">
                  <el-input v-model="formData.effect" :disabled="true" />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="确认人">
                  <user-avatar-list
                    v-model="formData.effectPerson!"
                    :user-list="props.userList"
                    :size="28"
                    :limit="10"
                    :add="false"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="跟进记录" name="2" v-show="!edit">
        <Comment ref="commentRef" category="problem" :limit="5" bgColor="#fff" />
      </el-collapse-item>
    </el-collapse>
    <template v-else-if="currentTab === 'target'">
      <vxe-table
        class="w-100%"
        :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
        :cell-style="{ padding: '5px', height: '30px' }"
        show-overflow
        :data="attachmentList"
        align="center"
        border
      >
        <vxe-column title="文件名" field="name" min-width="200" align="left">
          <template #default="{ row }">
            <el-link
              type="primary"
              @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
            >
              {{ row.name }}
            </el-link>
          </template>
        </vxe-column>
        <vxe-column title="版本" field="currentVersion" width="60" />
        <vxe-column title="审签状态" field="approvalStatus" width="90">
          <template #default="{ row }">
            <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
          </template>
        </vxe-column>
        <vxe-column
          title="审核通过时间"
          field="approvalTime"
          :formatter="dateFormatter3"
          width="120"
        />
        <vxe-column title="操作" width="100px" fixed="right" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              size="small"
              v-if="![1, 0].includes(row.approvalStatus) && row.creator === getUser.id"
              @click="fileUploadAnewRef?.openForm(row)"
            >
              重传
            </el-button>
            <el-button
              type="primary"
              link
              size="small"
              v-if="row.approvalStatus != 1 && row.creator === getUser.id"
              @click="delAttachment(row)"
            >
              删除
            </el-button>
            <!-- <el-button
              type="primary"
              link
              size="small"
              v-if="row.approvalStatus == 1"
              @click="downloadAttachment(row)"
            >
              下载
            </el-button> -->
          </template>
        </vxe-column>
      </vxe-table>
    </template>
    <template v-else-if="currentTab === 'log'">
      <LogForm
        ref="logFormRef"
        category="problem"
        :base-id="formData.id"
        :user-list="props.userList"
      />
    </template>

    <template #footer v-if="edit && !formData.id">
      <el-button type="primary" :loading="loading" @click="onSubmit(false)">保存</el-button>
      <el-button type="primary" :loading="loading" @click="onSubmit(true)" v-if="!showModal">
        保存并继续添加
      </el-button>
    </template>
    <template #footer v-else-if="['problem', 'target'].includes(currentTab) && !edit">
      <CommentInput
        v-model="commentData"
        placeholder="录入您的跟进记录，点击右下角纸飞机图标或回车发送"
        :user-list="props.userList"
        :sharp="false"
        @send="onSaveComment"
        v-if="currentTab == 'problem'"
      />
      <FileUpload
        v-if="
          currentTab == 'target' &&
          formData.status !== 10 &&
          formData.progress !== 100 &&
          allowPermission
        "
        ref="fileUploadRef"
        category="problem"
        :dynamic-id="formData.id"
        @success="onListAttachment"
      />
    </template>
    <FileUploadAnew
      category="problem"
      :dynamic-id="formData.id!"
      ref="fileUploadAnewRef"
      @success="onListAttachment"
    />
    <AttachmentPreview ref="attachmentPreviewRef" />
    <Dialog title="填写原因分析/解决措施" v-model="feedbackVisible">
      <el-form>
        <el-form-item label="原因分析">
          <el-input type="textarea" v-model="feedbackForm.reason" :rows="6" />
        </el-form-item>
        <el-form-item label="解决措施">
          <el-input type="textarea" v-model="feedbackForm.measures" :rows="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="onSubmitFeedback">保存</el-button>
      </template>
    </Dialog>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { getStrDictOptions } from '@/utils/dict'
import { ProblemVO, ProblemApi } from '@/api/project/problem'
import { FormRules } from 'element-plus'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import { dateUtil } from '@/utils/dateUtil'
import { CommentVO } from '@/components/CommentInput/comment'
import { CommentApi, CommentSaveReqVO } from '@/api/infra/comment'
import { dateFormatter3, formatDate } from '@/utils/formatTime'
import LogForm from '../../details/components/LogForm.vue'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import FileUploadAnew from '../components/FileUploadAnew.vue'
import AttachmentPreview from '../components/AttachmentPreview.vue'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { ElMessageBox } from 'element-plus'
import FileUpload from '../components/FileUpload.vue'
import { hasPermission } from '../util/permission'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { formDataChangeString, formDataChangeArray } from '@/utils/formDataChange'
import { cloneDeep } from 'lodash-es'
import { ProblemFlowApi } from '@/api/bpm/problem'
import { getVisiableUserList, getRolePermission } from '../util/permission'
import { useUserStore } from '@/store/modules/user'
import { downloadByOnlineUrl } from '@/utils/filt'
import * as FileApi from '@/api/infra/file'
const downloadAttachment = async (row: any) => {
  const res = await FileApi.get(row.infraFileId)
  downloadByOnlineUrl(res.url, row.name)
}

const activeNames = ref(['1', '2'])
const message = useMessage()
const dialogVisible = ref(false)
const currentTitle = ref('')
const currentTab = ref('problem')
const loading = ref(false)
const formRef = ref()
const edit = ref(true)
const commentRef = ref()
const fileUploadAnewRef = ref()
const attachmentPreviewRef = ref()
const logFormRef = ref()
const tempRow = ref<any>()
const router = useRouter()

interface Node {
  id: number | string
  label: string
}

const props = defineProps({
  templateNode: propTypes.oneOfType([Array<Node>]).isRequired,
  basicsId: propTypes.number.def(0),
  currentNode: propTypes.oneOfType([String]).isRequired,
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired
})

const formData = ref<ProblemVO>({})

const formRules = reactive<FormRules<ProblemVO>>({
  content: [{ required: true, message: '问题描述不能为空', trigger: 'blur' }],
  stage: [{ required: true, message: '问题所属阶段不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '问题等级不能为空', trigger: 'blur' }],
  category: [{ required: true, message: '问题分类不能为空', trigger: 'blur' }],
  module: [{ required: true, message: '问题模块不能为空', trigger: 'blur' }],
  proposingDepartment: [{ required: true, message: '问题提出部门不能为空', trigger: 'blur' }],
  director: [{ required: true, message: '问题责任人不能为空', trigger: 'blur' }],
  proposal: [{ required: true, message: '问题提出人不能为空', trigger: 'blur' }],
  timeOfProposal: [{ required: true, message: '问题提出时间不能为空', trigger: 'blur' }],
  timeOfPlan: [{ required: true, message: '问题计划完成时间不能为空', trigger: 'blur' }]
})

const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})
const attachmentList = ref<AttachmentRespVO[]>([])

const feedbackVisible = ref(false)
const feedbackForm = ref({
  id: undefined,
  reason: undefined,
  measures: undefined
})

const modifyInfo = reactive<BasicsModifyInfoVO[]>([])

const showModal = ref(false)

const allowToComplete = computed(() => {
  // 判断是否存在 approvalStatus === 0 的项
  return !attachmentList.value.some((item) => item.approvalStatus === 0)
})

const { getUser } = useUserStore()
const allowPermission = computed(() => {
  return (
    formData.value.director?.includes(getUser.id) || formData.value.coordinate?.includes(getUser.id)
  )
})

const onShare = async () => {
  loading.value = true
  try {
    message
      .confirm('确认修改当前问题的公开状态？')
      .then(async () => {
        await ProblemApi.updateProblemShare({
          problemId: formData.value.id,
          share: formData.value.share
        })
        message.success('修改成功')
        emits('submit:success')
      })
      .catch(() => {
        formData.value.share = !formData.value.share
      })
  } finally {
    loading.value = false
  }
}

const onComplete = async () => {
  loading.value = true
  try {
    if (!formData.value.reason || !formData.value.measures) {
      message.alertError('请填写原因分析/解决措施')
      return
    }

    if (attachmentList.value?.length === 0) {
      message.alertError('请输出您的问题完成佐证材料。')
      return
    }
    await message.confirm('确认当前问题已解决?')
    await ProblemFlowApi.completeProblem({
      ...formData.value,
      problemId: formData.value.id,
      modifyInfo
    })
    message.success('成功')
    edit.value = false
    emits('submit:success')
    const res = await ProblemApi.getProblem(formData.value.id!)
    formData.value = cloneDeep(res)
    tempRow.value = res
    modifyInfo.length = 0
  } finally {
    loading.value = false
  }
}

/** 删除活动 */
const onDelete = async () => {
  loading.value = true
  try {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入删除原因', '删除问题', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '删除原因不能为空'
    })
    modifyInfo.push({
      modifyField: 'delete',
      modifyFieldName: '问题删除审批处理,原因：' + value,
      beforeValue: '',
      afterValue: ''
    })
    await ProblemFlowApi.deleteProblem({
      ...formData.value,
      problemId: formData.value.id,
      modifyInfo
    })
    message.success('成功')
    edit.value = false
    emits('submit:success')
    dialogVisible.value = false
    refresh()
  } finally {
    loading.value = false
  }
}

/** 修改活动 */
const onModify = async () => {
  loading.value = true
  try {
    if (!edit.value) {
      edit.value = true
      currentTab.value = 'problem'
      return
    } else {
      if (!formData.value.proposal || formData.value.proposal.length === 0) {
        message.alertError('请先选择提出人')
        return
      }
      // 二次确认
      const { value } = await ElMessageBox.prompt('请输入变更原因', '问题', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
        inputErrorMessage: '修改原因不能为空'
      })
      modifyInfo.push({
        modifyField: 'update',
        modifyFieldName: '问题修改原因：' + value,
        beforeValue: ''
      })
      await ProblemFlowApi.updateProblem({
        ...formData.value,
        problemId: formData.value.id,
        modifyInfo
      })
      message.success('成功')
      edit.value = false
      emits('submit:success')
      const res = await ProblemApi.getProblem(formData.value.id!)
      formData.value = cloneDeep(res)
      tempRow.value = res
      modifyInfo.length = 0
    }
  } finally {
    loading.value = false
  }
}

/** 菜单切换 */
const onTabChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'problem':
      commentRef.value?.listEvent(formData.value.id)
      break
    case 'target':
      onListAttachment()
      break
    case 'log':
      logFormRef.value?.refreshLog()
      break
  }
}

/** 显示意见反馈表单 */
const onShowFeedbackForm = () => {
  feedbackVisible.value = true
  feedbackForm.value.id = formData.value.id as undefined
  feedbackForm.value.reason = formData.value.reason as undefined
  feedbackForm.value.measures = formData.value.measures as undefined
}
/** 提交意见反馈 */
const onSubmitFeedback = async () => {
  await ProblemApi.updateProblemFeedback(feedbackForm.value)
  message.success('保存成功')
  feedbackVisible.value = false
  emits('submit:success')
  const res = await ProblemApi.getProblem(formData.value.id!)
  formData.value = cloneDeep(res)
  tempRow.value = res
  modifyInfo.length = 0
}

/** 删除附件 */
const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'problem',
    dynamicId: formData.value.id
  })
  attachmentList.value = res
}

/** 发送评论 */
const onSaveComment = async () => {
  if (typeof commentData.value.imgs == 'string') {
    if (!commentData.value.imgs) {
      commentData.value.imgs = []
    } else {
      commentData.value.imgs = [commentData.value.imgs]
    }
  }
  const comment: CommentSaveReqVO = {
    moduleId: 'problem' + formData.value.id,
    content: commentData.value.content,
    imgUrls: commentData.value.imgs,
    parentId: -1,
    replyCommentId: -1
  }
  await CommentApi.createComment(comment)
  message.success('发送成功')
  commentData.value = {
    content: '',
    imgs: ''
  }
  commentRef.value?.listEvent(formData.value.id)
}

const open = async (title: string, row?: any) => {
  modifyInfo.length = 0
  dialogVisible.value = true
  currentTitle.value = title
  if (row) {
    edit.value = false
    formData.value = cloneDeep(row)
    tempRow.value = row
    await nextTick()
    commentRef.value?.listEvent(formData.value.id)
    onListAttachment()
  } else {
    edit.value = true
    refresh()
  }
}

const openRisk = async (title: string, row: any) => {
  showModal.value = true
  modifyInfo.length = 0
  dialogVisible.value = true
  currentTitle.value = title
  edit.value = true
  formData.value.content = row?.content
  formData.value.measures = row?.measures
  formData.value.stage = row?.stage
  formData.value.director = row?.director
}

const emits = defineEmits(['submit:success'])
/** 添加问题 */
const onSubmit = async (goOn: boolean) => {
  loading.value = true
  try {
    if (!formRef.value) return
    await unref(formRef).validate()
    formData.value.basicsId = props.basicsId
    formData.value.type = props.currentNode
    await ProblemFlowApi.createProblem(formData.value)
    refresh()
    emits('submit:success')
    message.success('保存成功')
    if (!goOn) dialogVisible.value = false
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    basicsId: undefined,
    content: undefined,
    imgIds: [],
    stage: undefined,
    level: undefined,
    category: undefined,
    module: undefined,
    proposingDepartment: undefined,
    reason: undefined,
    rejectRatio: undefined,
    measures: undefined,
    director: [],
    coordinate: [],
    timeOfProposal: undefined,
    timeOfPlan: undefined,
    timeOfActual: undefined,
    timeOfVerification: undefined,
    effect: undefined,
    effectPerson: undefined
  }
}

/** 将ga时间之后的时间禁用，同时将同行第一个时间之前的时间禁用 */
const disabledDate = (time: Date) => {
  return dateUtil().subtract(1, 'days').isAfter(time)
}

/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

defineExpose({
  open,
  openRisk
})
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  background-color: #fafafa !important;
  border: 0.3px dashed var(--el-color-info-light-5) !important;
  border-left: 5px solid var(--el-color-primary) !important;
  font-size: 1rem;
  height: 1.8rem;
}
</style>
