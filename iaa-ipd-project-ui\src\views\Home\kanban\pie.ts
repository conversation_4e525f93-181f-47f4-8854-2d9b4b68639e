const option = {
  title: {
    text: 'Referer of a Website',
    left: 'center',
    textStyle: {
      fontSize: '.9rem'
    }
  },
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    icon:'circle',
    itemHeight:5,
    itemGap: 10,
    textStyle:{
      fontSize: '.8rem',
      padding: [0, 0, 0, -8], // 修改文字和图标距离
    }
  },
  series: [
    {
      name: 'Access From',
      type: 'pie',
      radius: '50%',
      data: [] as any,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: function (data: any) {
          return `${data.name}
          [${data.percent.toFixed(1)}%]`
        },
        textStyle: {
          fontSize: '.8rem'
        }
      }
    }
  ],
  grid: {
    left: '10%', // 左边距
    right: '4%', // 右边距
    bottom: '3%', // 下边距
    containLabel: true // 包含刻度标签
  }
}

export const refreshChart = (data: any[], myChart: any, title: string) => {
  option.title.text = title
  option.series[0].name = title
  option.series[0].data = data
  option && myChart.setOption(option)
}
