<template>
  <div style="height: 3rem">
    <el-slider
      v-model="tempProcess"
      :step="1"
      :min="0"
      :max="100"
      :class="{ 'slider-process': tempProcess >= 3, 'completed-process': tempProcess >= 100 }"
      :disabled="true"
    />
    <div class="process-scale">
      <div
        v-for="(item, index) in marks"
        :key="index"
        :style="`text-align:center;position: absolute;left:${index}%;${item.style}`"
        class=""
      >
        <div class="slider__stop"></div>
        <div class="mt-1rem">{{ item.label }}</div>
        <div>{{ item.date }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { dateUtil } from '@/utils/dateUtil'
import { propTypes } from '@/utils/propTypes'
const props = defineProps({
  process: propTypes.number.def(5),
  keyNodeDate: propTypes.oneOfType<any[]>([]).def([]),
  createDate: propTypes.string.def('')
})
const tempProcess = ref(props.process) //组件内进度条，这么处理的原因是取消修改复位
/** 定义标记 */
interface Mark {
  style: string
  label: string
  date: string
}
type Marks = Record<number, Mark>
const marks = ref<Marks>({})

const initMarks = async () => {
  marks.value = {}
  marks.value[0.5] = {
    style: 'color:var(--secondary-text-color)',
    label: '创建',
    date: dateUtil(props.createDate).format('YYYY-MM-DD')
  }
  //均匀分割进度
  const segments = divideIntoSegments(props.keyNodeDate.length)
  let row = 0
  for (const key of segments) {
    const dateValue = props.keyNodeDate[row].endDate
    const endDate = dateUtil(props.keyNodeDate[row].endDate)
    let diffDays = 0
    if (props.keyNodeDate[row].progress < 100) {
      diffDays = dateUtil().diff(endDate, 'days')
    } else {
      diffDays = dateUtil(props.keyNodeDate[row].completedDate).diff(endDate, 'days')
    }
    marks.value[key] = {
      style: `color: ${[2, 4].includes(props.keyNodeDate[row].status) ? 'var(--el-color-danger)' : 'var(--secondary-text-color)'}`,
      label:
        cleanName(props.keyNodeDate[row].name) +
        (![2, 4].includes(props.keyNodeDate[row].status)
          ? ''
          : `${diffDays > 0 ? '延期' : '剩余'}${diffDays}天`),
      date: dateValue
    }
    row++
  }
}

// 修改方法为以下内容（移除中文保留逻辑）
const cleanName = (name: string): string => {
  if (name === '签发任务书') return name // 保留特殊处理
  return name.replace(/[^a-zA-Z0-9]/g, '') // 仅保留字母数字
}
function divideIntoSegments(n: number): number[] {
  // 计算每段长度
  const segmentLength = 100 / (n + 1)
  // 生成分割点数组
  const segments = [] as number[]
  for (let i = 1; i <= n; i++) {
    segments.push(Number((i * segmentLength).toFixed(0)))
  }
  return segments
}

//监听传入值的改变
watch(
  () => [props.process, props.keyNodeDate, props.createDate],
  () => {
    tempProcess.value = props.process
    initMarks()
  }
)
</script>

<style lang="scss" scoped>
.el-slider {
  height: 1rem;
}
:deep(.slider-process .el-slider__runway .el-slider__button-wrapper)::before {
  content: attr(aria-valuenow);
  font-size: 1rem;
  color: #fff;
  position: absolute;
  top: 0px;
  left: -28px;
  text-shadow:
    1px 1px 0 rgba(0, 0, 0, 0.1),
    -1px -1px 0 rgba(0, 0, 0, 0.1),
    1px -1px 0 rgba(0, 0, 0, 0.1),
    -1px 1px 0 rgba(0, 0, 0, 0.1),
    2px 2px 4px rgba(0, 0, 0, 0.2);
}
:deep(.el-slider__runway) {
  height: 1rem;
  border-radius: 2px;
  background-color: #ebeff3;
  // border: .3px solid #eee;
  // margin-top: -26px;

  .el-slider__bar {
    height: 1rem;
    background-color: var(--el-color-success-light-3);
    border-radius: 2px;
  }

  .el-slider__button-wrapper {
    height: 1rem;
    top: 0px;
    line-height: 1rem;
    width:1rem;
    right: 0;
    .el-slider__button {
      border-radius: 2px;
      width: 1rem;
      height: 1rem;
      border-color: var(--el-color-primary);
    }
  }
}

.completed-process{
  :deep(.el-slider__button-wrapper){
    left:99.5% !important;
  }
}
.process-scale {
  display: flex;
  text-align: center;
  position: relative;
  color: var(--secondary-text-color);
  font-size: 0.8rem;
  line-height: 1.2;
  margin-top: -1rem;
}

:deep(.slider__stop) {
  position: absolute;
  left: 50%; /* 水平居中 */
  border-radius: 0px;
  height: 1rem;
  width: 0.15vw;
  // border: 0.1px dashed #f1f1f1;
  background-color: #fff;
}
</style>
