import request from '@/config/axios'

// 项目文件模板分类 VO
export interface FileTemplateVO {
  id: number // 模板ID
  name: string // 模板名称
  uri: string // 模板路径
  remark: string // 模板备注
  currentVersion: string // 当前版本
  version: number // 版本号
  categoryId: number // 分类ID
  infraFileId: number // 文件ID
  hasFolder: boolean // 是否是文件夹
  createName: string // 创建人
  createTime: Date //创建时间
  updateName: string //创建人
  updateTime: Date //创建时间
  size: number // 文件大小
}

// 项目文件模板分类 API
export const FileTemplateApi = {
  // 查询项目文件模板分类分页
  getFileTemplatePage: async (params: any) => {
    return await request.get({ url: `/project/file-template/page`, params })
  },

  //批量上传文件模板
  uploadFileTemplate: async (data: any) => {
    return await request.post({ url: `/project/file-template/upload-file-template`, data })
  },

  // 查询文件模板的所有版本
  getFileTemplateAllVersion: async (id: number) => {
    return await request.get({ url: `/project/file-template/get-all-version/` + id })
  },
  // 查询单个文件模板
  getFileTemplate: async (id: number) => {
    return await request.get({ url: `/project/file-template/get/${id}` })
  },

  // 删除文件模板
  deleteFileTemplate: async (id: number) => {
    return await request.delete({ url: `/project/file-template/delete?id=` + id })
  },
  // 重命名文件
  fileTemplateRename: async (id: number, newName: string) => {
    return await request.post({ url: `/project/file-template/rename?id=${id}&newName=${newName}` })
  }
}
