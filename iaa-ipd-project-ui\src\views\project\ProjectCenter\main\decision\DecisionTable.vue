<template>
  <div class="table-container">
    <vxe-toolbar ref="toolbarRef" size="mini" custom>
      <template #buttons>
        <el-button
          type="primary"
          size="small"
          plain
          @click="decisionFormRef?.openForm('添加预审记录')"
          v-if="getVisiableUserList(props.basicsId).includes(getUser.id)"
        >
          添加评审问题
        </el-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      height="94%"
      :header-cell-style="{
        padding: '0',
        height: '2vw',
        fontSize: '1rem',
        backgroundColor: '#fafafa',
        color: 'var(--primary-text-color)'
      }"
      :row-style="{
        cursor: 'pointer'
      }"
      :cell-style="{
        padding: '0',
        height: '2vw',
        fontSize: '1rem',
        color: 'var(--primary-text-color)'
      }"
      round
      auto-resize
      border
      :row-config="{ drag: true, isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
      :column-config="{ resizable: true, isHover: true }"
      :data="props.decisionList"
      show-overflow
      ref="tableRef"
      @cell-click="(el: any) => decisionFormRef?.openForm('评审问题跟进', el.row)"
    >
      <vxe-column title="问题" field="problem" />
      <vxe-column title="负责人" field="director" width="240">
        <template #default="{ row }">
          <user-avatar-list
            v-model="row.director"
            :user-list="props.userList"
            :size="28"
            :limit="6"
            :add="false"
          />
        </template>
      </vxe-column>
      <vxe-column title="是自定义" field="hasCustom" width="80">
        <template #default="{ row }">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.hasCustom" />
        </template>
      </vxe-column>
    </vxe-table>
    <DecisionForm
      ref="decisionFormRef"
      :user-list="props.userList"
      :basics-id="props.basicsId"
      :category="props.category"
      @submit:success="emits('submit:success')"
    />
  </div>
</template>

<script lang="ts" setup>
import { DecisionVO } from '@/api/project/decision'
import { UserVO } from '@/api/system/user'
import { DICT_TYPE } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import DecisionForm from './DecisionForm.vue'
import { getVisiableUserList } from '../util/permission'
import { useUserStore } from '@/store/modules/user'

const { getUser } = useUserStore()

const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired,
  decisionList: propTypes.oneOfType<DecisionVO[]>([]).isRequired,
  category: propTypes.number.isRequired,
  basicsId: propTypes.number.isRequired
})
const decisionFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()

const emits = defineEmits(['submit:success'])

onMounted(() => {
  unref(tableRef).connect(unref(toolbarRef))
})
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  height: calc(100% - 5px);
  background-color: white;
  padding: 5px;
}

:deep(.vxe-toolbar) {
  height: 5%;

  .vxe-buttons--wrapper {
    & > * {
      height: 1.5vw;
      font-size: 0.6vw;
    }
  }

  .vxe-tools--operate {
    & > * {
      height: 1.5vw;
      width: 1.5vw !important;
      font-size: 0.6vw;
    }
  }
}

:deep(.vxe-cell) {
  height: 2.5rem !important;
}
</style>
