import request from '@/config/axios'

export const AttachmentDownloadFlowApi = {
  createAttachmentDownloadFlow: (data: any) => {
    return request.post({ url: '/bpm/attachment-download/create', data })
  },
  listAttachmentDownloadFlow: ()=>{
    return request.get({url:'/bpm/attachment-download/list'})
  },
  getAttachmentDownloadFlow: (id: string) => {
    return request.get({ url: `/bpm/attachment-download/get/${id}` })
  }
}
