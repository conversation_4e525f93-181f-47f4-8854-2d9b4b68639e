import request from '@/config/axios'

export const InstrumentApi = {
  /** 获取日历活动数量列表 */
  getCalendarCountList: (startDate: string, endDate: string, userId: number) => {
    return request.get({
      url: `/project/instrument/get-calendar-count-list?startDate=${startDate}&endDate=${endDate}&userId=${userId}`
    })
  },
  /** 获取日历待办 */
  getCalendarTodoList: (date: string, type: string, userId: number) => {
    return request.get({
      url: `/project/instrument/get-calendar-todo-list?date=${date}&type=${type}&userId=${userId}`
    })
  },
  /** 获取状态数量 */
  getStatusCountList: () => {
    return request.get({
      url: `/project/instrument/get-status-count-list`
    })
  }
}
