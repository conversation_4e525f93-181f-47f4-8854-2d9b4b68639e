import request from '@/config/axios'

export const InstrumentApi = {
  /** 获取日历活动数量列表 */
  getCalendarCountList: (startDate: string, endDate: string) => {
    return request.get({
      url: `/project/instrument/get-calendar-count-list?startDate=${startDate}&endDate=${endDate}`
    })
  },
  /** 获取日历待办 */
  getCalendarTodoList: (date: string,type:string) => {
    return request.get({
      url: `/project/instrument/get-calendar-todo-list?date=${date}&type=${type}`
    })
  },
  /** 获取状态数量 */
  getStatusCountList: () => {
    return request.get({
      url: `/project/instrument/get-status-count-list`
    })
  }
}
