<template>
  <NoModalDrawer v-model="visiable" title="计划总结" size="50%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">计划总结</div>
        <div v-if="formData.id && [0].includes(formData.status!)" class="flex">
          <el-tooltip content="修改问题">
            <el-button
              :loading="loading"
              link
              v-if="formData.creator?.includes(getUser.id)"
              @click="onEdit"
            >
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除问题">
            <el-button
              :loading="loading"
              link
              v-if="formData.creator?.includes(getUser.id)"
              @click="onDelete"
            >
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
      <el-collapse-item title="基本信息" name="1">
        <el-form class="custom-form" label-width="100px">
          <el-form-item label="所属日期范围">
            {{ formatToDate(formData.startDate!) }}-{{ formatToDate(formData.endDate!) }}
          </el-form-item>
          <el-form-item label="关注活动">
            <el-select v-model="formData.activitiesId!" :disabled="!edit">
              <el-option
                v-for="item in props.focusList"
                :key="item.id"
                :label="item.name"
                :value="item.id!"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="负责人">
            <UserAvatarList
              v-model="formData.director!"
              :user-list="props.userList"
              :add="edit"
              :size="28"
              :visiable-user-list="getVisiableUserList()"
            />
          </el-form-item>
          <el-form-item label="事项">
            <el-input type="textarea" v-model="formData.description" :rows="6" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="更新状态" v-if="formData.id && !edit">
            <el-radio-group
              v-model="formData.status"
              :disabled="!formData.director?.includes(getUser.id)"
              @change="update"
            >
              <el-radio-button
                v-for="dict in getIntDictOptions('project_summary_status')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="跟进记录" name="2" v-show="!edit">
        <Comment ref="commentRef" :at="false" :sharp="false" category="summary" />
      </el-collapse-item>
    </el-collapse>
    <template #footer v-if="edit">
      <el-button type="primary" @click="submit(false)" :loading="loading">保存</el-button>
      <el-button type="primary" @click="submit(true)" :loading="loading">保存并继续添加</el-button>
    </template>
    <template #footer v-else>
      <CommentInput
        v-model="commentData"
        placeholder="录入您的跟进记录，点击右下角纸飞机图标或回车发送"
        :user-list="props.userList"
        :sharp="false"
        @send="onSaveComment"
      />
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { ActivitiesVO } from '@/api/project/activities'
import { SummaryVO, SummaryApi } from '@/api/project/summary'
import { UserVO } from '@/api/system/user'
import { propTypes } from '@/utils/propTypes'
import { formatToDate } from '@/utils/dateUtil'
import { getVisiableUserList } from '../util/permission'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { getIntDictOptions } from '@/utils/dict'
dayjs.locale('zh-cn')

import { useUserStore } from '@/store/modules/user'
import { CommentVO } from '@/components/CommentInput/comment'
import { CommentSaveReqVO, CommentApi } from '@/api/infra/comment'

const { getUser } = useUserStore()

const visiable = ref(false)
const formData = ref<SummaryVO>({})
const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired,
  basicsId: propTypes.number.isRequired,
  focusList: propTypes.oneOfType<ActivitiesVO[]>([]).isRequired
})
const edit = ref(false)
const activeNames = ref(['1', '2'])
const loading = ref(false)
const message = useMessage()
const commentRef = ref()
const emits = defineEmits(['success'])
const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})

/** 发送评论 */
const onSaveComment = async () => {
  if (typeof commentData.value.imgs == 'string') {
    if (!commentData.value.imgs) {
      commentData.value.imgs = []
    } else {
      commentData.value.imgs = [commentData.value.imgs]
    }
  }
  const comment: CommentSaveReqVO = {
    moduleId: 'summary' + formData.value.id,
    content: commentData.value.content,
    imgUrls: commentData.value.imgs,
    parentId: -1,
    replyCommentId: -1
  }
  await CommentApi.createComment(comment)
  message.success('发送成功')
  commentData.value = {
    content: '',
    imgs: ''
  }
  commentRef.value?.listEvent(formData.value.id)
}
/** 提交 */
const submit = async (allow: boolean) => {
  loading.value = true
  try {
    await message.confirm('确定保存？')
    formData.value.basicsId = props.basicsId
    await SummaryApi.saveItem(formData.value)
    message.success('保存成功')
    refresh()
    if (!allow) {
      visiable.value = false
    }
    emits('success')
  } finally {
    loading.value = false
  }
}
const onEdit = async () => {
  if (!edit.value) {
    edit.value = true
    return
  }
  loading.value = true
  try {
    await message.confirm('确定修改事项内容？')
    await SummaryApi.saveItem(formData.value)
    message.success('更新成功')
    emits('success')
  } finally {
    loading.value = false
  }
}

const onDelete = async () => {
  loading.value = true
  try {
    await message.confirm('确定删除？')
    await SummaryApi.deleteItem(formData.value.id!)
    message.success('删除成功')
    refresh()
    visiable.value = false
    emits('success')
  } finally {
    loading.value = false
  }
}

/** 更新完成状态 */
const update = async () => {
  loading.value = true
  try {
    await message.confirm('确定事项已完成？')
    await SummaryApi.saveItem(formData.value)
    message.success('更新成功')
    emits('success')
  } finally {
    loading.value = false
  }
}
const openForm = async (date?: string, row?: any) => {
  visiable.value = true

  if (row) {
    formData.value = row
    edit.value = false
    await nextTick()
    commentRef.value?.listEvent(formData.value.id!)
  } else {
    refresh()
    edit.value = true
    formData.value.startDate = dayjs(date).startOf('week').format('YYYY-MM-DD')
    formData.value.endDate = dayjs(date).endOf('week').format('YYYY-MM-DD')
  }
}
const refresh = () => {
  formData.value = {
    id: undefined,
    basicsId: undefined,
    activitiesId: undefined,
    startDate: undefined,
    director: undefined,
    status: undefined,
    description: undefined
  }
}

defineExpose({
  openForm
})
</script>
