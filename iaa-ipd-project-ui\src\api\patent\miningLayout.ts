import request from '@/config/axios'

export interface MiningLayoutVO {
  id: any // 主键
  opportunityPoints: any
  proposer: any
  source: any
  models: any
  patentPoint: any
  patentLayout: any
  machineType: any
  component: any
  part: any
  area: any
  applicationNo: any
  protectionPoint: any
  attachmentIds: any
  inventor: any
  actualInventor: any
}

export const MiningLayoutApi = {
  /** 分页获取知知识产权情报 */
  getMiningLayoutPage: (data: any) => {
    return request.post({ url: '/patent/mining-layout/page', data })
  },
  /** 创建知识知识产权情报 */
  createMiningLayout: (data: any) => {
    return request.post({ url: '/patent/mining-layout/create', data })
  },
  /** 更新知识知识产权情报 */
  updateMiningLayout: (data: any) => {
    return request.post({ url: '/patent/mining-layout/update', data })
  },
  /** 删除知识知识产权情报 */
  deleteMiningLayout: (id: number) => {
    return request.get({ url: `/patent/mining-layout/delete/${id}` })
  },
  /** 查询知识产权情报详情 */
  getMiningLayout: async (id: any) => {
    return await request.get({ url: `/patent/mining-layout/get?id=` + id })
  }
}
