<template>
  <!-- 对话框(添加 / 修改) -->
  <NoModalDrawer :title="dialogTitle" v-model="visible" size="60%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ dialogTitle }}</div>
        <div class="flex" v-if="formType === 'edit'">
          <el-tooltip content="修改">
            <el-button link :loading="loading" @click="onModify()">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除">
            <el-button link :loading="loading" @click="handleDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="120px"
      :disabled="!edit"
    >
      <el-row>
        <el-col :span="24">
          <CardTitle title="机会信息" />
        </el-col>
        <el-col :span="24">
          <el-form-item label="创新点/机会点" prop="opportunityPoints">
            <el-input type="textarea" :rows="4" v-model="formData.opportunityPoints" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="提出人" prop="proposer">
            <el-input v-model="formData.proposer" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="来源" prop="source">
            <el-input v-model="formData.source" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="申请号" prop="applicationNo">
            <el-input v-model="formData.applicationNo" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="可应用的型号" prop="models">
            <TagsInput v-model="formData.models" :disabled="!edit" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form :model="formData" :rules="formRules" v-loading="loading" label-width="120px">
      <el-tabs v-model="currentTab" @tab-change="onTableChange">
        <el-tab-pane label="创新拓展" name="innovation">
          <el-form-item label="创新技术点" prop="patentPoint">
            <el-input
              type="textarea"
              :rows="4"
              v-model="formData.technologyPoints"
              :disabled="!edit"
            />
          </el-form-item>
          <el-form-item label="拓展的专利点" prop="patentPoint">
            <el-input type="textarea" :rows="4" v-model="formData.patentPoint" :disabled="!edit" />
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="保护范围" name="protection">
          <el-form-item label="保护整机类型" prop="machineType">
            <TagsInput v-model="formData.machineType" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="组件" prop="component">
            <TagsInput v-model="formData.component" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="零件" prop="part">
            <TagsInput v-model="formData.part" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="保护点" prop="protectionPoint">
            <TagsInput v-model="formData.protectionPoint" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="保护的地区" prop="area">
            <el-input v-model="formData.area" :disabled="!edit" />
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="专利布局" name="patent_layout">
          <el-form-item label="专利布局" prop="patentLayout">
            <el-input type="textarea" :rows="6" v-model="formData.patentLayout" :disabled="!edit" />
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="发明人" name="inventor">
          <InventorInfo
            ref="inventorInfoRef"
            :table-type="3"
            :belongs-id="formData.id"
            :form-type="formType"
          />
          <AgencyInfo
            ref="agencyInfoRef"
            :table-type="3"
            :belongs-id="formData.id"
            :form-type="formType"
          />
        </el-tab-pane>
        <el-tab-pane label="附图" name="imgs">
          <UploadImgs v-model="formData.imgs!" height="80px" width="80px" :disabled="!edit" />
        </el-tab-pane>
        <el-tab-pane label="附件" name="attachment">
          <AttachmentInfo
            ref="attachmentInfoRef"
            :table-type="3"
            :belongs-id="formData.id"
            :form-type="formType"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer v-if="formType === 'create'">
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { MiningLayoutApi } from '@/api/patent/miningLayout'
import TagsInput from '../components/TagsInput.vue'
import AgencyInfo from '../components/AgencyInfo.vue'
import InventorInfo from '../components/InventorInfo.vue'
import AttachmentInfo from '../components/AttachmentInfo.vue'

const inventorInfoRef = ref()
const agencyInfoRef = ref()
const attachmentInfoRef = ref()

const currentTab = ref('innovation')

const edit = ref(false)
const visible = ref(false)
const formData = ref({
  id: undefined,
  opportunityPoints: undefined,
  proposer: undefined,
  source: undefined,
  models: [] as string[],
  technologyPoints: undefined,
  patentPoint: undefined,
  patentLayout: undefined,
  machineType: [] as string[],
  component: [] as string[],
  part: [] as string[],
  area: undefined,
  applicationNo: undefined,
  protectionPoint: [] as string[],
  imgs: [] as string[]
})
const formRules = reactive({
  opportunityPoints: [{ required: true, message: '创新点或机会点', trigger: 'blur' }],
  proposer: [{ required: true, message: '请输入提出人', trigger: 'blur' }],
  applicationNo: [{ required: true, message: '请输入申请号', trigger: 'blur' }],
  models: [{ required: true, message: '请输入可应用的型号', trigger: 'blur' }],
  patentPoint: [{ required: true, message: '请输入可拓展的专利点', trigger: 'blur' }],
  area: [{ required: true, message: '请输入保护的地区', trigger: 'blur' }],
  protectionPoint: [{ required: true, message: '请输入保护点', trigger: 'blur' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const openForm = async (rows?: any[] | any) => {
  visible.value = true
  if (rows) {
    formType.value = 'edit'
    dialogTitle.value = '修改知识产权挖掘与布局'
    const data = await MiningLayoutApi.getMiningLayout(rows)
    formData.value = data
    edit.value = false
    onTableChange()
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加知识产权挖掘与布局'
    resetForm()
    edit.value = true
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    opportunityPoints: undefined,
    proposer: undefined,
    source: undefined,
    models: [] as string[],
    technologyPoints: undefined,
    patentPoint: undefined,
    patentLayout: undefined,
    machineType: [] as string[],
    component: [] as string[],
    part: [] as string[],
    area: undefined,
    applicationNo: undefined,
    protectionPoint: [] as string[],
    imgs: [] as string[]
  }
}
const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    const inventors = inventorInfoRef.value?.getData()
    const agencys = agencyInfoRef.value?.getData()
    const attachments = attachmentInfoRef.value?.getData()
    await MiningLayoutApi.createMiningLayout({
      ...formData.value,
      inventors,
      agencys,
      attachments
    })
    message.success('创建成功')
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

const onModify = async () => {
  if (!edit.value) {
    edit.value = true
    return
  } else {
    await MiningLayoutApi.updateMiningLayout(formData.value)
    message.success('修改成功')
    emit('success')
    edit.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async () => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await MiningLayoutApi.deleteMiningLayout(formData.value.id!)
  message.success('删除成功')
  emit('success')
}

/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'attachment':
      attachmentInfoRef.value?.onList()
      break
    case 'inventor':
      inventorInfoRef.value?.onList()
      agencyInfoRef.value?.onList()
      break
  }
}

defineExpose({
  openForm
})
</script>
<style lang="scss" scoped>
:deep(.el-tabs__header.is-top) {
  background-color: #f1f1f1;
}
:deep(.card-title) {
  padding: 5px;
  display: block;
}
</style>
