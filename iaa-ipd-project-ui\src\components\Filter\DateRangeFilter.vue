<template>
  <div v-if="currOption" class="p-14px">
    <div>
      <el-radio-group v-model="currOption.data.type" @change="changeOptionEvent()">
        <el-radio label="本月" value="currentMonth" />
        <el-radio label="本周" value="currentWeek" />
        <el-radio label="指定范围" value="range" />
      </el-radio-group>
    </div>
    <div class="flex" v-if="currOption.data?.type === 'range'">
      <el-date-picker
        v-model="currOption.data.startDate"
        value-format="YYYY-MM-DD"
        placeholder="开始时间"
        size="small"
        class="!w-120px"
      />
      -
      <el-date-picker
        v-model="currOption.data.endDate"
        value-format="YYYY-MM-DD"
        placeholder="结束时间"
        size="small"
        class="!w-120px"
      />
    </div>
    <div>
      <el-checkbox
        label="只筛选变更历史日期"
        v-model="currOption.data.history"
        @change="changeOptionEvent()"
      />
    </div>
    <div class="my-fc-footer">
      <el-button type="primary" plain @click="confirmEvent" size="small">筛选</el-button>
      <el-button @click="resetEvent" size="small">重置</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import type { VxeTableDefines } from 'vxe-table'

const props = defineProps({
  renderParams: propTypes.any.def({})
})

const currOption = ref<VxeTableDefines.FilterOption>()

const currField = computed(() => {
  const { column } = props.renderParams || {}
  return column ? column.field : ''
})

const load = () => {
  const { renderParams } = props
  if (renderParams) {
    const { column } = renderParams
    const option = column.filters[0]
    currOption.value = option
    currOption.value!.data.type = 'currentMonth'
    currOption.value!.data.startDate = undefined
    currOption.value!.data.endDate = undefined
    currOption.value!.data.history = false
  }
}

const changeOptionEvent = () => {
  const { renderParams } = props
  const option = currOption.value
  if (renderParams && option) {
    const { $table } = renderParams
    const checked = !!option.data
    $table.updateFilterOptionStatus(option, checked)
  }
}

const confirmEvent = ($event) => {
  changeOptionEvent()
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.confirmFilterEvent($event)
  }
}

const resetEvent = () => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.clearFilter()
  }
}

watch(currField, () => {
  load()
})

load()
</script>
