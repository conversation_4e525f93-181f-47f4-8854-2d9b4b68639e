<template>
  <div class="p-l-10px h-full overflow-auto" ref="containerRef">
    <!-- ADCP 按期达成 -->
    <el-card shadow="never">
      <template #header>
        <!-- 搜索工作栏 -->
        <el-form
          class="-mb-15px"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="100px"
          size="small"
        >
          <el-form-item label="发起人" prop="startUserId">
            <el-select
              v-model="queryParams.startUserId"
              placeholder="请选择发起人"
              class="!w-120px"
              filterable
              clearable
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.nickname"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="流程名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入流程名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-240px"
            />
          </el-form-item>
          <el-form-item label="流程状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择流程状态"
              clearable
              class="!w-120px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button type="warning" @click="handleExport" plain :loading="exportLoading"
              >导出</el-button
            >
          </el-form-item>
        </el-form>
      </template>

      <!-- 列表 -->
      <div class="h-[calc(100vh-150px)]">
        <div class="h-[calc(100%-52px)]">
          <vxe-table
            :loading="loading"
            :data="list"
            :column-config="{ resizable: true }"
            :cell-style="{ fontSize: '0.9rem' }"
            show-overflow
            height="100%"
            border
            stripe
            :header-cell-config="{ height: 40 }"
            :cell-config="{ height: 40 }"
          >
            <vxe-column title="流程名称" align="center" field="name" min-width="250px" fixed="left">
              <template #default="{ row }">
                <el-link type="primary" @click="handleDetail(row)">
                  {{ row.formVariables?.['approve_title'] || row.name }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              title="流程分类"
              align="center"
              field="categoryName"
              min-width="100"
              fixed="left"
            >
              <template #default="{ row }">
                {{ row.formVariables?.['typeName'] || row.categoryName }}
              </template>
            </vxe-column>
            <vxe-column title="流程发起人" align="center" field="startUser.nickname" width="120" />
            <vxe-column title="发起部门" align="center" field="startUser.deptName" width="120" />
            <vxe-column title="流程状态" align="center" field="status" width="100">
              <template #default="scope">
                <dict-tag :type="DICT_TYPE.BPM_PROCESS_INSTANCE_STATUS" :value="scope.row.status" />
              </template>
            </vxe-column>
            <vxe-column title="发起时间" align="center" field="startTime" width="180">
              <template #default="{ row }">
                {{ formatToDateTime(row.startTime) }}
              </template>
            </vxe-column>
            <vxe-column title="结束时间" align="center" field="endTime" width="180">
              <template #default="{ row }">
                {{ formatToDateTime(row.endTime) }}
              </template>
            </vxe-column>
            <vxe-column align="center" title="耗时" field="durationInMillis" width="80">
              <template #default="scope">
                {{ scope.row.durationInMillis > 0 ? formatPast2(scope.row.durationInMillis) : '-' }}
              </template>
            </vxe-column>
            <vxe-column title="当前审批任务" align="center" field="tasks" min-width="120px">
              <template #default="scope">
                <el-button type="primary" v-for="task in scope.row.tasks" :key="task.id" link>
                  <span>{{ task.name }}</span>
                </el-button>
              </template>
            </vxe-column>
            <vxe-column title="变更内容" align="left" field="content" width="200">
              <template #default="{ row }">
                {{ row.formVariables?.['content'] }}
              </template>
            </vxe-column>
            <vxe-column title="变更原因" align="left" field="reason" width="200">
              <template #default="{ row }">
                {{ row.formVariables?.['reason'] }}
              </template>
            </vxe-column>
            <vxe-column title="流程编号" align="center" field="id" min-width="320px" />
          </vxe-table>
        </div>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          size="small"
        />
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { formatPast2 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { CategoryApi } from '@/api/bpm/category'
import * as UserApi from '@/api/system/user'
import { formatToDateTime } from '@/utils/dateUtil'
import download from '@/utils/download'

// 它和【我的流程】的差异是，该菜单可以看全部的流程实例
defineOptions({ name: 'BpmProcessInstanceManager' })

const router = useRouter() // 路由
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  startUserId: undefined,
  name: '',
  processDefinitionId: undefined,
  category: undefined,
  status: undefined,
  variables: {
    event: ['modify', 'delete']
  },
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const categoryList = ref<any[]>([]) // 流程分类列表
const userList = ref<any[]>([]) // 用户列表
const exportLoading = ref(false)

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProcessInstanceApi.getProcessInstanceManagerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProcessInstanceApi.exportManager(queryParams)
    download.excel(data, '流程实例.xlsx')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查看详情 */
const handleDetail = (row) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.id
    }
  })
}

/** 取消按钮操作 */
const handleCancel = async (row) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: t('common.ok'),
    cancelButtonText: t('common.cancel'),
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '取消原因不能为空'
  })
  // 发起取消
  await ProcessInstanceApi.cancelProcessInstanceByAdmin(row.id, value)
  message.success('取消成功')
  // 刷新列表
  await getList()
}

/** 激活时 **/
onActivated(() => {
  getList()
})

/** 初始化 **/
onMounted(async () => {
  await getList()
  categoryList.value = await CategoryApi.getCategorySimpleList()
  userList.value = await UserApi.getSimpleUserList()
})
</script>
