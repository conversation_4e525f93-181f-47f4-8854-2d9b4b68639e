<template>
  <ContentWrap>
    <el-tabs v-model="currentTab" @tab-change="tabChange">
      <el-tab-pane label="项目" name="project" />
      <el-tab-pane label="活动" name="activities" />
      <el-tab-pane label="问题" name="problem" />
      <el-tab-pane label="风险" name="risk" />
      <el-tab-pane label="技术评审" name="technical" />
      <el-tab-pane label="工时" name="workinghours" />
    </el-tabs>
    <div class="h-[calc(100vh-200px)] w-full flex">
      <vxe-split class="wh-full" v-loading="loading" ref="splitRef">
        <ProjectExport v-if="currentTab === 'project'" />
        <ActivitiesExport v-else-if="currentTab === 'activities'" />
        <ProblemExport v-else-if="currentTab === 'problem'" />
        <RiskExport v-else-if="currentTab === 'risk'" />
        <TechnicalExport v-else-if="currentTab === 'technical'" />
        <WorkingHours v-else-if="currentTab === 'workinghours'" />
        <vxe-split-pane v-else>
          <el-empty description="开发中" />
        </vxe-split-pane>
      </vxe-split>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import ProjectExport from './components/ProjectExport.vue'
import ActivitiesExport from './components/ActivitiesExport.vue'
import WorkingHours from './components/WorkingHours.vue'
import ProblemExport from './components/ProblemExport.vue'
import RiskExport from './components/RiskExport.vue'
import TechnicalExport from './components/TechnicalExport.vue'

const currentTab = ref('activities')
const loading = ref(false)
const splitRef = ref()

const tabChange = (tab: string) => {
  loading.value = true
  currentTab.value = ''
  setTimeout(() => {
    splitRef.value && splitRef.value.reset()
    splitRef.value && splitRef.value.recalculate()
    currentTab.value = tab
    loading.value = false
  }, 500)
}
</script>

<style lang="scss" scoped></style>
