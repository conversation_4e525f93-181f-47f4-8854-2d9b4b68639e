<template>
  <NoModalDrawer v-model="visiable" :title="currentTitle" size="50%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ currentTitle }}</div>
        <div>
          <el-tooltip
            content="修改问题"
            v-if="formData.id && formData.hasCustom && formData.creator?.includes(getUser.id)"
          >
            <el-button :loading="loading" link @click="onModify">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip
            content="删除问题"
            v-if="formData.id && formData.hasCustom && formData.creator?.includes(getUser.id)"
          >
            <el-button :loading="loading" link @click="onDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <template #default>
      <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
        <el-collapse-item title="基础信息" name="1">
          <el-form
            label-width="100px"
            ref="formRef"
            class="custom-form"
            :rules="formRules"
            :model="formData"
          >
            <el-form-item label="问题" prop="problem">
              <el-input
                v-model="formData.problem"
                type="textarea"
                :rows="6"
                placeholder="请输入问题"
                :disabled="!edit"
              />
            </el-form-item>
            <el-form-item label="负责人" prop="director">
              <user-avatar-list
                v-model="formData.director!"
                :user-list="props.userList"
                :size="28"
                :limit="6"
                :add="edit"
                :visiable-user-list="getVisiableUserList()"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="问题反馈" name="2" v-show="!edit">
          <Comment ref="commentRef" category="decision" :limit="5" bgColor="#fff" />
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #footer v-if="edit && !formData.id">
      <el-button type="primary" @click="onSumbit(false)">保存</el-button>
      <el-button type="primary" @click="onSumbit(true)">保存并继续添加</el-button>
    </template>
    <template #footer v-else-if="!edit">
      <CommentInput
        v-model="commentData"
        placeholder="录入您的跟进记录，点击右下角纸飞机图标或回车发送"
        :user-list="props.userList"
        :sharp="false"
        @send="onSaveComment"
      />
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { CommentApi, CommentSaveReqVO } from '@/api/infra/comment'
import { DecisionVO, DecisionApi } from '@/api/project/decision'
import { UserVO } from '@/api/system/user'
import { CommentVO } from '@/components/CommentInput/comment'
import { propTypes } from '@/utils/propTypes'
import { ElMessageBox } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import { getVisiableUserList } from '../util/permission'
import { useUserStore } from '@/store/modules/user'

const visiable = ref(false)
const currentTitle = ref('')
const formData = ref<DecisionVO>({})
const formRef = ref()
const activeNames = ref(['1', '2'])
const message = useMessage()
const commentRef = ref()
const loading = ref(false)
const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired,
  basicsId: propTypes.number.isRequired,
  category: propTypes.number.isRequired
})
const edit = ref(true)
const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})

const formRules = reactive({
  problem: [
    {
      required: true,
      message: '请输入问题',
      trigger: 'blur'
    }
  ],
  director: [
    {
      required: true,
      message: '请选择负责人',
      trigger: 'blur'
    }
  ]
})

const emits = defineEmits(['submit:success'])

const { getUser } = useUserStore()

/** 删除 */
const onDelete = async () => {
  loading.value = true
  try {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入删除原因', '删除问题', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '删除原因不能为空'
    })
    await DecisionApi.deleteDecision({
      id: formData.value.id,
      reson: value
    })
    message.success('成功')
    emits('submit:success')
    refresh()
    visiable.value = false
  } finally {
    loading.value = false
  }
}
/** 修改问题 */
const onModify = async () => {
  loading.value = true
  try {
    if (!edit.value) {
      edit.value = true
      return
    }
    await unref(formRef).validate()
    await DecisionApi.updateDecision(formData.value)
    message.success('成功')
    emits('submit:success')
    refresh()
    visiable.value = false
  } finally {
    loading.value = false
  }
}

/** 提交问题 */
const onSumbit = async (goOn?: boolean) => {
  await unref(formRef)?.validate()
  formData.value.basicsId = props.basicsId
  formData.value.category = props.category
  formData.value.hasCustom = true
  await DecisionApi.createDecision(formData.value)
  message.success('保存成功')
  if (goOn) {
    refresh()
    emits('submit:success')
  } else {
    visiable.value = false
    refresh()
    emits('submit:success')
  }
}

/** 发送评论 */
const onSaveComment = async () => {
  if (typeof commentData.value.imgs == 'string') {
    if (!commentData.value.imgs) {
      commentData.value.imgs = []
    } else {
      commentData.value.imgs = [commentData.value.imgs]
    }
  }
  const comment: CommentSaveReqVO = {
    moduleId: 'decision' + formData.value.id,
    content: commentData.value.content,
    imgUrls: commentData.value.imgs,
    parentId: -1,
    replyCommentId: -1
  }
  await CommentApi.createComment(comment)
  message.success('发送成功')
  commentData.value = {
    content: '',
    imgs: ''
  }
  commentRef.value?.listEvent(formData.value.id)
}
const openForm = async (title: string, row?: any) => {
  currentTitle.value = title
  if (row) {
    formData.value = cloneDeep(row)
    edit.value = false
    await nextTick()
    commentRef.value?.listEvent(formData.value.id)
  } else {
    refresh()
    edit.value = true
  }
  visiable.value = true
}

const refresh = () => {
  formData.value = {
    id: undefined,
    basicsId: undefined,
    category: undefined,
    problem: undefined
  }
}
defineExpose({
  openForm
})
</script>
