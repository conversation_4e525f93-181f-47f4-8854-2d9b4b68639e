<template>
  <NoModalDrawer v-model="visiable" id="risk" :title="currentTitle" size="50%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ currentTitle }}</div>
        <div v-if="formData.id && ![2, 10].includes(formData.status!)" class="flex">
          <el-tooltip content="修改问题">
            <el-button
              :loading="loading"
              link
              @click="onModify"
              v-if="hasPermission('risk_modify') || formData.creator?.includes(getUser.id)"
            >
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除问题">
            <el-button
              :loading="loading"
              link
              @click="onDelete"
              v-if="hasPermission('risk_delete') || formData.creator?.includes(getUser.id)"
            >
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <template #default>
      <el-alert
        show-icon
        type="info"
        description="据C-IPD经营秘籍规定,当风险为已发生时，风险将转换为问题管理并在风险管理中归档（不可修改）"
        :closable="false"
        v-if="!edit && formData.status == 2 && editSection == false"
      />
      <el-alert
        v-if="formData.status === 10"
        type="warning"
        show-icon
        description="当前问题有相关流程审批中"
        close-text="查看流程"
        @close="toBpm(formData.processInstanceId!)"
      />
      <el-tabs
        v-model="currentTab"
        class="drawer-tabs position-sticky top-0 z-10 bg-#fff"
        @tab-change="onTabChange"
      >
        <el-tab-pane name="risk" label="风险" />
        <el-tab-pane name="target" label="输出物" v-if="!edit" />
        <el-tab-pane name="log" label="日志" v-if="!edit" />
      </el-tabs>
      <template v-if="currentTab === 'risk'">
        <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
          <el-collapse-item title="基础信息" name="1">
            <el-form
              label-width="100px"
              ref="formRef"
              class="custom-form"
              :rules="formRules"
              :model="formData"
            >
              <el-form-item label="风险分类" prop="category">
                <el-select
                  v-model="formData.category"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'category',
                      '风险分类',
                      formData.category!,
                      tempRow.category,
                      modifyInfo
                    )
                  "
                >
                  <el-option-group
                    v-for="group in RiskCategory"
                    :key="group.value"
                    :label="group.label"
                  >
                    <el-option
                      v-for="item in group.children"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                      <div
                        :class="{
                          'text-[var(--el-color-primary)]':
                            item.light === props.stageIndex || item.light == -1,
                          'p-l-10px': true
                        }"
                      >
                        {{ item.label }}
                      </div>
                    </el-option>
                    <div style="border: 0.3px dashed #f1f1f1"></div>
                  </el-option-group>
                </el-select>
              </el-form-item>
              <el-form-item label="描述" prop="content">
                <el-input
                  type="textarea"
                  v-model="formData.content"
                  :rows="5"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'content',
                      '描述',
                      formData.content!,
                      tempRow.content,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-form-item label="预计后果" prop="consequence">
                <el-input
                  type="textarea"
                  v-model="formData.consequence"
                  :rows="4"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'consequence',
                      '预计后果',
                      formData.consequence!,
                      tempRow.consequence,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="影响" prop="affect">
                    <el-select
                      v-model="formData.affect"
                      :disabled="!edit"
                      @change="
                        formDataChangeString(
                          'affect',
                          '影响',
                          formData.affect!,
                          tempRow.affect,
                          modifyInfo
                        )
                      "
                    >
                      <el-option
                        v-for="dict in getStrDictOptions('project_risk_level')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="概率" prop="probability">
                    <el-select
                      v-model="formData.probability"
                      :disabled="!edit"
                      @change="
                        formDataChangeString(
                          'probability',
                          '概率',
                          formData.probability!,
                          tempRow.probability,
                          modifyInfo
                        )
                      "
                    >
                      <el-option
                        v-for="dict in getStrDictOptions('project_risk_level')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="等级" prop="level">
                    <el-select
                      v-model="formData.level"
                      :disabled="!edit"
                      @change="
                        formDataChangeString(
                          'riskLevel',
                          '等级',
                          formData.level!,
                          tempRow.level,
                          modifyInfo
                        )
                      "
                    >
                      <el-option
                        v-for="dict in getStrDictOptions('project_risk_level')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="负责人" prop="director">
                    <UserAvatarList
                      v-model="formData.director!"
                      :user-list="props.userList"
                      :add="edit"
                      :size="28"
                      :limit="5"
                      :visiable-user-list="getVisiableUserList()"
                      @change:msg="
                        formDataChangeArray(
                          'director',
                          '负责人',
                          formData.director!,
                          tempRow.director!,
                          modifyInfo
                        )
                      "
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="提出人" v-if="!edit">
                    <UserAvatarList
                      v-model="formData.creator!"
                      :user-list="props.userList"
                      :add="false"
                      :size="28"
                      :limit="5"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="状态" prop="status">
                    <el-select
                      v-model="formData.status"
                      :disabled="!edit && !editSection"
                      v-if="formData.status !== 10"
                      @change="
                        formDataChangeString(
                          'status',
                          '状态',
                          String(formData.status!),
                          tempRow.status,
                          modifyInfo
                        )
                      "
                    >
                      <el-option
                        v-for="dict in getIntDictOptions('project_risk_status')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        v-show="![10, 2].includes(dict.value)"
                      />
                    </el-select>
                    <DictTag v-else type="project_risk_status" :value="formData.status" />
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="!edit">
                  <el-form-item label="发生时间">
                    <el-date-picker
                      v-model="formData.onsetDate"
                      type="date"
                      :disabled="!edit"
                      class="!w-full"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="计划应对措施">
                <el-input
                  type="textarea"
                  v-model="formData.solutionsPlan"
                  :rows="4"
                  :disabled="!edit && !editSection"
                  @change="
                    formDataChangeString(
                      'solutionsPlan',
                      '计划应对措施',
                      formData.solutionsPlan!,
                      tempRow.solutionsPlan,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-form-item label="实际措施" prop="solutionsActual">
                <el-input
                  type="textarea"
                  v-model="formData.solutionsActual"
                  :rows="4"
                  :disabled="!edit && !editSection"
                  @change="
                    formDataChangeString(
                      'solutionsActual',
                      '实际措施',
                      formData.solutionsActual!,
                      tempRow.solutionsActual,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
            </el-form>
            <el-button
              type="warning"
              plain
              class="!w-full"
              v-if="!edit && ![2, 10].includes(formData.status!)"
              @click="onEditSection"
            >
              {{ editSection ? '保存' : '修改状态及填写措施' }}
            </el-button>
          </el-collapse-item>
          <el-collapse-item title="跟进记录" name="2" v-show="!edit">
            <Comment ref="commentRef" category="risk" :limit="5" bgColor="#fff" />
          </el-collapse-item>
        </el-collapse>
      </template>
      <template v-else-if="currentTab === 'target'">
        <vxe-table
          class="w-100%"
          :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
          :cell-style="{ padding: '5px', height: '30px' }"
          show-overflow
          :data="attachmentList"
          align="center"
          border
        >
          <vxe-column title="文件名" field="name" min-width="200" align="left">
            <template #default="{ row }">
              <el-link
                type="primary"
                @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
              >
                {{ row.name }}
              </el-link>
            </template>
          </vxe-column>
          <vxe-column title="版本" field="currentVersion" width="60" />
          <vxe-column title="审签状态" field="approvalStatus" width="90">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column
            title="审核通过时间"
            field="approvalTime"
            :formatter="dateFormatter3"
            width="120"
          />
          <vxe-column title="操作" width="100px" fixed="right" align="center">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                v-if="![1, 0].includes(row.approvalStatus) && row.creator === getUser.id"
                @click="fileUploadAnewRef?.openForm(row)"
              >
                重传
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                v-if="row.approvalStatus != 1 && row.creator === getUser.id"
                @click="delAttachment(row)"
              >
                删除
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </template>
      <template v-else-if="currentTab === 'log'">
        <LogForm
          ref="logFormRef"
          category="risk"
          :base-id="formData.id"
          :user-list="props.userList"
        />
      </template>
    </template>
    <template #footer v-if="edit && !formData.id">
      <el-button :loading="loading" type="primary" @click="onSubmit(false)">保存</el-button>
      <el-button :loading="loading" type="primary" @click="onSubmit(true)"
        >保存并继续添加</el-button
      >
    </template>
    <template #footer v-else-if="['risk', 'target'].includes(currentTab) && !edit">
      <CommentInput
        v-model="commentData"
        placeholder="录入您的跟进记录，点击右下角纸飞机图标或回车发送"
        :user-list="props.userList"
        :sharp="false"
        @send="onSaveComment"
        v-if="currentTab == 'risk'"
      />
      <FileUpload
        v-if="currentTab == 'target' && ![10, 2].includes(formData.status!) && allowPermission"
        ref="fileUploadRef"
        category="risk"
        :dynamic-id="formData.id"
        @success="onListAttachment"
      />
    </template>
  </NoModalDrawer>
  <AttachmentPreview ref="attachmentPreviewRef" />
  <FileUploadAnew
    category="risk"
    :dynamic-id="formData.id!"
    ref="fileUploadAnewRef"
    @success="onListAttachment"
  />
</template>

<script lang="ts" setup>
import { RiskApi, RiskVO } from '@/api/project/risk'
import { propTypes } from '@/utils/propTypes'
import { RiskCategory } from './util'
import { getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { UserVO } from '@/api/system/user'
import { cloneDeep } from 'lodash-es'
import { CommentVO } from '@/components/CommentInput/comment'
import { CommentApi, CommentSaveReqVO } from '@/api/infra/comment'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import FileUpload from '../components/FileUpload.vue'
import { dateFormatter3 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import AttachmentPreview from '../components/AttachmentPreview.vue'
import LogForm from '../../details/components/LogForm.vue'
import { ProblemVO } from '@/api/project/problem'
import { formDataChangeString, formDataChangeArray } from '@/utils/formDataChange'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { RiskFlowApi } from '@/api/bpm/risk'
import { hasPermission, getVisiableUserList } from '../util/permission'
import { useUserStore } from '@/store/modules/user'

const props = defineProps({
  basicsId: propTypes.number.isRequired,
  stage: propTypes.number.isRequired,
  stageIndex: propTypes.number.isRequired,
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired
})

const visiable = ref(false)
const currentTitle = ref('')
const edit = ref(true)
const currentTab = ref('risk')
const activeNames = ref(['1', '2'])
const formData = ref<RiskVO>({})
const message = useMessage()
const formRef = ref()
const commentRef = ref()
const attachmentList = ref<AttachmentRespVO[]>([])
const attachmentPreviewRef = ref()
const fileUploadAnewRef = ref()
const logFormRef = ref()
const editSection = ref(false)
const tempRow = ref<any>({})
const modifyInfo = ref<BasicsModifyInfoVO[]>([])
const loading = ref(false)

const formRules = {
  category: [{ required: true, message: '请选择风险分类', trigger: 'change' }],
  content: [{ required: true, message: '请填写风险描述', trigger: 'blur' }],
  affect: [{ required: true, message: '请选择风险影响', trigger: 'change' }],
  probability: [{ required: true, message: '请选择风险发生概率', trigger: 'change' }],
  level: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
  director: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  status: [{ required: true, message: '请选择风险状态', trigger: 'change' }]
}

const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})

const { getUser } = useUserStore()
const allowPermission = computed(() => {
  return formData.value.director?.includes(getUser.id)
})

const onDelete = async () => {
  loading.value = true
  try {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入删除原因', '删除风险', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '删除原因不能为空'
    })

    modifyInfo.value.length = 0
    modifyInfo.value.push({
      modifyField: 'delete',
      modifyFieldName: '风险删除审批处理,原因：' + value,
      beforeValue: ''
    })

    await RiskFlowApi.deleteRisk({
      ...formData.value,
      riskId: formData.value.id!,
      modifyInfo: modifyInfo.value
    })
    message.success('成功')
    visiable.value = false
    emits('submit:success')
  } finally {
    loading.value = false
  }
}
const router = useRouter()
/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

const onModify = async () => {
  loading.value = true
  try {
    if (!edit.value) {
      edit.value = true
      currentTab.value = 'risk'
      return
    }
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入变更原因', '风险', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '修改原因不能为空'
    })
    modifyInfo.value.push({
      modifyField: 'update',
      modifyFieldName: '风险修改原因：' + value,
      beforeValue: ''
    })
    await RiskFlowApi.updateRisk({
      ...formData.value,
      riskId: formData.value.id,
      modifyInfo: modifyInfo.value
    })
    message.success('修改成功')
    const res = await RiskApi.getRisk(formData.value.id!)
    formData.value = cloneDeep(res)

    emits('submit:success')
    if (formData.value.status === 2) {
      await openProblemForm()
    }
    edit.value = false
  } finally {
    loading.value = false
  }
}

const onTabChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'risk':
      commentRef.value?.listEvent(formData.value.id)
      break
    case 'target':
      onListAttachment()
      break
    case 'log':
      logFormRef.value?.refreshLog()
      break
  }
}

const openProblemForm = async () => {
  await message.confirm(
    '据C-IPD经营秘籍规定,当风险为已发生时，风险将转换为问题管理并在风险管理中归档（不可修改），是否确认？'
  )
  let data: ProblemVO = {
    content: formData.value.content,
    stage: formData.value.stage,
    measures: formData.value.solutionsActual,
    director: formData.value.director
  }
  emits('open:problem', data)
}

const onEditSection = async () => {
  if (!editSection.value) {
    editSection.value = true
    return
  }
  //如果不是已发生则直接更新
  if (formData.value.status !== 2) {
    onFeedback()
  } else {
    await openProblemForm()
  }
}

const onFeedback = async () => {
  await RiskApi.updateRiskFeedback(formData.value)
  message.success('更新成功')
  emits('submit:success')
  editSection.value = false
}

/** 删除附件 */
const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

/** 保存风险 */
const onSubmit = async (goOn: boolean) => {
  loading.value = true
  try {
    if (!formRef.value) return
    await unref(formRef).validate()
    formData.value.basicsId = props.basicsId
    formData.value.stage = props.stage
    await RiskFlowApi.createRisk(formData.value)
    if (formData.value.status == 2) {
      await openProblemForm()
    } else {
      message.success('保存成功')
      if (!goOn) {
        visiable.value = false
      }
      refresh()
      emits('submit:success')
    }
  } finally {
    loading.value = false
  }
}

const emits = defineEmits(['submit:success', 'open:problem'])

/** 打开表单 */
const openForm = async (title: string, row?: RiskVO) => {
  visiable.value = true
  currentTitle.value = title

  if (row) {
    edit.value = false
    editSection.value = false
    formData.value = cloneDeep(row)
    tempRow.value = row
    await nextTick()
    commentRef.value?.listEvent(formData.value.id)
  } else {
    edit.value = true
    editSection.value = true
    refresh()
  }
}
/** 发送评论 */
const onSaveComment = async () => {
  if (typeof commentData.value.imgs == 'string') {
    if (!commentData.value.imgs) {
      commentData.value.imgs = []
    } else {
      commentData.value.imgs = [commentData.value.imgs]
    }
  }
  const comment: CommentSaveReqVO = {
    moduleId: 'risk' + formData.value.id,
    content: commentData.value.content,
    imgUrls: commentData.value.imgs,
    parentId: -1,
    replyCommentId: -1
  }
  await CommentApi.createComment(comment)
  message.success('发送成功')
  commentData.value = {
    content: '',
    imgs: ''
  }
  commentRef.value?.listEvent(formData.value.id)
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'risk',
    dynamicId: formData.value.id
  })
  attachmentList.value = res
}

const refresh = () => {
  formData.value = {}
}

defineExpose({
  openForm,
  onFeedback
})
</script>
