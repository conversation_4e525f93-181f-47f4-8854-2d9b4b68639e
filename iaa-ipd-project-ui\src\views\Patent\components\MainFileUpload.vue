<template>
  <Dialog
    title="文件上传"
    v-model="uploadDialog.visiable"
    width="80%"
    :before-close="fileBeforeClose"
  >
    <UploadFile
      ref="uploadRef"
      v-model="uploadDialog.fileUrls"
      :limit="limit"
      :show-url-list="false"
      :file-size="100"
      drag
      @file-list="onUploadSuccess"
    />
    <vxe-table :data="uploadDialog.fileList" show-overflow>
      <vxe-column title="文件名称" field="name">
        <template #default="{ row }">
          <el-input v-model="row.name" />
        </template>
      </vxe-column>
      <vxe-column title="文件地址" field="url" />
      <vxe-column title="文件备注" field="remark">
        <template #default="{ row }">
          <el-input v-model="row.remark" />
        </template>
      </vxe-column>
      <vxe-column title="操作" align="center" width="80">
        <template #default="{ row }">
          <el-button type="danger" size="small" @click="handleDeleteFile(row)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="fileSubmit">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { deleteFile } from '@/api/infra/file'

const limit = ref(5)
const templateId = ref<number>(undefined as unknown as number)

const message = useMessage()
const uploadDialog = ref({
  visiable: false,
  fileUrls: [],
  fileList: [] as any[]
})
const uploadRef = ref()
/** 文件上传成功事件 */
const onUploadSuccess = async (fileList: any[]) => {
  uploadDialog.value.fileList.push(...fileList)
}
/** 拦截文件关闭窗口事件 */
const fileBeforeClose = async (done?: any) => {
  if (!done) {
    uploadDialog.value = {
      fileList: [],
      visiable: false,
      fileUrls: []
    }
    return
  }
  if (uploadDialog.value.fileList.length == 0) {
    done()
    return
  }
  await message.confirm('确定要关闭窗口嘛？关闭将会删除已上传的文件')
  uploadRef.value?.handleRemoveAllFile()
  done()
}
/** 文件保存 */
const fileSubmit = async () => {
  if (uploadDialog.value.fileList.length == 0) {
    message.alertError('请上传文件')
    return
  }
  let fileItemList = [] as any[]
  uploadDialog.value.fileList.forEach((item: any) => {
    fileItemList.push({
      attachmentName: item.name,
      infraFileId: item.id,
      attachmentUrl: item.url,
      attachmentRemark: item.remark
    })
  })
  emits('success', fileItemList)
  fileBeforeClose()
}

const emits = defineEmits<{
  (e: 'success', fileItemList: any[]): void
}>()

const openDialog = (limitValue?: number, templateIdValue?: number) => {
  uploadDialog.value = {
    fileList: [],
    visiable: true,
    fileUrls: []
  }
  limit.value = limitValue || 5
  if (templateIdValue) {
    templateId.value = templateIdValue
  }
}
/** 删除文件 */
const handleDeleteFile = async (row: any) => {
  const index = uploadDialog.value.fileList.map((f) => f.uri).indexOf(row.uri)
  if (index > -1) {
    uploadDialog.value.fileList.splice(index, 1)
    uploadDialog.value.fileUrls.splice(index, 1)
    await deleteFile(row.id)
    message.success('删除成功')
  }
}

defineExpose({
  openDialog
})
</script>
