<template>
  <!-- 申请文件表格 -->
  <div class="flex justify-between items-center">
    <CardTitle title="文件" />
    <el-button size="small" @click="fileUploadRef?.openDialog()">
      <img src="@/assets/opertion/upload.png" class="mr-10px w-20px h-20px" />
      上传文件
    </el-button>
  </div>
  <vxe-table
    :data="attachmentList"
    border
    size="small"
    class="custom-table"
    show-overflow
    :header-cell-config="{ height: 40 }"
    :cell-config="{ height: 40 }"
  >
    <vxe-column field="attachmentName" title="附件名称" min-width="200" :loading="loading">
      <template #default="{ row }">
        <el-link type="primary" @click="handlePreview(row)">
          {{ row.attachmentName }}
        </el-link>
      </template>
    </vxe-column>
    <vxe-column field="attachmentRemark" title="附件备注" min-width="200" />
    <vxe-column field="createTime" title="上传时间" min-width="150" />
    <vxe-column title="操作" width="200" align="center">
      <template #default="{ row }">
        <el-button
          type="primary"
          link
          size="small"
          @click="downloadByUrl({ fileName: row.attachmentName, url: row.attachmentUrl })"
          :disabled="props.formType === 'create'"
        >
          下载
        </el-button>
        <el-button type="danger" link size="small" @click="handleDeleteAttachment(row)">
          删除
        </el-button>
      </template>
    </vxe-column>
  </vxe-table>

  <FileUpload
    ref="fileUploadRef"
    v-model:openFolder="openFolder"
    @success="setAttachmentTableData"
  />
  <!-- OfficeEditor 文件预览组件 -->
  <OfficeEditor ref="officeEditorRef" :hasDialog="true" :download="false" />
</template>

<script lang="ts" setup>
import OfficeEditor from '@/components/OfficeEditor/index.vue'
import { FileTemplateVO } from '@/api/project/file/template'
import FileUpload from './MainFileUpload.vue'
import { propTypes } from '@/utils/propTypes'
import { downloadByUrl } from '@/utils/filt'
import { deleteFile } from '@/api/infra/file'
import { SubclassApi } from '@/api/patent/subclass'

const fileUploadRef = ref()
const openFolder = ref<FileTemplateVO | any>({} as any)
const props = defineProps({
  /** 所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析" */
  tableType: propTypes.oneOfType<0 | 1 | 2 | 3 | 4 | 5 | 6>([]).isRequired,
  belongsId: propTypes.number.isRequired, // 主表ID
  formType: propTypes.oneOfType<'create' | 'edit'>([]).isRequired
})

// 附件数据
const attachmentList = ref<any[]>([])
const message = useMessage()
const loading = ref(false)

const setAttachmentTableData = async (fileItemList: any[]) => {
  if (props.belongsId && props.tableType) {
    await SubclassApi.batchCreate({
      tableType: props.tableType,
      belongsId: props.belongsId,
      attachmentList: fileItemList
    })
    onList()
  } else {
    attachmentList.value.push(...fileItemList)
  }
}

// OfficeEditor 组件引用
const officeEditorRef = ref()

const handleDeleteAttachment = async (row: any) => {
  try {
    await message.confirm(`确定要删除${row.attachmentName}？`)
    attachmentList.value = attachmentList.value.filter(
      (item) => item.infraFileId != row.infraFileId
    )
    if (row?.id) {
      await SubclassApi.deleteById(row.id, 'attachment')
    }
    await deleteFile(row.infraFileId)
    onList()
    message.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 预览和下载相关方法
const handlePreview = (row: any) => {
  // 使用 OfficeEditor 组件预览文件
  if (row.infraFileId && row.attachmentName) {
    officeEditorRef.value?.open(row.infraFileId, row.attachmentName)
  } else {
    message.warning('文件信息不完整，无法预览')
  }
}

// 获取附件列表
const onList = async () => {
  if (!props.belongsId || !props.tableType) return
  loading.value = true
  try {
    const res = await SubclassApi.querySubclass({
      tableType: props.tableType,
      belongsId: props.belongsId,
      queryType: 'attachment'
    })
    attachmentList.value = res.list
  } finally {
    loading.value = false
  }
}

// 获取数据的方法
const getData = () => {
  return attachmentList.value
}

// 暴露给父组件的方法
defineExpose({
  onList,
  getData
})
</script>

<style lang="scss" scoped>
.attachment-info {
  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .table-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;

        &::before {
          content: '■';
          color: #409eff;
          margin-right: 5px;
        }
      }
    }

    .custom-table {
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }
      }

      :deep(.el-table__body) {
        tr:hover > td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

.preview-container {
  .no-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #909399;

    p {
      margin: 20px 0;
      font-size: 16px;
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-upload) {
  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 7px;
  }
}
</style>
