import request from '@/config/axios'

export interface SummaryVO {
  id?: number
  basicsId?: number
  activitiesId?: number
  startDate?: Date | string
  endDate?: Date | string
  director?: number[]
  status?: number
  description?: string
  creator?: number[]
}

export const SummaryApi = {
  /** 获取所有活动信息 */
  getActivitiesAll: (basicsId: number) => {
    return request.get({ url: `/project/plan-summary/get-activities-all/${basicsId}` })
  },
  /** 获取重点活动信息 */
  getFocusActivities: (basicsId: number) => {
    return request.get({ url: `/project/plan-summary/get-focus-activities/${basicsId}` })
  },
  /** 保存活动关键信息 */
  saveFocus: (data: any) => {
    return request.post({ url: `/project/plan-summary/save-focus`, data })
  },
  /** 保存计划总结实现 */
  saveItem: (data: any) => {
    return request.post({ url: '/project/plan-summary/save-item', data })
  },
  /** 获取计划总结事项分解信息 */
  getSummaryItemList: (params: any) => {
    return request.get({ url: '/project/plan-summary/get-summary-item-list', params })
  },
  deleteItem: (id: number) => {
    return request.get({ url: `/project/plan-summary/delete-item/${id}` })
  }
}
