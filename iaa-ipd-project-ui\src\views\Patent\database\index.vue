<template>
  <ContentWrap>
    <div class="h-[calc(100vh-170px)]">
      <vxe-toolbar size="mini" custom ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" plain @click="databaseFormRef?.openForm()">
            <Icon icon="ep:plus" />
            新增
          </el-button>
        </template>
        <template #tools>
          <el-button circle class="!h-2rem !w-2rem mr-10px" @click="databaseUploadFormRef?.open()">
            <Icon icon="ep:upload" />
          </el-button>
        </template>
      </vxe-toolbar>
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)'
          }"
          :header-cell-config="{ height: 40 }"
          :cell-config="{ height: 40 }"
          :data="list"
          border
          show-overflow
          align="center"
          @cell-click="(el) => databaseFormRef?.openForm(el.row.id)"
        >
          <vxe-column title="搜索词" align="left" field="word" width="150" />
          <vxe-column title="开始时间" width="100" field="startDate" />
          <vxe-column title="结束时间" width="100" field="endDate" />
          <vxe-column title="专利号" width="150" field="patentNo" />
          <vxe-column title="公开号" width="150" field="publicNo" />
          <vxe-column title="专利名称" width="250" align="left" field="patentName" />
          <vxe-column title="专利类型" field="patentType" min-width="100">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PATENT_TYPE" :value="row.patentType" />
            </template>
          </vxe-column>
          <vxe-column title="权力人" width="150" field="ownership" />
          <vxe-column title="发明人" min-width="150" field="inventor" />
          <vxe-column title="申请日期" min-width="120" field="applicationDate" />
          <vxe-column title="授权日期" min-width="120" field="authorizationDate" />
          <vxe-column title="法律状态" field="legalStatus" min-width="90">
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="row.legalStatus" />
            </template>
          </vxe-column>
          <vxe-column title="摘要" width="300" field="abstracts" />
          <vxe-column title="保护的整机类型" min-width="120" field="machineType" />
          <vxe-column title="保护的组件类型" min-width="120" field="component" />
          <vxe-column title="保护的零件类型" min-width="120" field="part" />
          <vxe-column title="技术点" min-width="120" field="technologyPoint" />
          <vxe-column title="解决的问题" min-width="120" field="solvedProblem" />
          <vxe-column title="功能点" min-width="300" field="functionPoint" />
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <DatabaseForm ref="databaseFormRef" @success="handleList()" />
    <DatabaseUploadForm ref="databaseUploadFormRef" @success="handleList()" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { DatabaseApi, DatabaseVO } from '@/api/patent/database'
import DatabaseForm from './DatabaseForm.vue'
import { ref, onMounted, nextTick } from 'vue'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter4 } from '@/utils/formatTime'
import DatabaseUploadForm from './DatabaseUploadForm.vue'

// 消息弹窗
const databaseFormRef = ref()
const databaseUploadFormRef = ref()
const toolbarRef = ref()
const tableRef = ref()
const total = ref(0)
const list = ref<DatabaseVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  patentType: undefined,
  legalStatus: undefined,
  word: undefined
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const data = await DatabaseApi.getDatabasePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
