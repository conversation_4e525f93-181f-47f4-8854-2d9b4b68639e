<template>
  <el-drawer
    :title="`活动分解-${currentStageTitle}`"
    v-model="dialogVisible"
    size="80%"
    destroy-on-close
  >
    <div style="height: 74vh">
      <vxe-toolbar ref="toolbarRef" size="mini">
        <template #tools>
          <el-button circle @click="exportTemplate" title="导出模板">
            <Icon icon="ep:download" />
          </el-button>

          <el-button circle @click="openImportDialog" title="导入活动时间">
            <Icon icon="ep:upload" />
          </el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        v-loading="loading"
        :data="templateList.filter((item) => (shielding ? !item.hasDisassemble : true))"
        border
        stripe
        height="100%"
        scrollbar-always-on
        row-key="id"
        @cell-click="handleRowClick"
        ref="tableRef"
        :edit-config="{ trigger: 'click' }"
        show-overflow
        :header-cell-style="{ padding: 0, height: '36px' }"
        :cell-style="{ padding: 0, height: '36px' }"
        :checkbox-config="{
          checkMethod: checkboxMethod,
          visibleMethod: checkboxVisibleMethod,
          checkRowKeys: templateList.filter((item) => item.hasMust).map((item) => item.id)
        }"
        :row-config="{ keyField: 'id' }"
      >
        <vxe-column type="checkbox" width="55" align="center" />
        <vxe-column min-width="200" show-overflow-tooltip align="left" title="活动主题">
          <template #default="{ row }">
            <div :style="{ paddingLeft: `${(row.level - 2) * 20}px` }">{{
              `${row.orderNo} ${row.name}`
            }}</div>
          </template>
        </vxe-column>
        <vxe-column
          min-width="150"
          show-overflow-tooltip
          align="left"
          title="活动内容"
          field="content"
        />
        <vxe-column width="110" title="默认角色">
          <template #default="{ row }">
            <dict-tag
              v-if="row.defaultRole"
              :type="DICT_TYPE.PROJECT_TEAM_ROLE"
              :value="row.defaultRole"
            />
          </template>
        </vxe-column>
        <vxe-column width="150" title="主负责人">
          <template #default="{ row }">
            <user-avatar-list
              v-model="row.director"
              :user-list="props.userList"
              :size="28"
              :limit="3"
              :add="!row.hasDisassemble"
              :visiable-user-list="getVisiableUserList()"
            />
          </template>
        </vxe-column>
        <vxe-column width="150" title="执行人">
          <template #default="{ row }">
            <user-avatar-list
              v-model="row.coordinate"
              :user-list="props.userList"
              :size="28"
              :limit="3"
              :add="!row.hasDisassemble"
              :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
            />
          </template>
        </vxe-column>
        <vxe-column width="160" title="开始时间" :edit-render="{ autoFocus: '.el-input__wrapper' }">
          <template #default="{ row }">
            {{ !row.startDate ? '点击设定' : row.startDate }}
          </template>
          <template #edit="{ row }">
            <el-date-picker
              v-model="row.startDate"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 140px"
              :disabled-date="disabledDate"
              :default-value="defaultDateValue"
              @change="(value) => onDateChange(row, value)"
              v-if="!row.hasDisassemble"
            />
            <template v-else>
              {{ row.startDate }}
            </template>
          </template>
        </vxe-column>
        <vxe-column width="160" title="结束时间" :edit-render="{ autoFocus: '.el-input__wrapper' }">
          <template #default="{ row }">
            {{ !row.endDate ? '点击设定' : row.endDate }}
          </template>
          <template #edit="{ row }">
            <el-date-picker
              v-model="row.endDate"
              type="date"
              value-format="YYYY-MM-DD"
              style="width: 140px"
              :disabled-date="(date) => disabledDate(date, row.startDate)"
              :default-value="defaultDateValue"
              @change="(value) => (defaultDateValue = value)"
              v-if="!row.hasDisassemble"
            />
            <template v-else>
              {{ row.endDate }}
            </template>
          </template>
        </vxe-column>
        <vxe-column title="标准工时(天)" field="defaultManDay" width="80" align="center" />
        <vxe-column title="关键活动" field="isCrux" width="60" align="center">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.isCrux" />
          </template>
        </vxe-column>
        <vxe-column title="已分解" filed="hasDisassemble" width="120" align="center">
          <template #header>
            已分解
            <el-checkbox v-model="shielding">屏蔽已分解</el-checkbox>
          </template>
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="row.hasDisassemble" />
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="saveActivities(false)"> 确认 </el-button>
      <el-button type="warning" :loading="loading" @click="saveActivities(true)">
        确认并继续添加
      </el-button>
    </template>
  </el-drawer>
  <Dialog v-model="importDailogVisible" title="导入活动时间" width="500px">
    <el-upload
      ref="uploadRef"
      v-model:file-list="fileList"
      :auto-upload="false"
      :limit="1"
      :on-exceed="handleExceed"
      :on-change="handleFileChange"
      accept=".xlsx, .xls"
      drag
    >
      <Icon icon="ep:upload" />
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <span>仅允许导入 xls、xlsx 格式文件。</span>
          <div>文件格式：第一列为活动名称，第二列为开始时间，第三列为结束时间</div>
        </div>
      </template>
    </el-upload>
    <template #footer>
      <el-button type="primary" @click="handleImport" :loading="importLoading">导入</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { ActivitiesDisassembleVO, ActivitiesApi } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import { TeamVO } from '@/api/project/team'
import { dateUtil } from '@/utils/dateUtil'
import dayjs from 'dayjs'
import { getVisiableUserList } from '../util/permission'
import * as XLSX from 'exceljs'

import { useCache } from '@/hooks/web/useCache'
import { BasicsVO } from '@/api/project/basics'
import * as ConfigApi from '@/api/infra/config'
import { useUserStore } from '@/store/modules/user'
import { SupportLibraryApi } from '@/api/project/supportlibrary'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const { getUser } = useUserStore()

const props = defineProps({
  basicsId: propTypes.number.def(0),
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired,
  templateCategory: propTypes.string.def('10')
})

const { wsCache } = useCache()
const shielding = ref(true)
const dialogVisible = ref(false)
const currentStageTitle = ref('') //当前阶段标题
const currentStage = ref() //当前阶段
const currentTeams = ref<TeamVO[]>([]) //当前项目团队成员
const loading = ref(false)
const templateList = ref<ActivitiesDisassembleVO[]>([])
const tableRef = ref()
const toolbarRef = ref()
const message = useMessage()
const defaultDateValue = ref<Date>(new Date())
const gaDate = ref('')

const importDailogVisible = ref(false)
const uploadRef = ref()
const fileList = ref<any[]>([])
const importLoading = ref(false)

const queryParams = reactive({
  basicsId: undefined,
  stage: undefined
})

/** 查询列表 */
const getTemplateList = async () => {
  loading.value = true
  try {
    queryParams.basicsId = props.basicsId as unknown as undefined
    queryParams.stage = currentStage.value
    const res = await ActivitiesApi.getActivitiesDisassembleList(queryParams)
    templateList.value = res
    console.log(res)

    templateList.value.forEach((item) => {
      item.director = currentTeams.value.find((team) => item.defaultRole == team.role)?.userIds
    })
  } finally {
    loading.value = false
  }
}


/** 显示活动分解页面 */
const openForm = (stageTitle: string, stage: number, teams: TeamVO[], ga: string) => {
  dialogVisible.value = true
  currentStageTitle.value = stageTitle
  currentStage.value = stage
  currentTeams.value = teams
  gaDate.value = ga
  getTemplateList()
  getSupportLibraryList()
}

defineExpose({ openForm })

const handleRowClick = ({ row }: any) => {
  if (row.hasMust || row.hasDisassemble) return
  unref(tableRef)?.setCheckboxRow(row, true)
}

const emits = defineEmits(['success'])

/** 保存批量分解活动 */
const saveActivities = async (isBatch: boolean) => {
  loading.value = true
  try {
    await unref(tableRef)?.clearEdit()
    await unref(tableRef)?.updateData()
    const tempRows = unref(tableRef)?.getCheckboxRecords(true)
    const rows = templateList.value?.filter((item) =>
      tempRows.map((tempItem) => tempItem.id).includes(item.id)
    )
    if (rows.length === 0) {
      message.alertError('请选择要保存的数据')
      return
    }
    let data: any[] = []
    rows.forEach((item: ActivitiesDisassembleVO) => {
      if (item.hasDisassemble) return
      data.push({
        basicsId: props.basicsId,
        templateId: item.id,
        director: item.director,
        coordinate: item.coordinate,
        startDate: item.startDate,
        endDate: item.endDate,
        stage: currentStage.value,
        mold: 0
      })
    })

    await ActivitiesApi.createActivitiesBatch(data)
    message.success('保存成功')
    if (isBatch) {
      getTemplateList()
    } else {
      dialogVisible.value = false
    }
    emits('success')
  } finally {
    loading.value = false
  }
}
/** 选中方法 */
const checkboxMethod = ({ row }: { row: ActivitiesDisassembleVO }) => {
  return !row.hasMust || getUser.id == 1
}
/** 隐藏方法 */
const checkboxVisibleMethod = ({ row }: { row: ActivitiesDisassembleVO }) => {
  return !row.hasDisassemble || getUser.id == 1
}

const onDateChange = (row: ActivitiesDisassembleVO, value: Date) => {
  defaultDateValue.value = value
  row.endDate = dayjs(value)
    .add(row.defaultManDay - 1, 'days')
    .format('YYYY-MM-DD')
}

const iotDate = ref(0)
const getIotDateConfig = async () => {
  const res = await ConfigApi.getConfigKey('iot.date')
  iotDate.value = res
}

/** 将ga时间之后的时间禁用，同时将同行第一个时间之前的时间禁用 */
const disabledDate = (time: Date, startDate?: Date) => {
  if (basicsInfo.value?.categoryIds?.includes(8) && iotDate.value == 1) {
    return false
  }
  if (startDate) {
    return dateUtil(startDate).isAfter(time) || dateUtil(gaDate.value).isBefore(time)
  }
  //if (gaDate.value) {
    //return dateUtil().subtract(1, 'days').isAfter(time) || dateUtil(gaDate.value).isBefore(time)
 // }
  return dateUtil().subtract(1, 'days').isAfter(time)
}

/** 文件超出限制时的钩子 */
const handleExceed = () => {
  message.warning('最多只能上传1个文件')
}

/** 文件状态改变时的钩子 */
const handleFileChange = (file: any) => {
  fileList.value = [file]
}

/** 打开导入对话框 */
const openImportDialog = () => {
  importDailogVisible.value = true
  fileList.value = []
}

/** 处理导入 */
const handleImport = async () => {
  if (fileList.value.length === 0) {
    message.warning('请选择要上传的文件')
    return
  }

  importLoading.value = true
  try {
    const file = fileList.value[0].raw
    const reader = new FileReader()

    reader.onload = async (e) => {
      try {
        const data = e.target?.result
        const workbook = new XLSX.Workbook()
        await workbook.xlsx.load(data as ArrayBuffer)

        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(1)
        if (!worksheet) {
          message.error('无法读取工作表')
          return
        }

        // 解析数据
        const activityData: {
          name: string
          content: string
          startDate: string
          endDate: string
        }[] = []

        // 从第二行开始读取数据（跳过表头）
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) {
            // 跳过表头
            const name = row.getCell(1).text.trim()
            const content = row.getCell(2).text.trim()
            const startDate = row.getCell(3).text.trim()
            const endDate = row.getCell(4).text.trim()
            if (dateUtil(startDate).isAfter(dateUtil(endDate))) {
              message.error('开始时间不能大于结束时间')
              return
            }
            if (dateUtil(endDate).isAfter(dateUtil(gaDate.value))) {
              message.error('结束时间不能大于ga时间')
              return
            }
            if (dateUtil(startDate).isBefore(dateUtil())) {
              message.error('开始时间不能小于当前时间')
              return
            }
            if (name && startDate && endDate) {
              activityData.push({ name, content: content, startDate, endDate })
            }
          }
        })

        if (activityData.length === 0) {
          message.warning('未找到有效数据')
          return
        }

        // 更新表格数据
        updateTableWithImportedData(activityData)

        message.success(`成功导入 ${activityData.length} 条数据`)
        importDailogVisible.value = false
      } catch (error) {
        console.error('解析Excel文件失败:', error)
        message.error('解析Excel文件失败')
      } finally {
        importLoading.value = false
      }
    }

    reader.onerror = () => {
      message.error('读取文件失败')
      importLoading.value = false
    }

    reader.readAsArrayBuffer(file)
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败')
    importLoading.value = false
  }
}

/** 根据导入的数据更新表格 */
const updateTableWithImportedData = (
  activityData: { name: string; startDate: string; endDate: string }[]
) => {
  // 遍历导入的数据
  activityData.forEach((importedItem) => {
    // 在表格数据中查找匹配的活动名称
    const matchingRow = templateList.value.find(
      (row) => row.name === importedItem.name && !row.hasDisassemble
    )

    if (matchingRow) {
      // 更新开始时间和结束时间
      matchingRow.startDate = dateUtil(importedItem.startDate).format('YYYY-MM-DD')
      matchingRow.endDate = dateUtil(importedItem.endDate).format('YYYY-MM-DD')

      // 自动选中该行
      unref(tableRef)?.setCheckboxRow(matchingRow, true)
    }
  })

  // 更新表格数据
  unref(tableRef)?.updateData()
}

/** 导出模板 */
const exportTemplate = async () => {
  try {
    // 创建一个新的工作簿
    const workbook = new XLSX.Workbook()
    const worksheet = workbook.addWorksheet('活动时间模板')

    // 添加表头
    worksheet.columns = [
      { header: '活动主题', key: 'name', width: 30 },
      { header: '活动内容', key: 'content', width: 30 },
      { header: '开始时间', key: 'startDate', width: 15 },
      { header: '结束时间', key: 'endDate', width: 15 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    }

    // 获取未分解的活动数据
    const activities = templateList.value.filter((item) => !item.hasDisassemble)

    // 添加数据行
    activities.forEach((activity) => {
      worksheet.addRow({
        name: activity.name,
        content: activity.content || '',
        startDate: activity.startDate || '',
        endDate: activity.endDate || ''
      })
    })

    // 设置日期列的格式
    worksheet.getColumn('startDate').numFmt = 'yyyy-mm-dd'
    worksheet.getColumn('endDate').numFmt = 'yyyy-mm-dd'

    // 导出文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = URL.createObjectURL(blob)

    // 创建下载链接并点击
    const link = document.createElement('a')
    link.href = url
    link.download = `活动时间模板_${dayjs().format('YYYYMMDD')}.xlsx`
    link.click()

    // 释放URL对象
    URL.revokeObjectURL(url)

    message.success('导出模板成功')
  } catch (error) {
    console.error('导出模板失败:', error)
    message.error('导出模板失败')
  }
}

const basicsInfo = ref<any>()
onMounted(() => {
  unref(tableRef)?.connect(unref(toolbarRef))
  basicsInfo.value = wsCache.get('PROJECT_BASICS_INFO') as BasicsVO
  getIotDateConfig()
})
</script>

<style lang="scss" scoped>
/** header tabs 样式 */
.header-tabs-container-temp.el-tabs {
  border: none;
  border-radius: 2px;
  background-color: #ebeff3;

  :deep(.el-tabs__header) {
    background-color: #fff !important;
    border-bottom: 0.3px solid #d8d8d8;
    // border-bottom-left-radius: 20px;
    // border-bottom-right-radius: 20px;

    // .el-tabs__nav {
    //   padding: 0 20px;
    // }
    .el-tabs__item {
      height: 1.8rem;
      font-size: 1rem;
      color: var(--primary-text-color);
      &.is-active {
        border-top: 3px solid var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }
}
</style>
