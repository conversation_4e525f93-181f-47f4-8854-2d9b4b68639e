import request from '@/config/axios'

export const TechnicalFlowApi = {
  // 获取技术流程
  getTechnicalFlow: async (id: string) => {
    return await request.get({ url: `/bpm/technical/get/${id}` })
  },
  // 更新技术流程
  updateTechnicalFlow: async (data: any) => {
    return await request.post({ url: '/bpm/technical/update', data })
  },
  // 删除技术流程
  deleteTechnicalFlow: async (data: any) => {
    return await request.post({ url: '/bpm/technical/delete', data })
  },
  // 完成技术流程
  completeTechnicalFlow: async (data: any) => {
    return await request.post({ url: '/bpm/technical/complete', data })
  }
}
