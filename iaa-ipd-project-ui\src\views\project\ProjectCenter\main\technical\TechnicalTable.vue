<template>
  <div class="table-container">
    <vxe-toolbar ref="toolbarRef" size="mini" custom :export="hasPermission('technical_export')">
      <template #buttons>
        <el-button
          type="primary"
          size="small"
          plain
          @click="technicalFormRef?.openForm('添加预审记录')"
          v-if="getVisiableUserList(props.basicsId).includes(getUser.id)"
        >
          添加预审记录
        </el-button>
      </template>
    </vxe-toolbar>
    <vxe-table
      height="94%"
      :header-cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        backgroundColor: '#fafafa',
        color: 'var(--primary-text-color)'
      }"
      :row-style="{
        cursor: 'pointer'
      }"
      :cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        color: 'var(--primary-text-color)'
      }"
      round
      auto-resize
      border
      :loading="loading"
      :row-config="{ drag: true, isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
      :column-config="{ resizable: true, isHover: true }"
      :export-config="{ remote: true, exportMethod: onExport }"
      :data="technicalList"
      show-overflow
      ref="technicalTableRef"
      @cell-click="(el: any) => technicalFormRef?.openForm('评审要素操作', el.row)"
    >
      <vxe-column title="提出人" field="creator" width="80">
        <template #default="{ row }">
          <user-avatar-list
            v-model="row.creator"
            :user-list="props.userList"
            :size="28"
            :limit="3"
            :add="false"
          />
        </template>
      </vxe-column>
      <vxe-column
        title="提出日期"
        field="createTime"
        :formatter="dateFormatter4"
        width="90"
        align="center"
      />
      <vxe-column title="图片" field="imgIds" width="60" align="center">
        <template #default="{ row }">
          <template v-if="row.imgIds?.length > 0">
            <img :src="row.imgIds?.[0]" style="width: 1.5vw; height: 1.5vw" />
            <div
              style="
                position: absolute;
                top: 0;
                width: 1vw;
                height: 1vw;
                background-color: var(--el-color-primary-light-3);
                border-radius: 1rem;
                right: 0;
                color: #fff;
              "
              >{{ row.imgIds?.length }}</div
            >
          </template>
        </template>
      </vxe-column>
      <vxe-column title="状态" field="status" width="95" align="center">
        <template #default="{ row }">
          <DictTag type="project_activities_status" :value="row.status" />
        </template>
      </vxe-column>
      <vxe-column title="进度" field="progress" width="100">
        <template #default="{ row }">
          <el-progress
            :text-inside="true"
            :stroke-width="22"
            :percentage="row.progress"
            status="success"
            class="no-radius"
          />
        </template>
      </vxe-column>
      <vxe-column title="针对要素" field="targetFactor">
        <template #default="{ row }">
          {{ props.factorList.find((item) => item.id === row.targetFactor)?.factor }}
        </template>
      </vxe-column>
      <vxe-column
        title="问题描述"
        field="description"
        :filters="descriptionOptions"
        :filter-render="FilterValue.textFilterRender"
      />
      <vxe-column
        title="建议"
        field="suggestion"
        :filters="suggestionOptions"
        :filter-render="FilterValue.textFilterRender"
      />
      <vxe-column
        title="负责人"
        field="director"
        width="120"
        :filters="directorOptions"
        :filter-render="FilterValue.userFilterRender"
      >
        <template #default="{ row }">
          <user-avatar-list
            v-model="row.director"
            :user-list="props.userList"
            :size="28"
            :limit="3"
            :add="false"
          />
        </template>
      </vxe-column>
      <vxe-column
        title="预计完成"
        field="endDate"
        :formatter="dateFormatter4"
        align="center"
        width="100"
      />
    </vxe-table>
  </div>
  <!-- @row-dragend="rowDragendEvent"
    
    :export-config="{ remote: true, exportMethod: onExport }"
    :data="props.list" -->

  <TechnicalForm
    ref="technicalFormRef"
    :user-list="props.userList"
    :basics-id="props.basicsId"
    :node-id="props.nodeId"
    :factor-list="props.factorList"
    @submit:success="onList"
  />
</template>

<script lang="ts" setup>
import TechnicalForm from './TechnicalForm.vue'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import { TechnicalFactorVO } from '@/api/project/technicalfactor'
import { TechnicalApi, TechnicalVO } from '@/api/project/technical'
import { debounce } from 'lodash-es'
import { dateFormatter4 } from '@/utils/formatTime'
import download from '@/utils/download'
import { hasPermission, getVisiableUserList } from '../util/permission'
import { useCache } from '@/hooks/web/useCache'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import { useUserStore } from '@/store/modules/user'
const { getUser } = useUserStore()

const suggestionOptions = ref([{ data: '' }])
const descriptionOptions = ref([{ data: '' }])
const directorOptions = ref([{ data: [] }])

const toolbarRef = ref()
const technicalTableRef = ref()
const technicalFormRef = ref()
const technicalList = ref<TechnicalVO[]>([])
const message = useMessage()
const { wsCache } = useCache()
const loading = ref(false)

const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired,
  basicsId: propTypes.number.isRequired,
  nodeId: propTypes.number.isRequired,
  factorList: propTypes.oneOfType<TechnicalFactorVO[]>([]).isRequired
})

/** 导出方法 */
const onExport = async ({ options }: any) => {
  try {
    if (!props.basicsId) return
    // 导出的二次确认
    await message.exportConfirm()
    const columns = options.columns.map((item) => item.field)
    if (columns.length === 0) {
      message.warning('未选择需要导出的列')
      return
    }
    // if (columns.includes('date')) {
    //   columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
    // }
    const data = await TechnicalApi.exportTechnical({
      basicsId: props.basicsId,
      nodeId: props.nodeId,
      filename: options.filename,
      column: columns
    })
    download.excel(data, `${options.filename}.xlsx`)
  } catch {}
}

const scrollToRow = (id: number) => {
  requestAnimationFrame(() => {
    const row = technicalTableRef.value?.getRowById(id)
    technicalTableRef.value?.scrollToRow(row)
    technicalTableRef.value?.setCurrentRow(row)
    if (row) {
      technicalFormRef?.value.openForm('评审要素操作', row)
    }
    wsCache.delete('project_page_show_form')
  })
}

const onList = debounce(async () => {
  loading.value = true
  try {
    const res = await TechnicalApi.listTechnical({
      basicsId: props.basicsId,
      nodeId: props.nodeId
    })
    technicalList.value = res
    await nextTick()
    const form = wsCache.get('project_page_show_form')
    if (form) {
      scrollToRow(Number(form.id))
    }
  } finally {
    loading.value = false
  }
}, 100)

watch(
  () => [props.basicsId, props.nodeId],
  () => {
    if (!props.basicsId || !props.nodeId) return
    onList()
  },
  { immediate: true }
)

onMounted(() => {
  unref(technicalTableRef)?.connect(unref(toolbarRef))
})
</script>

<style lang="scss" scoped>
.table-container {
  width: 100%;
  height: calc(100% - 1.6vw - 10px);
  background-color: white;
  padding: 5px;
}

:deep(.vxe-toolbar) {
  height: 5%;

  .vxe-buttons--wrapper {
    & > * {
      height: 2rem;
      font-size: 0.8rem;
    }
  }

  .vxe-tools--operate {
    & > * {
      height: 2rem;
      width: 2rem !important;
      font-size: 0.8rem;
    }
  }
}

:deep(.vxe-cell) {
  height: 2.5rem !important;
}
</style>
