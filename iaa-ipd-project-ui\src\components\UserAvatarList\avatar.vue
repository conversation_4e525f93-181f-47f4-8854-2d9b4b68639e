<template>
  <el-popover :placement="placement" :width="200" trigger="hover">
    <template #reference>
      <el-avatar
        :size="size"
        :style="{
          backgroundColor: user.status === 2 ? '#ccc' : user.backgroundColor,
          fontSize: `${size / 2 - 2.5}px`,
          textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)'
        }"
      >
        {{
          user.nickname.length > 2
            ? user.nickname.substring(user.nickname.length - 2)
            : user.nickname
        }}
      </el-avatar>
    </template>
    <el-button
      type="danger"
      size="small"
      plain
      style="width: 100%; margin-bottom: 5px"
      v-if="showDelete"
      @click="deleteUser(user)"
    >
      移除
    </el-button>
    <el-form label-width="60px" class="custom-form" size="small">
      <el-form-item label="姓名">{{ user.nickname }}</el-form-item>
      <el-form-item label="状态">
        <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="user.status" />
      </el-form-item>
      <el-form-item label="工号">{{ user.username }}</el-form-item>
      <el-form-item label="部门">{{ user.deptName }}</el-form-item>
    </el-form>
  </el-popover>
</template>
<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { UserVO } from '@/api/system/user'
import { PropType } from 'vue'
interface TempUserVO extends UserVO {
  backgroundColor: string
  deptName: string
}
const props = defineProps({
  user: {
    type: Object as PropType<TempUserVO>,
    required: true
  },
  size: {
    type: Number,
    default: 40
  },
  placement: {
    type: String as PropType<
      | 'top-start'
      | 'top-end'
      | 'top'
      | 'bottom-start'
      | 'bottom'
      | 'bottom-end'
      | 'right-start'
      | 'right-end'
      | 'left-start'
      | 'left-end'
    >,
    default: 'bottom'
  },
  showDelete: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['delete'])

const deleteUser = (user: TempUserVO) => {
  emit('delete', user)
}
</script>
