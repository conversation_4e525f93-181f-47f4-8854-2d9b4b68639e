<template>
  <el-form label-width="120px" class="custom-border-form">
    <el-form-item
      v-for="item in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
      <user-avatar-list
        v-model="teamMap[item.value]"
        :user-list="userList"
        :size="30"
        :add="!['pm'].includes(item.value) && edit"
        @change:msg="
          formDataChange(
            item.value,
            item.label,
            teamMap[item.value],
            propFormData.teams?.find((team) => team.role === item.value)?.userIds!
          )
        "
      />
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user/index'
import { BasicsVO } from '@/api/project/basics'
import { copyValueToTarget } from '@/utils'
import { listTeam } from '@/api/project/team'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'

const props = defineProps({
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired,
  propFormData: {
    type: Object as PropType<BasicsVO>,
    default: () => {}
  },
  edit: propTypes.bool.def(true)
})
/** 数据存储对象 */
const formData = ref<BasicsVO>({
  teams: []
})
/** 数据存储Map */
const teamMap = ref<any>({})
/** 数据修改 */
const modifyInfo = ref<BasicsModifyInfoVO[]>([])

/** 刷新团队信息 */
const refreshTeam = async () => {
  if (!props.propFormData.id) {
    return
  }
  const res = await listTeam(props.propFormData.id)
  formData.value.teams = res
  setTeamMap()
}
/** 将团队信息放到FormData中 */
const setTeamMap = () => {
  formData.value.teams?.forEach((role) => {
    teamMap.value[role.role!] = role.userIds
  })
}
/** 将teamMap的值赋值给formData */
const validate = () => {
  formData.value.teams?.forEach((role) => {
    role.userIds = teamMap.value[role.role!]
  })
  if (formData.value.teams?.length === 0) {
    for (const key in teamMap.value) {
      formData.value.teams.push({
        role: key,
        userIds: teamMap.value[key],
        basicsId: props.propFormData.id
      })
    }
  }
  for (const key in teamMap.value) {
    if (key === 'pm') continue
    if (formData.value.teams?.some((item) => item.role === key)) {
      const data = formData.value.teams?.find((item) => item.role === key)
      if (data) {
        data.userIds = teamMap.value[key]
      }
    } else {
      formData.value.teams!.push({
        role: key,
        userIds: teamMap.value[key],
        basicsId: props.propFormData.id
      })
    }
  }
  debugger
  //校验通过更新数据
  Object.assign(props.propFormData, formData.value)
}
/** 项目角色变更 */
const formDataChange = (
  key: string,
  title: string,
  currentValue: number[],
  beforeValue: number[]
) => {
  // 对两个数组进行排序
  const sortedCurrent = currentValue?.slice().sort((a, b) => a - b)
  const sortedBefore = beforeValue?.slice().sort((a, b) => a - b)
  let compare = true
  // 比较排序后的数组是否相等
  if (sortedCurrent?.length !== sortedBefore?.length) {
    compare = false // 长度不同，直接返回 false
  }

  for (let i = 0; i < sortedCurrent.length; i++) {
    if (sortedCurrent?.[i] !== sortedBefore?.[i]) {
      compare = false // 元素不同，返回 false
    }
  }
  if (compare) {
    modifyInfo.value = modifyInfo.value.filter((item) => item.modifyField !== key)
    return
  }
  const exists = modifyInfo.value.find((item) => item.modifyField === key)
  if (exists) {
    exists.beforeValue = JSON.stringify(beforeValue)
    exists.afterValue = JSON.stringify(currentValue)
  } else {
    modifyInfo.value.push({
      modifyField: key,
      modifyFieldName: title,
      beforeValue: JSON.stringify(beforeValue),
      afterValue: JSON.stringify(currentValue)
    })
  }
}

/** 获取表单变更数据 */
const getChangeMsg = () => {
  return modifyInfo.value
}

defineExpose({
  refreshTeam,
  validate,
  getChangeMsg
})

/** 将传进来的值赋值给formData */
watch(
  () => props.propFormData,
  (data) => {
    if (!data) {
      return
    }
    copyValueToTarget(formData.value, data)
    setTeamMap()
  },
  {
    immediate: true
  }
)
</script>
