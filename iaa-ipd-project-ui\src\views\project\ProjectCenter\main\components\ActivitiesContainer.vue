<template>
  <div class="activities-container">
    <div class="activities-header">
      <div class="current-project-name">
        <el-tooltip :content="props.basicsInfo?.name">
          <div
            class="overflow-hidden whitespace-nowrap text-ellipsis inline-block"
            style="max-width: calc(100% - 170px)"
          >
            {{ props.basicsInfo?.name }}
          </div>
        </el-tooltip>
        <div class="w-170px text-.7rem">计划发布:{{ props.basicsInfo?.releaseDate }}</div>
      </div>
      <el-radio-group
        v-model="currentNode"
        size="small"
        @change="() => emit('update:currentNode', currentNode)"
        class="!w-60%"
      >
        <el-radio-button v-for="(item, index) in nodeList" :key="index" :value="item.id">
          {{ item.label }}-{{ stageCount[item.id] || 0 }}
        </el-radio-button>

        <el-radio-button
          :label="`所有活动-${totalStageCount}`"
          :value="-1"
          v-if="props.type === 'activities'"
        />
      </el-radio-group>
      <div class="header-right !w-20%">
        <!-- <el-switch
          v-model="currentFixed"
          size="small"
          inline-prompt
          active-text="固定"
          inactive-text="滚动"
        /> -->
        <el-radio-group size="small" v-model="currentView">
          <el-radio-button label="table">
            <el-tooltip content="表格视图">
              <Icon icon="ep:grid" />
            </el-tooltip>
          </el-radio-button>
          <!-- <el-radio-button label="list">
            <el-tooltip content="列表视图">
              <Icon icon="ep:list" />
            </el-tooltip>
          </el-radio-button> -->
        </el-radio-group>
      </div>
    </div>
    <div class="activities-body">
      <div class="h-5% flex items-center activities-buttons" v-if="$slots['activities-buttons']">
        <slot name="activities-buttons"></slot>
      </div>
      <div class="activities-content">
        <slot name="activities-table" v-if="currentView === 'table'"></slot>
        <slot name="activities-list" v-else-if="currentView === 'list'"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics'
import { PropType } from 'vue'
const currentView = ref('table')

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>,
    required: true
  },
  nodeList: {
    type: Array as PropType<any[]>,
    required: true
  },
  currentNode: {
    type: [Number, String],
    default: undefined
  },
  stageCount: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  type: {
    type: String
  }
})
const currentNode = ref(props.currentNode)

const emit = defineEmits(['update:currentNode'])

const totalStageCount = computed(() => {
  return Object.values(props.stageCount)?.reduce((sum: number, value) => sum + Number(value), 0)
})

watch(
  () => props.currentNode,
  (val) => {
    if (val) {
      currentNode.value = val
    }
  }
)
</script>

<style lang="scss" scoped>
.activities-container {
  background-color: #ebeff3;
  height: calc(100vh - 2.4vw - 2rem - 90px);

  .activities-header {
    display: flex;
    justify-content: space-between;
    // border-bottom: 0.3px solid #f1f1f1;
    // background-color: #fff;
    max-height: 2rem;
    & > * {
      justify-content: center;
    }

    .current-project-name {
      font-size: 0.9rem;
      font-weight: bold;
      color: var(--regular-text-color);
      letter-spacing: 2px;
      padding: 5px 5px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 20%;
    }

    :deep(.el-radio-button__inner) {
      border: 1px solid #f1f1f1 !important;
      // background-color: #ebeff3;
      font-size: 0.9rem;
      padding: 5px 5px;

      i {
        font-size: 0.9rem !important;

        .el-tooltip__trigger {
          font-size: 0.9rem !important;
        }
      }
    }

    .header-right {
      display: flex;
      justify-content: flex-end;
      // .el-radio-group{
      //   position: absolute;
      //   top: 0;
      //   right: 0;
      // }
    }
  }

  .activities-body {
    height: 100%;

    .activities-content {
      padding: 0.5vw;
      padding-top: 0;
      background-color: #fff;
      height: 100%;
    }
  }
}

:deep(.el-switch) {
  height: 1.8rem;

  .el-switch__core {
    height: 1.4vw;
    width: 3.2vw;

    .el-switch__inner .is-text {
      font-size: 1rem;
    }

    .el-switch__action {
      height: 1vw;
      width: 1vw;
    }
  }

  &.is-checked .el-switch__core .el-switch__action {
    left: calc(100% - 1vw);
  }
}

.activities-buttons {
  padding: 0 20px;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  border: 0.3px solid #ececec;
  border-bottom: none;
  background-color: #fff;
}

.activities-buttons > :deep(button) {
  height: 1.5rem;
  font-size: 0.8rem;

  i {
    font-size: 0.8rem !important;
    span {
      font-size: 0.8rem !important;
    }
  }

  & + * {
    margin-left: 0.7rem;
  }
}

.activities-buttons :deep(.vxe-toolbar) {
  height: 100%;
  padding: 0;

  .vxe-buttons--wrapper {
    & > * {
      height: 1.5rem;
      font-size: 0.8rem;
    }
  }

  .vxe-tools--operate {
    & > * {
      height: 1.5rem;
      width: 1.5rem !important;
      font-size: 0.8rem;
    }
  }
}
</style>
