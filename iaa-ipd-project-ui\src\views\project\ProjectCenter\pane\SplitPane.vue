<template>
  <div
    ref="splitPane"
    class="split-pane"
    :class="props.direction"
    :style="'flex-direction' + ':' + props.direction"
  >
    <div class="pane pane-one" :style="lengthType + ':' + paneLengthValue">
      <slot name="one"></slot>
    </div>
    <div
      class="pane pane-trigger"
      :style="lengthType + ':' + triggerLengthValue"
      @mousedown="handleMouseDown"
    >
    </div>
    <div class="pane pane-two" :style="lengthType + ':' + paneLengthValue1">
      <slot name="two"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  direction: {
    type: String,
    default: 'row'
  },
  triggerLength: {
    type: Number,
    default: 10
  },
  min: {
    type: Number,
    default: 10
  },
  max: {
    type: Number,
    default: 90
  },
  paneLengthPercent: {
    type: Number,
    default: 50
  }
})

const lengthType = computed(() => (props.direction === 'row' ? 'width' : 'height'))
const paneLengthValue = computed(
  () => `calc(${props.paneLengthPercent}% - ${props.triggerLength / 2}px)`
)
const paneLengthValue1=computed(()=>`calc(100% - ${props.paneLengthPercent}%)`)
const triggerLengthValue = computed(() => props.triggerLength + 'px')

const emits = defineEmits(['update:paneLengthPercent'])

const triggerLeftOffset = ref(0) //偏移量
const splitPane = ref() //按下时触发事件

/** 按下滑动器 */
const handleMouseDown = (e: any) => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)

  if (props.direction === 'row') {
    triggerLeftOffset.value = e.pageX - e.srcElement.getBoundingClientRect().left
  } else {
    triggerLeftOffset.value = e.pageY - e.srcElement.getBoundingClientRect().top
  }
}

/** 按下鼠标后移动鼠标 */
const handleMouseMove = (e: MouseEvent) => {
  // 计算一次 clientRect，避免频繁计算
  const clientRect = splitPane.value.getBoundingClientRect()

  // 使用 requestAnimationFrame 确保平滑更新
  requestAnimationFrame(() => {
    let paneLengthPercent = 0

    if (props.direction === 'row') {
      // 计算偏移量
      const offset = e.pageX - clientRect.left - triggerLeftOffset.value + props.triggerLength / 2
      // 确保偏移量在合理范围内
      paneLengthPercent = Math.max(
        props.min,
        Math.min(props.max, (offset / clientRect.width) * 100)
      )
    } else {
      // 计算偏移量
      const offset = e.pageY - clientRect.top - triggerLeftOffset.value + props.triggerLength / 2
      // 确保偏移量在合理范围内
      paneLengthPercent = Math.max(
        props.min,
        Math.min(props.max, (offset / clientRect.height) * 100)
      )
    }
    emits('update:paneLengthPercent', paneLengthPercent)
  })
}

/** 松开鼠标 */
const handleMouseUp = () => {
  document.removeEventListener('mousemove', handleMouseMove)
}
</script>

<style scoped lang="scss">
.split-pane {
  height: 100%;
  display: flex;
  &.row {
    .pane {
      height: 100%;
    }
    .pane-trigger {
      height: 100%;
      cursor: col-resize;
    }
  }
  &.column {
    .pane {
      width: 100%;
    }
    .pane-trigger {
      width: 100%;
      cursor: row-resize;
    }
  }
  // .pane-one{
  //   border-right: 1px dashed #ccc;
  // }
  .pane-trigger {
    user-select: none;
  }
  .pane-two {
    // border-left: 1px dashed #ccc;
    flex: 1;
  }
}
</style>
