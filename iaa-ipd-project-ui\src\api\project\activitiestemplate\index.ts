import request from '@/config/axios'

// 项目活动模板 API
export const ActivitiesTemplateApi = {
  /** 创建活动模板 */
  createActivitiesTemplate: async (data: any) => {
    return await request.post({ url: '/project/activities-template/create', data })
  },

  updateActivitiesTemplate: async (data: any) => {
    return await request.post({ url: '/project/activities-template/update', data })
  },

  /** 查询活动模板 */
  listActivitiesTemplate: async (data: any) => {
    return await request.post({ url: `/project/activities-template/list`, data })
  },
  deleteActivitiesTemplate: async (id: number) => {
    return await request.get({ url: `/project/activities-template/delete/${id}` })
  },
  /** 查询技术评审模板 */
  listActivitiesTechnical:async(category:string,basicsId:number)=>{
    return await request.get({ url: `/project/activities-template/list-technical/${category}/${basicsId}` })
  }
}
