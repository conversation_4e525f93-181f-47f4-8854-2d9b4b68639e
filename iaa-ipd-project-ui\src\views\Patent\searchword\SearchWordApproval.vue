<template>
  <el-form label-width="120px" :model="formData" :rules="formRules" ref="formRef">
    <el-form-item label="查询词" prop="word">
      <el-input
        v-model="formData.word"
        placeholder="请输入查询词"
        type="textarea"
        :rows="5"
        :disabled="!edit"
      />
    </el-form-item>
    <el-form-item label="查询时间范围" prop="startDate">
      <el-date-picker
        v-model="formData.startDate"
        value-format="YYYY-MM-DD"
        placeholder="开始时间"
        class="!w-[calc(50%-15px)]"
        @change="changeDate"
        :disabled="!edit"
      />
      ——
      <el-date-picker
        v-model="formData.endDate"
        value-format="YYYY-MM-DD"
        placeholder="结束时间"
        class="!w-[calc(50%-15px)]"
        :disabled-date="disabledDate"
        @change="changeDate"
        :disabled="!edit"
      />
    </el-form-item>
    <el-form-item v-if="props.modifyForm && !edit">
      <div class="w-full text-right">
        <el-button type="primary" @click="onEdit">修改</el-button>
      </div>
    </el-form-item>
    <el-form-item v-if="formData.wordHistory?.length > 0" label="历史版本">
      <vxe-table
        :data="formData.wordHistory"
        class="!w-full"
        align="center"
        border
        stripe
        show-overflow
        :header-cell-config="{ height: 40 }"
        :cell-config="{ height: 40 }"
      >
        <vxe-column title="查询词" field="word" align="left" width="60%" />
        <vxe-column title="开始时间" field="startDate" />
        <vxe-column title="结束时间" field="endDate" />
      </vxe-table>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { SearchWordFlowApi } from '@/api/bpm/patent/search-word'
import { propTypes } from '@/utils/propTypes'
import moment from 'moment'

const formData = ref({
  id: undefined,
  word: undefined,
  startDate: undefined,
  endDate: undefined,
  wordHistory: undefined as unknown as any[]
})

const formRules = reactive({
  word: [{ required: true, message: '请输入搜索词', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择时间范围', trigger: 'change' }]
})

const props = defineProps({
  modifyForm: propTypes.bool.def(false)
})

const { query } = useRoute()
const edit = ref(false)
const message = useMessage()

const getData = async () => {
  const processInstanceId = query.id || ''
  if (!processInstanceId) {
    return
  }
  const res = await SearchWordFlowApi.getByProcessInstanceId(processInstanceId as string)
  formData.value = res
}

const onEdit = () => {
  edit.value = true
  if (!formData.value.wordHistory || formData.value.wordHistory?.length == 0) {
    formData.value.wordHistory = []
  }
  formData.value.wordHistory.unshift({
    word: formData.value.word,
    startDate: formData.value.startDate,
    endDate: formData.value.endDate
  })
  formData.value.word = undefined
}

const updateForm = async () => {
  await SearchWordFlowApi.updateSearchWord(formData.value)
  edit.value = false
  return true
}

const disabledDate = (current: any) => {
  return moment(current).isBefore(moment(formData.value?.startDate))
}

const changeDate = () => {
  const { startDate, endDate } = formData.value

  if (startDate && endDate && moment(endDate).isBefore(moment(startDate))) {
    formData.value.endDate = undefined
    message.error('结束时间不能早于开始时间')
  }
}

defineExpose({
  updateForm
})

onMounted(() => {
  getData()
})
</script>
