<template>
  <!-- <SplitPane
    v-model:direction="direction"
    :min="controlMin"
    :max="100 - controlMin"
    :triggerLength="3"
    v-model:paneLengthPercent="paneLengthPercent"
  >
    <template #one>
      <tree-list-category
        ref="categoryListRef"
        @checked:project="handleCheckedProject"
        @storage="startDecrement"
        @reduction="reductionDecrementPaneLength"
        @checked:category:custom="handleCheckedCategoryCustom"
        v-model:treeListCategoryVisible="treeListCategoryVisible"
        :currentId="projectCurrentId"
        :currentCategory="projectCategory"
      />
    </template>
    <template #two>
      <main-page
        v-if="basicsInfo"
        ref="mainPageRef"
        v-model:basicsInfo="basicsInfo"
        v-model:custom="customActivities"
        v-model:templateCategory="currentTemplateCategory"
      />
      <el-empty v-else description="当前您没有选择项目，无法查看" class="h-full bg-white" />
    </template>
  </SplitPane> -->
  <vxe-split class="position-absolute top-0 right-0 bottom-10px left-0">
    <vxe-split-pane width="200px" min-width="200px">
      <tree-list-category
        ref="categoryListRef"
        @checked:project="handleCheckedProject"
        @storage="startDecrement"
        @reduction="reductionDecrementPaneLength"
        @checked:category:custom="handleCheckedCategoryCustom"
        v-model:treeListCategoryVisible="treeListCategoryVisible"
        :currentId="projectCurrentId"
        :currentCategory="projectCategory"
      />
    </vxe-split-pane>
    <vxe-split-pane min-width="60%" show-action>
      <main-page
        v-if="basicsInfo"
        ref="mainPageRef"
        v-model:basicsInfo="basicsInfo"
        v-model:custom="customActivities"
        v-model:templateCategory="currentTemplateCategory"
      />
      <el-empty v-else description="当前您没有选择项目，无法查看" class="h-full bg-white" />
    </vxe-split-pane>
  </vxe-split>
</template>

<script lang="ts" setup>
import TreeListCategory from './category/TreeListCategory.vue'
import MainPage from './main/index.vue'
import { cloneDeep, debounce } from 'lodash-es'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { BasicsApi, BasicsVO } from '@/api/project/basics'

const { wsCache } = useCache()
const direction = ref('row')
const paneLengthPercent = ref(12)
const controlMin = ref(12)
const categoryListRef = ref()
const mainPageRef = ref()
const treeListCategoryVisible = ref(true)
const customActivities = ref(false)
const currentTemplateCategory = ref('')
const projectCurrentId = ref<number>(undefined as unknown as number)
const projectCategory = ref<number>(undefined as unknown as number)
const basicsInfo = ref<BasicsVO>()
const permissionList = ref<any[]>([])
const { query } = useRoute()
const { push } = useRouter()

const handleCheckedProject = (projectId: number, categoryId: number) => {
  wsCache.set(CACHE_KEY.PROJECT_CURRENT_ID, projectId)
  wsCache.set(CACHE_KEY.PROJECT_CURRENT_CATEGORY, categoryId)
  getBasicsInfo(projectId)
}

/** 获取项目基础信息 */
const getBasicsInfo = async (basicsId: number) => {
  const res = await BasicsApi.getBasics(basicsId)
  basicsInfo.value = res
  wsCache.set('PROJECT_BASICS_INFO', basicsInfo.value)
  getPermissions(basicsId)
}
const getPermissions = debounce(async (basicsId: number) => {
  const res = await BasicsApi.getPermission(basicsId)
  permissionList.value = res
  wsCache.set('PROJECT_PERMISSION', permissionList.value)
}, 300)

const handleCheckedCategoryCustom = (custom: boolean, templateCategory: string) => {
  customActivities.value = custom
  currentTemplateCategory.value = templateCategory
}
/** 还原项目列表 */
const reductionDecrementPaneLength = () => {
  direction.value = 'row'
  controlMin.value = 12
  paneLengthPercent.value = 12
  treeListCategoryVisible.value = true
}
/** 收纳项目列表 */
const startDecrement = async () => {
  await decrementPaneLengthAsync()
  // 在这里执行其他内容
  direction.value = 'column'
  controlMin.value = 3
  paneLengthPercent.value = 3
  treeListCategoryVisible.value = false
}
/**缩放并改变布局 */
const decrementPaneLengthAsync = async () => {
  controlMin.value = 0
  const size = cloneDeep(paneLengthPercent.value)
  let index = 0

  while (index < size) {
    paneLengthPercent.value--
    index++

    if (paneLengthPercent.value <= 5) {
      treeListCategoryVisible.value = false
    }

    // 设置时间间隔，例如每 500 毫秒执行一次
    await new Promise((resolve) => setTimeout(resolve, 10))
  }
}

onMounted(() => {
  if (
    query.categoryId &&
    query.basicsId &&
    query.page &&
    ((query.stage && query.id) || query.type)
  ) {
    wsCache.set('project_page_show_form', {
      categoryId: Number(query.categoryId),
      basicsId: Number(query.basicsId),
      page: query.page as string,
      stage: query?.stage as string,
      id: Number(query?.id),
      type: query?.type as string
    })
    push({
      name: 'ProjectCenter'
    })
  }

  const form = wsCache.get('project_page_show_form')
  if (form) {
    projectCurrentId.value = Number(form.basicsId)
    projectCategory.value = Number(form.categoryId)
  } else {
    projectCurrentId.value = wsCache.get(CACHE_KEY.PROJECT_CURRENT_ID)
    projectCategory.value = wsCache.get(CACHE_KEY.PROJECT_CURRENT_CATEGORY)
  }

})
</script>

<style lang="scss" scoped>
.split-pane {
  height: calc(100vh - 80px);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
}
</style>
