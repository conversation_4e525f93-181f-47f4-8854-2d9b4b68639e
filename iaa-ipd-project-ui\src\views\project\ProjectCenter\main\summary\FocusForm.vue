<template>
  <el-drawer v-model="visiable" title="重点关注任务" size="60%" destroy-on-close>
    <el-transfer
      v-model="formData.activitiesIds"
      filterable
      :data="activitiesList"
      :props="{ label: 'tempTitle', key: 'id' }"
      :titles="['项目任务列表', '重点关注任务']"
    />
    <template #footer>
      <el-button type="primary" @click="saveFocus">保存</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { SummaryApi } from '@/api/project/summary'
import { getUserNickName } from '../../details/components/utils'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'

const activitiesList = ref<any[]>([])
const loading = ref(false)
const visiable = ref(false)
const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([])
})
const formData = ref({
  basicsId: undefined as unknown as number,
  activitiesIds: undefined as unknown as number[]
})
const message = useMessage()

const onListActivities = async (basicsId: number) => {
  loading.value = true
  try {
    const res = await SummaryApi.getActivitiesAll(basicsId)
    activitiesList.value = res
    activitiesList.value.forEach((item) => {
      item['tempTitle'] =
        `${item.orderNo} ${item.name} ${item.progress}% ${getUserNickName(props.userList, item.director)}`
    })
  } finally {
    loading.value = false
  }
}

const saveFocus = async () => {
  if (!formData.value.activitiesIds || formData.value.activitiesIds.length === 0) {
    message.error('请选择要关注的任务')
    return
  }
  await SummaryApi.saveFocus(formData.value)
  visiable.value = false
  emits('success')
}

const emits = defineEmits(['success'])

const openForm = async (basicsId: number, checkeds: number[]) => {
  await onListActivities(basicsId)
  visiable.value = true
  formData.value.activitiesIds = checkeds
  formData.value.basicsId = basicsId
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-transfer-panel) {
  width: calc(50% - 85px);
  height: 70vh;
}
:deep(.el-transfer-panel__body),
:deep(.el-transfer-panel__list) {
  height: calc(100% - 40px - 64px);
}
</style>
