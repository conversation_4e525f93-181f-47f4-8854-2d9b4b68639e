let option: any = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: []
  },
  xAxis: {},
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [],
  grid: {
    left: '2%', // 左边距
    right: '2%', // 右边距
    bottom: '2%', // 下边距
    containLabel: true // 包含刻度标签
  }
}

export const refreshChart = (title: string, type: string[], data: number[], myChart: any) => {
  option.legend = {
    data: [title]
  }
  option.xAxis = [
    {
      type: 'category',
      axisTick: { show: false },
      data: type
    }
  ]
  const series = [] as any[]

  series.push({
    name: title,
    type: 'bar',
    emphasis: {
      focus: 'series'
    },
    label: {
      show: true,
      position: 'top'
    },
    data: data
  })
  option.series = series
  option && myChart.setOption(option, true)
}

export const refreshChartMulti = (
  title: string,
  legend: string[],
  type: string[],
  customData: any[],
  myChart: any
) => {
  if (type.length == 0) {
    //暂无数据
    option = {
      title: {
        text: title + '暂无数据',
        x: 'center',
        y: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      }
    }
  } else {
    option['title'] = {
      text: title,
      left: 'left',
      textStyle: {
        color: 'rgb(51.2, 126.4, 204)',
        fontSize: 18,
        fontWeight: 'bold'
      }
    }
    option.yAxis = [
      {
        type: 'value'
      }
    ]
    option.legend = {
      data: legend
    }
    option.xAxis = [
      {
        type: 'category',
        axisTick: { show: false },
        data: type,
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      }
    ]
    option.series = customData
  }

  option && myChart.setOption(option, true)
}

export const refreshChartVertical = (
  title: string,
  type: string[],
  data: number[],
  myChart: any
) => {
  option['title'] = {
    text: title,
    left: 'left',
    textStyle: {
      color: 'rgb(51.2, 126.4, 204)',
      fontSize: 18,
      fontWeight: 'bold'
    }
  }
  option.xAxis = {
    type: 'value'
  }
  option.yAxis = {
    type: 'category',
    data: type,
    inverse: true
  }
  option.series = [
    {
      name: title,
      type: 'bar',
      data: data
    }
  ]
  option && myChart.setOption(option, true)
}

export const refreshOne = (title: string, type: string[], data: number[], myChart: any) => {
  option['title'] = {
    text: title,
    left: 'left',
    textStyle: {
      color: 'rgb(51.2, 126.4, 204)',
      fontSize: 18,
      fontWeight: 'bold'
    }
  }
  option.legend = {
    data: [title]
  }
  option.xAxis = [
    {
      type: 'category',
      axisTick: { show: false },
      data: type
    }
  ]
  option.yAxis = [
    {
      type: 'value'
    }
  ]
  option.series = [
    {
      name: title,
      type: 'bar',
      data: data,
      itemStyle: {
        normal: {
          color: '#63b4ff'
        }
      }
    }
  ]
  option && myChart.setOption(option, true)
}
