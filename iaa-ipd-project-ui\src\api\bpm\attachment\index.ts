import request from '@/config/axios'

export const AttachmentFlowApi = {
  /** 创建输出物流程 */
  createAttachmentFlow: (data: any) => {
    return request.post({ url: '/bpm/attachment/create', data })
  },
  /**获取输出物流程信息 */
  getAttachmentFlow: (id: string) => {
    return request.get({ url: `/bpm/attachment/get/${id}` })
  },
  /** 设置文件引用 */
  createAttachmentFlowBatch: (data: any) => {
    return request.post({ url: '/bpm/attachment/create-batch', data })
  }
}
