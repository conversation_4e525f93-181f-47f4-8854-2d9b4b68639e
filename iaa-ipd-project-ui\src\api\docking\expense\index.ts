import request from '@/config/axios'

export const ExpenseApi = {
  // 获取ERP费用列表
  getErpList: (params: any) => {
    return request.get({ url: '/docking/erp-expense/list', params })
  },
  // 获取Ekuaibao费用列表
  getEkuaibaoList: (params: any) => {
    return request.get({ url: '/docking/ekuaibao-expense/list', params })
  },
  // 获取项目成本及费用信息统计
  getExpenseList: (params: any) => {
    return request.get({ url: '/docking/project-expense/list', params })
  }
}
