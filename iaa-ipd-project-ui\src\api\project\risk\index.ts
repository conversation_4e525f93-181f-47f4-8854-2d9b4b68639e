import request from '@/config/axios'

// 项目风险信息 VO
export interface RiskVO {
  id?: number //风险ID
  basicsId?: number //项目id
  stage?: number // 所属阶段
  category?: string // 风险分类
  content?: string // 风险描述
  consequence?: string //风险后果
  affect?: string // 风险影响
  probability?: string // 风险概率
  level?: string // 风险等级
  solutionsPlan?: string // 解决方案计划
  director?: number[] // 责任人
  sort?: number // 排序
  status?: number // 状态
  onsetDate?: Date // 发生时间
  solutionsActual?: string // 解决方案实际
  processInstanceId?: string // 当前流程
  creator?: number[] //提出人
}

export const RiskApi = {
  // 查询风险列表
  getRiskList: async (params: any) => {
    return await request.get({ url: '/project/risk/list', params })
  },
  // 添加风险
  createRisk: async (data: RiskVO) => {
    return await request.post({ url: '/project/risk/create', data })
  },
  // 更新风险反馈
  updateRiskFeedback: async (data: any) => {
    return await request.post({ url: '/project/risk/update-feedback', data })
  },
  // 查询单个风险
  getRisk: async (id: number) => {
    return await request.get({ url: `/project/risk/get/${id}` })
  },
  // 导出风险
  exportRisk: async (data: any) => {
    return await request.downloadPost({ url: `/project/risk/export`, data })
  },
  // 获取阶段数量
  getStageCount: async (basicsId: number) => {
    return await request.get({ url: `/project/risk/get-stage-count/${basicsId}` })
  },
  getRiskPage: async (data: any) => {
    return await request.post({ url: `/project/risk/page`, data })
  },
  exportRiskMulti: async (data: any) => {
    return await request.downloadPost({ url: `/project/risk/export-multi`, data })
  }
}
