import { getIntDictOptions, getStrDictOptions } from '@/utils/dict'

export const textFilterRender = reactive({
  name: 'TextFilter'
})

export const userFilterRender = reactive({
  name: 'UserFilter'
})

export const userOrDeptFilterRender = reactive({
  name: 'UserOrDeptFilter',
  props:{
    default:'dept'
  }
})

export const dateRangeFilterRender = reactive({
  name:'DateRangeFilter'
})

export const statusOptions = computed<Array<{ label: string; value: any }>>(() => {
  return (
    getIntDictOptions('project_activities_status')?.map((dict) => {
      return {
        label: dict.label,
        value: dict.value
      }
    }) || []
  )
})

export const problemLevelOptions = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('project_problem_level')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const problemCategoryOptions = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('project_problem_category')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const problemModuleOptions = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('project_problem_module')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

export const riskLevelOptions = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('project_risk_level')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})
