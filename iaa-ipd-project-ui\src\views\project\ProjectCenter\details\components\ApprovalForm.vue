<template>
  <el-form
    :model="formData"
    :rules="formRules"
    size="small"
    label-width="170px"
    label-position="left"
  >
    <el-form-item
      v-for="(template, index) in dataTemplate"
      :key="template.value"
      :label="template.label"
      :prop="template.value"
      :class="`form-item${index % 2}`"
    >
      <el-card class="w-full" shadow="never">
        <template #header>
          <div class="w-full flex justify-between">
            <el-select
              v-model="formData[template.value].value"
              filterable
              @change="
                () => {
                  onApproveChange(template.value)
                  formDataChange(
                    template.value,
                    template.label,
                    JSON.stringify(formData[template.value].value!),
                    JSON.stringify(
                      props.propFormData.approve?.find((el) => el.type == template.value)
                        .processKey!
                    )
                  )
                }
              "
              :disabled="!props.edit"
              class="!w-40% flow-select"
              placeholder=""
              size="default"
            >
              <el-option
                v-for="item in definitionList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              />
            </el-select>
            <el-checkbox
              v-model="formData[template.value].effectiveness"
              label="生效"
              :disabled="!props.edit"
              v-if="
                !template.value.includes('Complete') &&
                !template.value.includes('Target') &&
                !template.value.includes('Crux')
              "
            />
          </div>
        </template>

        <div class="w-full">
          <div class="flex w-full bg-#faf5f3 text-.9rem">
            <div class="w-33% td">流程节点</div>
            <div class="w-33% td">流程审批人类型</div>
            <div class="w-33% td">审批人</div>
          </div>
          <div class="flex" v-for="node in formData[template.value].node" :key="node.taskId">
            <div class="w-33% td">{{ node.taskName }}</div>
            <div class="w-33% td">
              <el-select
                v-model="node.userType"
                @change="node.userIds = undefined"
                :disabled="!props.edit"
              >
                <el-option label="项目角色" :value="0" />
                <el-option label="流程用户分组" :value="1" />
                <el-option
                  label="负责人/提出人"
                  :value="2"
                  v-if="!['additionalTarget', 'conferenceTarget'].includes(template.value)"
                />
                <el-option
                  label="评审团队"
                  :value="3"
                  v-if="template.value == 'additionalTarget'"
                />
                <el-option
                  label="外部专家团"
                  :value="4"
                  v-if="template.value == 'additionalTarget'"
                />
              </el-select>
            </div>
            <div class="w-33% td">
              <el-select
                v-model="node.userIds"
                multiple
                collapse-tags
                collapse-tags-tooltip
                :disabled="!props.edit"
              >
                <template v-if="node.userType === 0">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </template>
                <template v-if="node.userType === 1">
                  <el-option
                    v-for="item in userGroupList"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  />
                </template>
              </el-select>
            </div>
          </div>
        </div>
        <!-- <vxe-table
          class="!w-full"
          size="mini"
          :header-cell-style="{
            padding: 0,
            backgroundColor: '#fafafa',
            color: '#969696',
            height: '30px'
          }"
          :cell-style="{ padding: 0, height: '30px' }"
          :data="formData[template.value].node"
          border
          show-overflow
          :column-config="{ resizable: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
        >
          <vxe-column title="流程节点" field="taskName" />
          <vxe-column title="审批人类型" field="userType">
            <template #default="{ row }">
              <el-select
                v-model="row.userType"
                @change="row.userIds = undefined"
                :disabled="!props.edit"
              >
                <el-option label="项目角色" :value="0" />
                <el-option label="流程用户分组" :value="1" />
                <el-option
                  label="负责人/提出人"
                  :value="2"
                  v-if="!['additionalTarget', 'conferenceTarget'].includes(template.value)"
                />
                <el-option
                  label="评审团队"
                  :value="3"
                  v-if="template.value == 'additionalTarget'"
                />
                <el-option
                  label="外部专家团"
                  :value="4"
                  v-if="template.value == 'additionalTarget'"
                />
              </el-select>
            </template>
          </vxe-column>
          <vxe-column title="审批人" field="userIds">
            <template #default="{ row }">
              <el-select
                v-model="row.userIds"
                v-if="row.userType === 0"
                multiple
                collapse-tags
                collapse-tags-tooltip
                :disabled="!props.edit"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <el-select
                v-model="row.userIds"
                v-else-if="row.userType === 1"
                multiple
                collapse-tags
                collapse-tags-tooltip
                :disabled="!props.edit"
              >
                <el-option
                  v-for="item in userGroupList"
                  :key="item.id"
                  :label="item.name"
                  :value="String(item.id)"
                />
              </el-select>
            </template>
          </vxe-column>
        </vxe-table> -->
      </el-card>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import * as DefinitionApi from '@/api/bpm/definition'
import { propTypes } from '@/utils/propTypes'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import * as UserGroupApi from '@/api/bpm/userGroup'
import { BasicsVO } from '@/api/project/basics'
import { copyValueToTarget } from '@/utils'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { ApprovalTemplate } from './ApprovalTemplate'

const props = defineProps({
  edit: propTypes.bool.def(true),
  propFormData: {
    type: Object as PropType<BasicsVO>,
    default: () => {}
  }
})

const message = useMessage()
const definitionList = ref<any[]>([]) // 流程列表
const dataTemplate = ref([
  { label: '活动修改审批', value: 'activities' },
  { label: '关键活动修改审批', value: 'activitiesCrux' },
  { label: '活动删除审批', value: 'activitiesDelete' },
  { label: '活动完成审批', value: 'activitiesComplete' },
  { label: '问题提出审批', value: 'problemCreate' },
  { label: '问题修改审批', value: 'problem' },
  { label: '问题删除审批', value: 'problemDelete' },
  { label: '问题输出审批', value: 'problemTarget' },
  { label: '问题完成审批', value: 'problemComplete' },
  { label: '风险创建审批', value: 'riskCreate' },
  { label: '风险修改审批', value: 'risk' },
  { label: '风险删除审批', value: 'riskDelete' },
  { label: '风险输出审批', value: 'riskTarget' },
  // { label: '风险完成审批', value: 'riskComplete' },
  { label: '技术评审修改审批', value: 'tr' },
  { label: '技术评审删除审批', value: 'trDelete' },
  { label: '技术评审输出审批', value: 'trTarget' },
  { label: '技术评审完成审批', value: 'trComplete' },
  { label: '技术评审报告上传审批', value: 'additionalTarget' },
  { label: '会议纪要上传审批', value: 'conferenceTarget' },
  { label: '文件引用审批', value: 'quoteTarget' },
])

const formData = ref<any>(ApprovalTemplate)
const formRules = reactive({})
dataTemplate.value.forEach((item) => {
  if (!formData.value[item.value]) {
    formData.value[item.value] = {
      value: '',
      hasCrux: false,
      effectiveness: true,
      node: []
    }
  }

  formRules[item.value] = [
    {
      required: computed(() => formData.value?.[item.value]?.effectiveness),
      message: `必须选择${item.label}流程`,
      trigger: 'change'
    }
  ]
})
const userGroupList = ref<any[]>([]) // 流程用户分组列表
// 表单修改记录
const modifyInfo = ref<BasicsModifyInfoVO[]>([])

/** 加载所有的流程 */
const onListDefinition = async () => {
  const res = await DefinitionApi.getProcessDefinitionList({
    suspensionState: 1,
    category: 'a_p_r_tr_bpm'
  })
  definitionList.value = res
}

/** 切换流程 */
const onApproveChange = async (type: string) => {
  const userTask = await DefinitionApi.getProcessDefinition(undefined, formData.value[type].value)
  formData.value[type].node = userTask?.startUserSelectTasks?.map((el) => {
    return {
      taskId: el.id,
      taskName: el.name,
      userType: undefined,
      userIds: undefined
    }
  })
}

/** 加载所有的流程用户分组 */
const onListUserGroup = async () => {
  userGroupList.value = await UserGroupApi.getUserGroupSimpleList()
}

/** 表单校验 */
const emit = defineEmits(['update:activeName'])
/** 将teamMap的值赋值给formData */
const validate = () => {
  let data = {} as any
  copyValueToTarget(data, props.propFormData)
  const keys = Object.keys(formData.value)
  const approveData: any[] = []

  for (const key of keys) {
    if (!formData.value[key].value && formData.value[key].effectiveness) {
      message.error('请选择审批流程')
      emit('update:activeName', 'approval')
      throw new Error('请选择审批流程')
      return // 提前终止函数执行
    }
    if (formData.value[key].value) {
      approveData.push({
        type: key,
        hasCrux: formData.value[key].hasCrux,
        effectiveness: formData.value[key].effectiveness,
        processKey: formData.value[key].value,
        node: formData.value[key].node
      })
    }
  }

  data.approve = approveData
  Object.assign(props.propFormData, data)
}

/** 项目信息变更 */
const formDataChange = (key: string, title: string, currentValue: string, beforeValue: string) => {
  if (currentValue == beforeValue) {
    modifyInfo.value = modifyInfo.value.filter((item) => item.modifyField !== key)
    return
  }
  modifyInfo.value.push({
    modifyField: key,
    modifyFieldName: title,
    beforeValue: beforeValue,
    afterValue: currentValue
  })
}

watch(
  () => props.propFormData?.id,
  () => {
    if (!props.propFormData) return

    props.propFormData.approve?.forEach((el: any) => {
      if (!formData.value[el.type]) return
      formData.value[el.type].value = el.processKey
      formData.value[el.type].hasCrux = el.hasCrux
      formData.value[el.type].effectiveness = el.effectiveness
      formData.value[el.type].node = el.node
    })
  },
  { immediate: true }
)

const getChangeMsg = () => {
  return modifyInfo.value
}

defineExpose({
  validate,
  getChangeMsg
})

onMounted(() => {
  onListDefinition()
  onListUserGroup()
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-size: 0.9rem;
  font-weight: bold;
  color: var(--el-color-info);
  text-shadow:
    0 1px 2px rgba(0, 0, 0, 0.15),
    0 0 5px rgba(255, 255, 255, 0.2);
}
:deep(.flow-select) {
  .el-select__wrapper {
    background-color: #f1f1f1 !important;
  }

  .el-select__selected-item {
    text-shadow:
      0 1px 2px rgba(0, 0, 0, 0.15),
      0 0 3px rgba(255, 255, 255, 0.2);
    color: var(--el-color-info) !important;
    -webkit-text-fill-color: var(--el-color-info) !important;
    // color: #fff !important;
    // -webkit-text-fill-color: #fff !important;
  }
}

// :deep(.bg-f1f9ff0) {
//   .el-select__wrapper {
//     background-color: var(--el-color-warning-light-9) !important;
//   }
// }

// :deep(.bg-f1f9ff1) {
//   .el-select__wrapper {
//     background-color: var(--el-color-primary-light-9) !important;
//   }
// }

// :deep(.el-form-item) {
//   .el-select__selected-item {
//     color: #131313;
//     -webkit-text-fill-color: #131313;
//   }

//   .is-transparent {
//     color: #ccc !important;
//     -webkit-text-fill-color: #ccc !important;
//   }
// }

:deep(.vxe-table--body-wrapper) {
  min-height: 30px !important;
}

// :deep(.form-item0) {
//   // border: 1px dashed var(--el-color-warning-light-7);
//   // 添加阴影效果（可调整参数）
//   box-shadow:
//     0 2px 4px var(--el-color-warning-light-7),    // 外部阴影
//     inset 0 0 0 1px rgba(0, 0, 0, 0.08); // 内阴影模拟边框
//   margin-bottom: 15px !important;

//   // 可选：添加轻微圆角提升现代感
//   border-radius: 4px;
//   margin-bottom: 15px !important;
// }

// :deep(.form-item1) {
//   // border: 1px dashed var(--el-color-primary-light-7);
//   box-shadow:
//     0 2px 4px var(--el-color-primary-light-7),    // 外部阴影
//     inset 0 0 0 1px rgba(0, 0, 0, 0.08); // 内阴影模拟边框
//   margin-bottom: 15px !important;

//   // 可选：添加轻微圆角提升现代感
//   border-radius: 4px;
//   margin-bottom: 15px !important;
// }

:deep(.el-card__header) {
  padding: 5px;
}

:deep(.el-card__body) {
  padding: 0px;
}

:deep(.el-select__wrapper) {
  &,
  &:hover {
    box-shadow: none;
  }
}

.td{
  border-left: .3px solid #ddd;
  border-bottom: .3px solid #ddd;
}
</style>
