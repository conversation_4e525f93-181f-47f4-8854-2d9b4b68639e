import request from '@/config/axios'

export interface CommentSaveReqVO {
  id?: number // 评论ID
  moduleId?: string // 模块ID
  parentId?: number // 父评论ID
  replyCommentId?: number // 回复评论ID
  content?: string // 评论内容
  imgUrls?: string | string[] // 图片地址
}

export interface CommentRespVO {
  id?: number
  moduleId?: number
  parentId?: number
  replyCommentId?: number
  replayNickName?: string
  content?: string
  nickname?: string
  avatar?: string
  imgUrls?: string | string[]
  createTime?: Date
  hasDeleted?: boolean
  children?: CommentRespVO[]
}

export const CommentApi = {
  // 查询评论列表
  getCommentPage: (params: PageParam) => {
    return request.post({ url: '/infra/comment/list', params })
  },
  //添加评论
  createComment: (data: CommentSaveReqVO) => {
    return request.post({ url: '/infra/comment/add', data })
  },
  //删除评论
  deleteComment: (id: number) => {
    return request.delete({ url: `/infra/comment/delete/${id}` })
  },
  getMentionPage: (params: any) => {
    return request.post({ url: '/infra/comment/list-mention', params })
  },
  viewMention: (params: any) => {
    return request.get({ url: '/infra/comment/view-mention', params })
  }
}
