export const ApprovalTemplate = {
  activities: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['lpdt']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: []
      }
    ]
  },
  activitiesCrux: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 1,
        userIds: ['131']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: []
      }
    ]
  },
  activitiesDelete: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['lpdt']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 1,
        userIds: ['131']
      }
    ]
  },
  activitiesComplete: {
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pqa']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: []
      }
    ]
  },
  problemCreate: {
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: []
      }
    ]
  },
  problem: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: []
      }
    ]
  },
  problemDelete: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: ['pqa']
      }
    ]
  },
  problemTarget: {
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 2,
        userIds: []
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: []
      }
    ]
  },
  problemComplete: {
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: ['pqa']
      }
    ]
  },
  riskCreate: {
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: ['se']
      }
    ]
  },
  risk: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: []
      }
    ]
  },
  riskDelete: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pqa']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: []
      }
    ]
  },
  riskTarget: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: ['se']
      }
    ]
  },
  tr: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: false,
    node: []
  },
  trDelete: {
    value: 'modifying_and_deleting_objects',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_1q6p7da',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pqa']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_092no4c',
        userType: 0,
        userIds: []
      }
    ]
  },
  trTarget:{
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 2,
        userIds: []
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: []
      }
    ]
  },
  trComplete:{
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['se']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: ['pqa']
      }
    ]
  },
  additionalTarget:{
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['se']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: ['lpdt']
      }
    ]
  },
  conferenceTarget:{
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['lpdt']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: []
      }
    ]
  },
  quoteTarget:{
    value: 'common_completion_process',
    hasCrux: false,
    effectiveness: true,
    node: [
      {
        taskId: 'Activity_0wximze',
        taskName: '系统默认',
        userType: 0,
        userIds: ['pm']
      },
      {
        taskName: '系统默认',
        taskId: 'Activity_0lgd2sy',
        userType: 0,
        userIds: []
      }
    ]
  }
}
