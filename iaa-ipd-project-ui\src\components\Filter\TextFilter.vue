<template>
  <div v-if="currOption" class="my-filter-input">
    <el-input
      mode="text"
      v-model="currOption.data"
      placeholder="支持回车筛选"
      @keyup.enter="keyupEvent"
      @input="changeOptionEvent"
    />
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { watch, ref, computed } from 'vue'
import type { VxeTableDefines } from 'vxe-table'

const props = defineProps({
  renderParams: propTypes.any.def({})
})

const currOption = ref<VxeTableDefines.FilterOption>()

const currField = computed(() => {
  const { column } = props.renderParams || {}
  return column ? column.field : ''
})

const load = () => {
  const { renderParams } = props
  if (renderParams) {
    const { column } = renderParams
    const option = column.filters[0]
    currOption.value = option
  }
}

const changeOptionEvent = () => {
  const { renderParams } = props
  const option = currOption.value
  if (renderParams && option) {
    const { $table } = renderParams
    const checked = !!option.data
    $table.updateFilterOptionStatus(option, checked)
  }
}

const keyupEvent = ($event) => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.confirmFilterEvent($event)
  }
}

watch(currField, () => {
  load()
})

load()
</script>

<style scoped>
.my-filter-input {
  padding: 10px;
}
</style>
