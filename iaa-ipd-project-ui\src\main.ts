// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'
import 'dayjs/locale/zh-cn'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 权限
import { setupAuth } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患

// 引入vxe-table
import VxeUIAll from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'

import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'

import VxeUIPluginExportXLSX from '@vxe-ui/plugin-export-xlsx'
import ExcelJs from 'exceljs'

VxeUIAll.VxeUI.use(VxeUIPluginExportXLSX,{ExcelJs})

// 完整导入 右键菜单
import ContextMenu from 'vue3-contextmenu'
import 'vue3-contextmenu/dist/vue3-contextmenu.css'


import '@/renderer/FilterRenderer'

// 创建实例
const setupAll = async () => {
  const app = createApp(App)
  // 禁用warn日志
  app.config.warnHandler = () =>null

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  setupAuth(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)

  app.use(VxeUIAll)

  app.use(VxeUITable)

  app.use(ContextMenu)

  app.mount('#app')
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
