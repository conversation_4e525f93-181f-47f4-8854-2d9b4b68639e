<template>
  <vxe-split-pane width="300px" min-width="300px">
    <div class="p-10px">
      <el-form class="custom-form" label-width="110px">
        <el-divider>项目基础信息</el-divider>
        <el-form-item label="项目名称">
          <el-input placeholder="请输入项目名称" v-model="formData.name" />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select v-model="formData.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目等级">
          <el-select v-model="formData.level" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_level')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目平台">
          <el-select v-model="formData.platform" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_platform')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型">
          <el-select v-model="formData.mold" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_type')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-divider>项目团队信息</el-divider>
        <template v-for="dict in getStrDictOptions('project_team_role')" :key="dict.value">
          <el-form-item :label="dict.label">
            <UserAvatarList
              v-model="formData.team[dict.value]"
              :user-list="userList"
              :size="28"
              :limit="3"
            />
          </el-form-item>
        </template>
      </el-form>
    </div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div class="p-10px h-full">
      <vxe-toolbar custom size="mini" ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" @click="onList"> 查询 </el-button>
          <el-button type="warning" size="small" @click="refresh"> 重置 </el-button>
        </template>
        <template #tools>
          <el-button type="primary" size="small" @click="handleExport">导出</el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        height="90%"
        :header-cell-style="{ padding: '0', height: '34px' }"
        :cell-style="{ padding: '0', height: '34px' }"
        show-overflow
        align="center"
        border
        ref="tableRef"
        :data="basicsList"
        :loading="loading"
        :export-config="{
          remote: true,
          exportMethod: handleExport
        }"
      >
        <vxe-column title="项目名称" field="name" align="left" width="200px" />
        <vxe-column title="项目等级" field="level" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.level) }}
          </template>
        </vxe-column>
        <vxe-column title="项目平台" field="platform" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_platform', row.platform) }}
          </template>
        </vxe-column>
        <vxe-column title="项目类型" field="mold" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_type', row.mold) }}
          </template>
        </vxe-column>
        <vxe-column title="状态" field="status" width="100px">
          <template #default="{ row }">
            {{ getDictLabel('project_status', row.status) }}
          </template>
        </vxe-column>
        <vxe-column title="进度" field="progress" width="100px" />
        <vxe-column
          v-for="dict in getStrDictOptions('project_team_role')"
          :key="dict.value"
          :title="dict.label"
          :field="dict.value"
          width="100px"
        >
          <template #default="{ row }">
            {{ getUserNickName(row.teams?.find((item) => item.role === dict.value)?.userIds) }}
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { getIntDictOptions, getStrDictOptions, getDictLabel } from '@/utils/dict'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { BasicsApi, BasicsVO } from '@/api/project/basics'
import download from '@/utils/download'

const userList = ref<UserVO[]>([])
const toolbarRef = ref()
const tableRef = ref()
const basicsList = ref<BasicsVO[]>([])
const loading = ref(false)
const message = useMessage()

const formData = ref({
  name: undefined,
  status: undefined,
  level: undefined,
  platform: undefined,
  mold: undefined,
  team: {} as any
})
const onListUser = async () => {
  userList.value = await getSimpleUserList()
}

const onList = async () => {
  loading.value = true
  try {
    const res = await BasicsApi.getBasicsList(formData.value)
    basicsList.value = res
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    name: undefined,
    status: undefined,
    level: undefined,
    platform: undefined,
    mold: undefined,
    team: {} as any
  }
  onList()
}

/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname)
    .join(',')
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    loading.value = true
    const data = await BasicsApi.export(formData.value)
    download.excel(data, '项目信息.xlsx')
  } catch {
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  onListUser()
  onList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>
