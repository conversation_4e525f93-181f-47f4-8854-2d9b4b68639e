<template>
  <!-- 代理机构表格 -->
  <div class="flex justify-between items-center">
    <card-title title="代理机构" />
    <el-button type="primary" size="small" plain @click="handleAddAgency"> 新增 </el-button>
  </div>
  <vxe-table
    :data="agencyList"
    border
    size="small"
    class="custom-table"
    :header-cell-config="{ height: 40 }"
    :cell-config="{ height: 40 }"
  >
    <vxe-column fleld="agencyName" title="代理机构名称" min-width="150" />
    <vxe-column fleld="agencyCode" title="代理机构代码" min-width="150" />
    <vxe-column fleld="agencyAddress" title="代理机构地址" min-width="120" />
    <vxe-column fleld="agencyPhone" title="代理机构电话" min-width="120" />
    <vxe-column title="操作" width="150" align="center">
      <template #default="{ row, rowIndex }">
        <el-button type="danger" link size="small" @click="hanldeDeleteRow(row, rowIndex)">
          删除
        </el-button>
      </template>
    </vxe-column>
  </vxe-table>

  <!-- 代理机构编辑对话框 -->
  <Dialog
    v-model="agencyDialogVisible"
    :title="agencyDialogTitle"
    width="500px"
    @close="resetAgencyForm"
  >
    <el-form
      ref="agencyFormRef"
      :model="agencyForm"
      :rules="agencyRules"
      label-width="120px"
      size="small"
    >
      <el-form-item label="代理机构名称" prop="agencyName">
        <el-input v-model="agencyForm.agencyName" />
      </el-form-item>
      <el-form-item label="代理机构代码" prop="agencyCode">
        <el-input v-model="agencyForm.agencyCode" />
      </el-form-item>
      <el-form-item label="代理机构地址" prop="agencyAddress">
        <el-input v-model="agencyForm.agencyAddress" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="代理机构电话" prop="agencyPhone">
        <el-input v-model="agencyForm.agencyPhone" type="number" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="saveAgency(true)">继续添加</el-button>
      <el-button type="primary" @click="saveAgency()">添加</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { SubclassApi } from '@/api/patent/subclass'

const message = useMessage()
const loading = ref(false)
const props = defineProps({
  /** 所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析" */
  tableType: propTypes.oneOfType<0 | 1 | 2 | 3 | 4 | 5 | 6>([]).isRequired,
  belongsId: propTypes.number.isRequired, // 主表ID
  formType: propTypes.oneOfType<'create' | 'edit'>([]).isRequired
})

interface Agency {
  id?: number
  agencyName?: string
  agencyCode?: string
  agencyAddress?: string
  agencyPhone?: string
  tableType?: number
  belongsId?: number
}

// 代理机构数据
const agencyList = ref<Agency[]>([])

// 代理机构表单相关
const agencyDialogVisible = ref(false)
const agencyDialogTitle = ref('')
const agencyFormRef = ref()
const agencyForm = ref<Agency>({})
const agencyRules = {
  agencyName: [{ required: true, message: '请输入代理机构名称', trigger: 'blur' }]
}

const hanldeDeleteRow = async (row: Agency, rowIndex: number) => {
  if (row?.id) {
    await SubclassApi.deleteById(row.id, 'agency')
    onList()
  } else {
    agencyList.value.splice(rowIndex, 1)
  }
  message.success('删除成功')
}

// 代理机构相关方法
const handleAddAgency = () => {
  agencyDialogTitle.value = '新增代理机构'
  resetAgencyForm()
  agencyDialogVisible.value = true
}

const resetAgencyForm = () => {
  agencyForm.value = {}
}

const saveAgency = async (again: boolean = false) => {
  try {
    await agencyFormRef.value?.validate()
    agencyForm.value.tableType = props.tableType
    agencyForm.value.belongsId = props.belongsId
    if (props.belongsId && props.tableType) {
      await SubclassApi.batchCreate({
        tableType: props.tableType,
        belongsId: props.belongsId,
        agencyList: [agencyForm.value]
      })
      onList()
    } else {
      agencyList.value.push({ ...agencyForm.value })
    }
    resetAgencyForm()
    if (!again) {
      agencyDialogVisible.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const onList = async () => {
  if (!props.belongsId || !props.tableType) return
  loading.value = true
  try {
    const res = await SubclassApi.querySubclass({
      tableType: props.tableType,
      belongsId: props.belongsId,
      queryType: 'agency'
    })
    agencyList.value = res.list
  } finally {
    loading.value = false
  }
}

// 获取数据的方法
const getData = () => {
  return agencyList.value
}

// 暴露给父组件的方法
defineExpose({
  onList,
  getData
})
</script>

<style lang="scss" scoped>
.inventor-info {
  .table-section {
    margin-bottom: 20px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .table-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;

        &::before {
          content: '■';
          color: #409eff;
          margin-right: 5px;
        }
      }
    }

    .custom-table {
      :deep(.vxe__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 500;
        }
      }

      :deep(.vxe__body) {
        tr:hover > td {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 15px 20px 10px;
    border-bottom: 1px solid #e4e7ed;
  }

  .el-dialog__body {
    padding: 20px 40px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
