import request from '@/config/axios'

// 项目测试项模板 VO
export interface TestingTemplateVO {
  id?: number // 编号
  type?: string // 测试类型
  secondaryType?: string // 二级测试类型
  sort?: number // 序号
  name?: string // 测试项
  demand?: string // 测试要求
  accountFor?: string // 说明
  workHours?: number // 工时
}

// 项目测试项模板 API
export const TestingTemplateApi = {
  // 查询项目测试项模板分页
  getTestingTemplatePage: async (params: any) => {
    return await request.get({ url: `/project/testing-template/page`, params })
  },

  // 查询项目测试项模板详情
  getTestingTemplate: async (id: number) => {
    return await request.get({ url: `/project/testing-template/get?id=` + id })
  },

  // 新增项目测试项模板
  createTestingTemplate: async (data: TestingTemplateVO) => {
    return await request.post({ url: `/project/testing-template/create`, data })
  },

  // 修改项目测试项模板
  updateTestingTemplate: async (data: TestingTemplateVO) => {
    return await request.put({ url: `/project/testing-template/update`, data })
  },

  // 删除项目测试项模板
  deleteTestingTemplate: async (id: number) => {
    return await request.delete({ url: `/project/testing-template/delete?id=` + id })
  },

  // 导出项目测试项模板 Excel
  exportTestingTemplate: async (params) => {
    return await request.download({ url: `/project/testing-template/export-excel`, params })
  },
  // 根据类型获取测试模板类型
  getTestingTemplateList: (type: string) => {
    return request.get({ url: `/project/testing-template/list/` + type })
  }
}
