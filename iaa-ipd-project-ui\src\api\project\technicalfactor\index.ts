import request from '@/config/axios'

// 评审要素 VO
export interface TechnicalFactorVO {
  id: number // 评审要素ID
  groupSort: number // 评审分组
  item: string // 评审项
  factor: string // 评审要素
  description: string // 评审要素描述
}

// 评审要素 API
export const TechnicalFactorApi = {
  // 查询评审要素分页
  getTechnicalFactorPage: async (params: any) => {
    return await request.get({ url: `/project/technical-factor/page`, params })
  },

  // 查询评审要素详情
  getTechnicalFactor: async (id: number) => {
    return await request.get({ url: `/project/technical-factor/get?id=` + id })
  },

  // 新增评审要素
  createTechnicalFactor: async (data: TechnicalFactorVO) => {
    return await request.post({ url: `/project/technical-factor/create`, data })
  },

  // 修改评审要素
  updateTechnicalFactor: async (data: TechnicalFactorVO) => {
    return await request.put({ url: `/project/technical-factor/update`, data })
  },

  // 删除评审要素
  deleteTechnicalFactor: async (id: number) => {
    return await request.delete({ url: `/project/technical-factor/delete?id=` + id })
  },

  // 导出评审要素 Excel
  exportTechnicalFactor: async (params) => {
    return await request.download({ url: `/project/technical-factor/export-excel`, params })
  },
  getTechnicalFactorListByGroup: async (group?: number) => {
    return await request.get({ url: `/project/technical-factor/list-group/${group}` })
  },
  getSimpleList: () => {
    return request.get({ url: `/project/technical-factor/get-simple-list` })
  }
}
