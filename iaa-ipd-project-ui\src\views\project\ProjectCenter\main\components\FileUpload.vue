<template>
  <UploadFile
    ref="uploadRef"
    v-model="fileUrls"
    :limit="1"
    :file-size="100"
    :show-url-list="false"
    tip="请上传您的输出文件"
    drag
    @file-list="onUploadSuccess"
    :disabled="props.disabled"
  />
  <Dialog title="输出上传" v-model="show" width="40%" :before-close="fileBeforeClose">
    <el-form
      label-width="90px"
      class="custom-border-form custom-form"
      :rules="formDataRules"
      :model="formData"
      ref="formRef"
    >
      <el-form-item label="文件名称" prop="name">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="文件地址" prop="url">
        <el-link
          type="primary"
          @click="officeEditorRef.open(formData.infraFileId, formData.name)"
          >{{ formData.url }}</el-link
        >
      </el-form-item>
      <el-form-item label="参考模板" v-if="props.category === 'activities'" prop="templateId">
        <el-select v-model="formData.templateId">
          <el-option
            v-for="file in props.fileTemplateList"
            :key="file.id"
            :label="file.name"
            :value="file.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="onSubmit(false)">提交</el-button>
    </template>
  </Dialog>
  <office-editor ref="officeEditorRef" />
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { AttachmentFlowApi } from '@/api/bpm/attachment'

const show = ref(false)
const fileUrls = ref<string[]>([])
const message = useMessage()
const uploadRef = ref()
const officeEditorRef = ref()
const formRef = ref()

const formData = ref({
  attachmentId: undefined,
  category: undefined,
  dynamicId: undefined,
  name: undefined,
  infraFileId: undefined,
  templateId: undefined,
  url: undefined,
  targetType: undefined,
  targetDockingId: undefined
})

const props = defineProps({
  category: propTypes.oneOfType<
    'activities' | 'problem' | 'risk' | 'technical' | 'additional' | any
  >([]).isRequired,
  dynamicId: propTypes.oneOfType<string | number | undefined>([]).isRequired,
  fileTemplateList: propTypes.arrayOf<any>([]).def([]),
  targetType: propTypes.oneOfType<number | undefined>([]).def(undefined),
  targetDockingId: propTypes.oneOfType<number | undefined>([]).def(undefined),
  disabled: propTypes.bool.def(false)
})

const formDataRules = {
  name: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
  url: [{ required: true, message: '路径不能为空', trigger: 'blur' }],
  templateId: [
    {
      required: props.fileTemplateList?.length > 0,
      message: '请选择绑定的参考模板',
      trigger: 'blur'
    }
  ]
}

const emits = defineEmits(['success'])

/** 文件上传成功 */
const onUploadSuccess = async (fileList: any[]) => {
  const row = fileList[0]
  formData.value.category = props.category as unknown as undefined
  formData.value.dynamicId = props.dynamicId as unknown as undefined
  formData.value.infraFileId = row.id
  formData.value.name = row.name
  formData.value.url = row.url
  formData.value.targetType = props.targetType as unknown as undefined
  formData.value.targetDockingId = props.targetDockingId as unknown as undefined
  show.value = true
}

const onSubmit = async (allow: boolean) => {
  if (props.fileTemplateList?.length > 0 && !formData.value.templateId) {
    message.error('请选择绑定的参考模板')
    return
  }
  await formRef.value?.validate()
  await AttachmentFlowApi.createAttachmentFlow(formData.value)
  message.success('上传成功')
  resetForm()
  emits('success')
  if (!allow) show.value = false
}

/** 拦截文件关闭窗口事件 */
const fileBeforeClose = async (done?: any) => {
  if (!done) {
    resetForm()
    return
  }
  if (formData.value.infraFileId == undefined) {
    done()
    return
  }
  await message.confirm('确定要关闭窗口嘛？关闭将会删除已上传的文件')
  uploadRef.value?.handleRemoveAllFile()
  resetForm()
  done()
}

const resetForm = () => {
  formData.value = {
    attachmentId: undefined,
    category: undefined,
    dynamicId: undefined,
    infraFileId: undefined,
    name: undefined,
    url: undefined,
    templateId: undefined,
    targetType: undefined,
    targetDockingId: undefined
  }
  fileUrls.value = []
}

// const openForm = (id?: number) => {
//   show.value = true
//   formData.value.attachmentId = id as unknown as undefined
// }

// defineExpose({
//   openForm
// })
</script>

<style lang="scss" scoped>
.dont-break-out {
  /* 禁用自动连字符断行 */
  -ms-hyphens: none;
  -moz-hyphens: none;
  -webkit-hyphens: none;
  hyphens: none;

  /* 允许在任意位置换行，但优先保持单词完整 */
  overflow-wrap: anywhere;
  word-break: normal;

  /* 如果需要完全禁止换行 */
  /* white-space: nowrap; */
  /* overflow: hidden;
     text-overflow: ellipsis; */
}
</style>
