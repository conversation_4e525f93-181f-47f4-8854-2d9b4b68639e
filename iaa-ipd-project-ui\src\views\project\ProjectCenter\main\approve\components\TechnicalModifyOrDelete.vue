<template>
  <el-form label-width="100px" ref="formRef" class="custom-form">
    <el-form-item label="原因">
      <div class="text-1rem bg-amber w-full rounded-1 break-all">
        {{
          formData.modifyInfo
            ?.filter((item) => item.modifyField === 'update')
            ?.map((item) => item.modifyFieldName)
            ?.join('\n')
        }}
      </div>
    </el-form-item>
    <el-form-item label="所属项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="问题图片">
      <UploadImgs v-model="formData.imgIds!" height="60px" width="60px" :disabled="true" />
    </el-form-item>
    <el-form-item label="针对的要素" prop="targetFactor">
      <div
        class="w-full h-36px leading-[36px] text-1rem p-2px rounded-5px cursor-pointer"
        :style="{
          backgroundColor: 'var(--el-disabled-bg-color)'
        }"
      >
        {{ factorData.label }}
      </div>
    </el-form-item>
    <el-form-item label="预计完成" prop="endDate">
      <el-date-picker
        v-model="formData.endDate"
        type="date"
        placeholder="选择日期"
        :disabled="true"
      />
    </el-form-item>
    <el-form-item label="问题描述" prop="description">
      <el-input v-model="formData.description" type="textarea" :rows="5" :disabled="true" />
    </el-form-item>
    <el-form-item label="建议">
      <el-input v-model="formData.suggestion" type="textarea" :rows="5" :disabled="true" />
    </el-form-item>
    <el-form-item label="负责人" prop="director">
      <user-avatar-list
        v-model="formData.director!"
        :user-list="userList"
        :size="28"
        :limit="10"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="状态">
      <DictTag type="project_activities_status" :value="formData.status!" />
    </el-form-item>
    <el-form-item label="进度">
      <el-progress
        :percentage="formData.progress"
        class="w-100% no-radius"
        :text-inside="true"
        :stroke-width="20"
        status="success"
      />
    </el-form-item>
    <el-form-item label="修改信息">
      <vxe-table
        :data="formData.modifyInfo?.filter((item) => item.modifyField !== 'update')"
        show-overflow
        :header-cell-style="{ padding: 0, fontSize: '1rem' }"
        :cell-style="{ padding: '5px', fontSize: '1rem', height: '1.5vw' }"
        border
        stripe
        align="center"
        class="!w-full"
      >
        <vxe-column title="修改字段/原因" field="modifyFieldName" />
        <!-- 统一处理列的渲染逻辑 -->
        <template v-for="col in ['beforeValue', 'afterValue']" :key="col">
          <vxe-column :title="col === 'beforeValue' ? '修改前' : '修改后'">
            <template #default="{ row }">
              <template v-if="shouldShowDictTag(row.modifyField)">
                <el-tag>
                  {{ formatDictLabel(row.modifyField, row[col]) }}
                </el-tag>
              </template>
              <template
                v-else-if="
                 ['managers','director','coordinate'].includes(row.modifyField) ||
                  getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                    (dict) => dict.value === row.modifyField
                  )?.label
                "
              >
                {{ getUserNickName(userList, row[col]) }}
              </template>
              <!-- <template v-else-if="row.modifyField === 'categoryIds'">
                {{ getCategoryName(row[col]) }}
              </template> -->
              <template v-else>{{ row[col] }}</template>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { TechnicalFlowApi } from '@/api/bpm/technical'
import { propTypes } from '@/utils/propTypes'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'
import { TechnicalFactorApi, TechnicalFactorVO } from '@/api/project/technicalfactor'

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})
const formData = ref<any>({})
const userList = ref<UserVO[]>([])

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

const factorData = ref({
  label: '',
  id: 0
})
const factorList = ref<TechnicalFactorVO[]>([])

watch(
  () => props.processInstanceId,
  async () => {
    if (!props.processInstanceId) return
    formData.value = await TechnicalFlowApi.getTechnicalFlow(props.processInstanceId)
    const res = await TechnicalFactorApi.getTechnicalFactorListByGroup(-1)
    factorList.value = res
    factorData.value.id = formData.value.targetFactor
    factorData.value.label =
      factorList.value.find((item) => item.id === formData.value.targetFactor)?.factor || ''
  },
  { immediate: true }
)

onMounted(() => {
  getUserList()
})
</script>
