import request from '@/config/axios'

// 项目支持人员库 VO
export interface SupportLibraryVO {
  id: number // 项目支持人员库ID
  role: string // 角色
  userIds: number // 人员ID
}

// 项目支持人员库 API
export const SupportLibraryApi = {
  // 查询项目支持人员库分页
  getSupportLibraryPage: async (params: any) => {
    return await request.get({ url: `/project/support-library/page`, params })
  },

  // 查询项目支持人员库详情
  getSupportLibrary: async (id: number) => {
    return await request.get({ url: `/project/support-library/get?id=` + id })
  },

  // 新增项目支持人员库
  createSupportLibrary: async (data: SupportLibraryVO) => {
    return await request.post({ url: `/project/support-library/create`, data })
  },

  // 修改项目支持人员库
  updateSupportLibrary: async (data: SupportLibraryVO) => {
    return await request.put({ url: `/project/support-library/update`, data })
  },

  // 删除项目支持人员库
  deleteSupportLibrary: async (id: number) => {
    return await request.delete({ url: `/project/support-library/delete?id=` + id })
  },

  // 导出项目支持人员库 Excel
  exportSupportLibrary: async (params) => {
    return await request.download({ url: `/project/support-library/export-excel`, params })
  },
  // 获取所有项目支持人员ID
  getSimpleList:async()=>{
    return await request.get({url:`/project/support-library/simple-list`})
  }
}
