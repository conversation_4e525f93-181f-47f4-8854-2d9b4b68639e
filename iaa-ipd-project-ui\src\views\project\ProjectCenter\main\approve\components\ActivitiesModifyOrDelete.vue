<template>
  <el-form label-width="100px" class="custom-form" size="small" ref="formRef">
    <el-form-item label="原因">
      <div class="text-1rem bg-amber w-full rounded-1 break-all">
        {{
          formData.modifyInfo
            ?.filter((item) => item.modifyField === 'update')
            ?.map((item) => item.modifyFieldName)
            ?.join('\n')
        }}
      </div>
    </el-form-item>
    <el-form-item label="所属项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="活动名称">
      <el-input v-model="formData.name" :disabled="true" />
    </el-form-item>
    <el-form-item label="活动内容">
      <el-input v-model="formData.content" type="textarea" :rows="3" :disabled="true" />
    </el-form-item>
    <el-form-item label="活动描述">
      <el-input v-model="formData.description" type="textarea" :rows="6" :disabled="true" />
    </el-form-item>
    <el-form-item label="负责人" prop="director">
      <user-avatar-list
        v-model="formData.director!"
        :user-list="userList"
        :size="28"
        :limit="3"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="执行人">
      <user-avatar-list
        v-model="formData.coordinate!"
        :user-list="userList"
        :size="28"
        :limit="3"
        :add="false"
      />
    </el-form-item>
    <el-row>
      <el-col :span="12">
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="formData.startDate"
            value-format="YYYY-MM-DD"
            class="!w-100%"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="formData.endDate"
            value-format="YYYY-MM-DD"
            class="!w-100%"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="进度">
      <el-progress
        :percentage="formData.progress"
        class="w-100% no-radius"
        :text-inside="true"
        :stroke-width="20"
        status="success"
      />
    </el-form-item>
    <el-form-item label="状态">
      <DictTag type="project_activities_status" :value="formData.status!" />
    </el-form-item>
    <el-form-item label="修改信息">
      <vxe-table
        :data="formData.modifyInfo?.filter((item) => item.modifyField !== 'update')"
        show-overflow
        :header-cell-style="{ padding: 0, fontSize: '1rem' }"
        :cell-style="{ padding: '5px', fontSize: '1rem', height: '1.5vw' }"
        border
        stripe
        align="center"
        class="!w-full"
      >
        <vxe-column title="修改字段/原因" field="modifyFieldName" />
        <!-- 统一处理列的渲染逻辑 -->
        <template v-for="col in ['beforeValue', 'afterValue']" :key="col">
          <vxe-column :title="col === 'beforeValue' ? '修改前' : '修改后'">
            <template #default="{ row }">
              <template v-if="shouldShowDictTag(row.modifyField)">
                <el-tag>
                  {{ formatDictLabel(row.modifyField, row[col]) }}
                </el-tag>
              </template>
              <template
                v-else-if="
                  ['managers', 'director', 'coordinate'].includes(row.modifyField) ||
                  getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                    (dict) => dict.value === row.modifyField
                  )?.label
                "
              >
                {{ getUserNickName(userList, row[col]) }}
              </template>
              <!-- <template v-else-if="row.modifyField === 'categoryIds'">
                {{ getCategoryName(row[col]) }}
              </template> -->
              <template v-else>{{ row[col] }}</template>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ActivitiesFlowApi } from '@/api/bpm/activities'
import { propTypes } from '@/utils/propTypes'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})
const formData = ref<any>({})
const userList = ref<UserVO[]>([])

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

watch(
  () => props.processInstanceId,
  async () => {
    if (!props.processInstanceId) return
    formData.value = await ActivitiesFlowApi.getActivities(props.processInstanceId)
  },
  { immediate: true }
)

onMounted(() => {
  getUserList()
})
</script>
