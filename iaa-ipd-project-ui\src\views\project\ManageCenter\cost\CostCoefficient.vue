<template>
  <Dialog title="成本系数设置" v-model="visible">
    <el-form label-width="110px" class="custom-form">
      <el-row :gutter="10">
        <el-col :span="12" v-for="dict in getStrDictOptions('project_team_role')" :key="dict.value">
          <el-form-item :label="dict.label">
            <el-input-number class="!w-full" v-model="formData[dict.value]" :precision="2" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="其他">
            <el-input-number class="!w-full" v-model="formData['other']" :precision="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save" :loading="loading">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { getStrDictOptions } from '@/utils/dict'
import { CostApi } from '@/api/project/cost'

const visible = ref(false)
const formData = ref<any>({})
const message = useMessage()
const loading = ref(false)

const openModal = () => {
  visible.value = true
  const dict = getStrDictOptions('project_team_role')
  dict.forEach((item) => {
    formData.value[item.value] = 0
  })
  formData.value['other'] = 0
  getCostList()
}

const getCostList = async () => {
  const res = await CostApi.getCostRateList()
  res.forEach((item) => {
    formData.value[item.role] = item.rate
  })
}

const save = async () => {
  loading.value = true
  try {
    let datas = [] as any[]
    for (const key in formData.value) {
      datas.push({
        role: key,
        rate: formData.value[key]
      })
    }
    await CostApi.saveCostRate(datas)
    message.success('保存成功')
    visible.value = false
  } finally {
    loading.value = false
  }
}

defineExpose({
  openModal
})
</script>
