<template>
  <ContentWrap>
    <el-form inline size="small">
      <el-form-item label="名称">
        <el-input v-model="queryParams.name" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleList">查询</el-button>
        <el-button type="warning" @click="encoderFormRef.open()">添加</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <vxe-table :data="list" :cell-style="{ padding: '5px' }">
      <vxe-column title="名称" field="name" />
      <vxe-column title="模式" field="pattern">
        <template #default="{ row }">
          <DictTag type="system_encoder" :value="row.pattern" />
        </template>
      </vxe-column>
      <vxe-column title="占位符" field="prefix" />
      <vxe-column title="日期格式" field="dateFormat" />
      <vxe-column title="流水号长度" field="serialLength" />
      <vxe-column title="最后生成日期" field="lastDate" />
      <vxe-column title="当前流水号" field="currentSerial" />
      <vxe-column title="操作" width="200" align="center">
        <template #default="{ row }">
          <el-button type="primary" @click="encoderFormRef.open(row)" link> 编辑 </el-button>
          <el-button type="danger" link @click="deleteEncoder(row.id)">删除</el-button>
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <EncoderForm ref="encoderFormRef" @success="handleList" />
</template>

<script lang="ts" setup>
import EncoderForm from './EncoderForm.vue'
import { EncoderApi } from '@/api/infra/encoder'

const list = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const encoderFormRef = ref()
const message = useMessage()

const queryParams = ref({
  pageSize: 10,
  pageNo: 1,
  name:''
})

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 获取列表 */
const getList = async () => {
  loading.value = true
  try {
    const res = await EncoderApi.page(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = true
  }
}

const deleteEncoder = async (id: number) => {
  await message.delConfirm()
  await EncoderApi.deleteEncoder(id)
  message.success('删除成功')
  handleList()
}

onMounted(() => {
  handleList()
})
</script>
