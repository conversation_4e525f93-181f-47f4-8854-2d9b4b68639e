import request from '@/config/axios'

export const SearchWordFlowApi = {
  /** 创建专利查询词确认流程 */
  createSearchWord: (data: any) => {
    return request.post({ url: '/bpm/patent/search-word/create', data })
  },
  /** 根据流程实例ID查询专利查询词确认流程 */
  getByProcessInstanceId: (processInstanceId: string) => {
    return request.get({
      url: '/bpm/patent/search-word/get-by-process-instance-id/' + processInstanceId
    })
  },
  /** 更新专利查询词 */
  updateSearchWord: (data: any) => {
    return request.post({
      url: '/bpm/patent/search-word/update',
      data
    })
  }
}
