<template>
  <ContentWrap>
    <div class="h-[calc(100vh-140px)]">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="专利维护" name="patent">
          <vxe-toolbar size="mini" custom ref="toolbarRef">
            <template #buttons>
              <el-button type="primary" plain @click="maintenanceFormRef?.openForm()">
                <Icon icon="ep:plus" />
                新增专利
              </el-button>
            </template>
          </vxe-toolbar>
          <div class="h-[calc(100vh-280px)]">
            <vxe-table
              ref="tableRef"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :row-style="{
                cursor: 'pointer'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :header-cell-config="{ height: 40 }"
              :cell-config="{ height: 40 }"
              :data="list"
              @cell-click="cellClickEvent"
              @cell-context-menu="cellContextMenuEvent"
              @menu-click="contextMenuClickEvent"
              @context-menu-click="contextMenuClickEvent"
              :menu-config="contextMenuConfig"
              border
              align="center"
              stripe
            >
              <vxe-column type="checkbox" width="50" />
              <vxe-column title="申请状态" field="status" width="90">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.PATENT_MAINTENANCE_STATUS" :value="row.status" />
                </template>
              </vxe-column>
              <vxe-column title="申请号" field="applicationNo" min-width="120" />
              <vxe-column title="公开号" field="publicNo" min-width="120" />
              <vxe-column title="专利名称" field="patentName" min-width="120" />
              <vxe-column title="当前权力人" field="ownership" min-width="120" />
              <vxe-column title="保护技术点" field="protectionPoint" min-width="120" />
              <vxe-column title="保护的产品型号" field="models" min-width="120" />
              <vxe-column title="专利类型" field="patentType" width="90">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.PATENT_TYPE" :value="row.patentType" />
                </template>
              </vxe-column>
              <vxe-column
                title="申请日期"
                width="120"
                :formatter="dateFormatter4"
                field="applicationDate"
              />
              <vxe-column
                title="授权日期"
                width="120"
                :formatter="dateFormatter4"
                field="authorizationDate"
              />
              <vxe-column title="有效期(年数)" field="validityPeriod" min-width="100" />
              <vxe-column title="获取方式" field="acquisitionMethod" min-width="120" />
              <vxe-column title="法律状态" field="legalStatus" width="90">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="row.legalStatus" />
                </template>
              </vxe-column>
              <vxe-column title="代理机构" field="agency" min-width="120" />
              <vxe-column title="同族专利" field="families" min-width="120" />
              <vxe-column title="维保评估" field="maintenanceAssessment" min-width="120" />
              <vxe-column
                title="创建时间"
                min-width="150"
                field="createTime"
                :formatter="dateFormatter3"
              />
            </vxe-table>
          </div>
          <!-- 分页 -->
          <Pagination
            size="small"
            :total="total"
            v-model:page="queryParams.pageNo"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </el-tab-pane>
        <el-tab-pane label="年费提醒规则" name="tip">
          <vxe-toolbar size="mini" custom ref="reminderToolbarRef">
            <!-- <template #buttons>
            <el-button type="primary" size="small" plain @click="reminderFormRef?.openForm('create')">
              新增提醒规则
            </el-button>
          </template> -->
          </vxe-toolbar>
          <div class="h-[calc(100vh-280px)]">
            <vxe-table
              ref="reminderTableRef"
              height="100%"
              :header-cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                backgroundColor: '#fafafa',
                color: 'var(--primary-text-color)'
              }"
              :row-style="{
                cursor: 'pointer'
              }"
              :cell-style="{
                padding: '0',
                height: '2.5rem',
                fontSize: '.9rem',
                color: 'var(--primary-text-color)'
              }"
              :header-cell-config="{ height: 40 }"
              :cell-config="{ height: 40 }"
              :data="reminderList"
              @cell-click="reminderCellClickEvent"
              border
              align="center"
            >
              <vxe-column type="checkbox" width="50" />
              <vxe-column title="申请号" field="applicationNo" min-width="120" />
              <vxe-column title="专利名称" field="patentName" min-width="150" />
              <vxe-column title="保护技术点" field="protectionPoint" min-width="120" />
              <vxe-column title="提醒人员" field="reminderUsers" min-width="150">
                <template #default="{ row }">
                  <user-avatar-list
                    v-model="row.reminderUsers"
                    :user-list="userList"
                    :size="28"
                    :limit="5"
                    :add="false"
                  />
                </template>
              </vxe-column>
              <vxe-column title="提前天数" field="remindDays" width="100" align="center">
                <template #default="{ row }"> {{ row.remindDays }}天 </template>
              </vxe-column>
              <vxe-column title="提醒类型" field="remindType" width="100">
                <template #default="{ row }">
                  <dict-tag :type="DICT_TYPE.PATENT_REMINDER_TYPE" :value="row.remindType" />
                </template>
              </vxe-column>
              <vxe-column title="状态" field="status" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                    {{ row.status === 1 ? '启用' : '停用' }}
                  </el-tag>
                </template>
              </vxe-column>
              <vxe-column
                title="创建时间"
                min-width="150"
                field="createTime"
                :formatter="dateFormatter3"
              />
              <vxe-column title="操作" min-width="120" fixed="right">
                <template #default="{ row }">
                  <el-button
                    @click="reminderFormRef?.openForm('edit', row.id)"
                    link
                    type="primary"
                    size="small"
                  >
                    编辑
                  </el-button>
                  <el-button @click="handleReminderDelete(row.id)" link type="danger" size="small">
                    删除
                  </el-button>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
          <!-- 分页 -->
          <Pagination
            size="small"
            :total="reminderTotal"
            v-model:page="reminderQueryParams.pageNo"
            v-model:limit="reminderQueryParams.pageSize"
            @pagination="getReminderList"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <MaintenanceForm ref="maintenanceFormRef" @success="getList()" />
    <MaintenanceReminderForm ref="reminderFormRef" @success="getReminderList()" />
  </ContentWrap>
</template>
<script setup lang="ts">
import { MaintenanceApi, MaintenanceVO } from '@/api/patent/maintenance'
import { MaintenanceReminderApi, MaintenanceReminderVO } from '@/api/patent/maintenance-reminder'
import MaintenanceForm from './MaintenanceForm.vue'
import MaintenanceReminderForm from './MaintenanceReminderForm.vue'
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter4, dateFormatter3 } from '@/utils/formatTime'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import UserAvatarList from '@/components/UserAvatarList/index.vue'
import moment from 'moment'

// 消息弹窗
const message = useMessage()
const maintenanceFormRef = ref()
const reminderFormRef = ref()
const toolbarRef = ref()
const reminderToolbarRef = ref()
const tableRef = ref()
const reminderTableRef = ref()
const total = ref(0)
const reminderTotal = ref(0)
const list = ref<MaintenanceVO[]>([]) // 列表的数据
const reminderList = ref<MaintenanceReminderVO[]>([]) // 提醒规则列表数据
const loading = ref(true) // 列表的加载中
const reminderLoading = ref(true) // 提醒规则列表的加载中
const userList = ref<UserVO[]>([]) // 用户列表
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  protectionPoint: undefined,
  status: undefined,
  patentType: undefined,
  legalStatus: undefined
})

// 提醒规则查询参数
const reminderQueryParams = ref({
  pageNo: 1,
  pageSize: 30,
  applicationNo: undefined,
  patentName: undefined,
  status: undefined
})

// 默认选中"针对专利许可、转让"标签
const activeTab = ref('patent')
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const data = await MaintenanceApi.getMaintenancePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

//单击编辑
const cellClickEvent: any = ({ row }) => {
  maintenanceFormRef.value.openForm(row.id)
}

// 右键菜单配置
const contextMenuConfig = ref({
  body: {
    options: [[{ code: 'addToReminder', name: '添加到提醒规则', prefixIcon: 'vxe-icon-bell' }]]
  }
})

// 右键菜单事件处理
const cellContextMenuEvent = ({ row }) => {
  // 这里可以根据需要设置当前选中行
  console.log('右键点击行:', row)
}

// 右键菜单点击事件
const contextMenuClickEvent = async ({ menu, row }) => {
  const { code } = menu
  if (code === 'addToReminder') {
    await handleAddToReminder(row)
  }
}

// 添加到提醒规则
const handleAddToReminder = async (row: MaintenanceVO) => {
  try {
    // 将数组转换为 Date 对象
    const applicationDate = moment(row.applicationDate).subtract(1, 'months')
    const authorizationDate = moment(row.authorizationDate).subtract(1, 'months')
    // 构建提醒数据，从专利维保数据中提取相关字段
    const reminderData = {
      maintenanceId: row.id,
      applicationNo: row.applicationNo,
      publicNo: row.publicNo,
      patentName: row.patentName,
      protectionPoint: row.protectionPoint,
      models: row.models,
      applicationDate: applicationDate.format('YYYY-MM-DD'),
      authorizationDate: authorizationDate.format('YYYY-MM-DD'),
      validityPeriod: row.validityPeriod,
      legalStatus: row.legalStatus,
      // 默认值，后端会设置
      reminderUsers: [],
      remindDays: 30,
      remindType: 0,
      status: 1
    }

    await MaintenanceReminderApi.createMaintenanceReminder(reminderData)
    message.success('已成功添加到提醒规则')

    // 切换到提醒规则配置页签
    activeTab.value = 'problem'
  } catch (error) {
    console.error('添加提醒规则失败:', error)
    message.error('添加提醒规则失败')
  }
}

// ========== 提醒规则相关函数 ==========

// 获取用户列表
const getUserList = async () => {
  try {
    userList.value = await getSimpleUserList()
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 获取提醒规则列表
const getReminderList = async () => {
  reminderLoading.value = true
  try {
    const data = await MaintenanceReminderApi.getMaintenanceReminderPage(reminderQueryParams.value)
    reminderList.value = data.list
    reminderTotal.value = data.total
  } finally {
    reminderLoading.value = false
  }
}

// 提醒规则单击编辑
const reminderCellClickEvent: any = ({ row }) => {
  reminderFormRef.value.openForm('edit', row.id)
}

// 删除提醒规则
const handleReminderDelete = async (id: number) => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await MaintenanceReminderApi.deleteMaintenanceReminder(id)
  message.success('删除成功')
  await getReminderList()
}

onMounted(async () => {
  await nextTick()
  unref(tableRef)?.connect(unref(toolbarRef))
  unref(reminderTableRef)?.connect(unref(reminderToolbarRef))

  // 初始化数据
  getList()
  getUserList()

  // 监听tab切换，当切换到提醒规则页签时加载数据
  watch(activeTab, (newTab) => {
    if (newTab === 'tip') {
      getReminderList()
    }
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header.is-top) {
  margin-bottom: 0 !important;
}
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
