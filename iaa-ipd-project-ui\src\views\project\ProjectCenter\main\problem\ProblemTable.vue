<template>
  <vxe-table
    height="91%"
    :header-cell-style="{
      padding: '0',
      height: '2.5rem',
      fontSize: '.9rem',
      backgroundColor: '#fafafa',
      color: 'var(--primary-text-color)'
    }"
    :row-style="{
      cursor: 'pointer'
    }"
    :cell-style="{
      padding: '0',
      height: '2.5rem',
      fontSize: '.9rem',
      color: 'var(--primary-text-color)'
    }"
    round
    auto-resize
    border
    :row-config="{ drag: true, isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
    :column-config="{ resizable: true, isHover: true }"
    :export-config="{ remote: true, exportMethod: onExport }"
    :data="props.list"
    show-overflow
    ref="problemTableRef"
    @row-dragend="rowDragendEvent"
    @cell-click="(el: any) => emits('show:form', el.row)"
  >
    <vxe-column title="序号" field="sort" width="70" align="center" drag-sort />
    <vxe-column
      title="描述"
      field="content"
      width="200"
      align="left"
      :filters="contentOptions"
      :filter-render="FilterValue.textFilterRender"
    />
    <vxe-column title="图片" field="imgIds" width="60" align="center">
      <template #default="{ row }">
        <template v-if="row.imgIds?.length > 0">
          <img :src="row.imgIds?.[0]" style="width: 1.5vw; height: 1.5vw" />
          <div
            style="
              position: absolute;
              top: 0;
              width: 1vw;
              height: 1vw;
              background-color: var(--el-color-primary-light-3);
              border-radius: 1rem;
              right: 0;
              color: #fff;
            "
            >{{ row.imgIds?.length }}</div
          >
        </template>
      </template>
    </vxe-column>
    <vxe-column title="阶段" field="stage" width="90" align="center">
      <template #default="{ row }">
        {{ templateNode.find((item) => item.id === row.stage)?.label }}
      </template>
    </vxe-column>
    <vxe-column
      title="状态"
      field="status"
      width="95"
      align="center"
      :filters="FilterValue.statusOptions"
    >
      <template #default="{ row }">
        <DictTag :type="'project_activities_status'" :value="row.status" />
      </template>
    </vxe-column>
    <vxe-column title="进度" field="progress" align="center" width="7%">
      <template #default="{ row }">
        <el-progress
          :text-inside="true"
          :stroke-width="userSize - 6"
          :percentage="row.progress"
          status="success"
          class="no-radius"
        />
      </template>
    </vxe-column>
    <vxe-column
      title="等级"
      field="level"
      width="90"
      align="center"
      :filters="FilterValue.problemLevelOptions"
    >
      <template #default="{ row }">
        <DictTag :type="'project_problem_level'" :value="row.level" />
      </template>
    </vxe-column>
    <vxe-column
      title="分类"
      field="category"
      width="110"
      align="center"
      :filters="FilterValue.problemCategoryOptions"
    >
      <template #default="{ row }">
        <DictTag :type="'project_problem_category'" :value="row.category" />
      </template>
    </vxe-column>
    <vxe-column
      title="责任模块"
      field="module"
      width="100"
      align="center"
      :filters="FilterValue.problemModuleOptions"
    >
      <template #default="{ row }">
        <DictTag :type="'project_problem_module'" :value="row.module" />
      </template>
    </vxe-column>
    <vxe-column
      title="原因分析"
      field="reason"
      width="200"
      align="left"
      :filters="reasonOptions"
      :filter-render="FilterValue.textFilterRender"
    />
    <vxe-column title="提出部门" field="proposingDepartment" width="100" align="center">
      <template #default="{ row }">
        <DictTag :type="'project_problem_proposing_department'" :value="row.proposingDepartment" />
      </template>
    </vxe-column>
    <vxe-column title="不良比例" field="rejectRatio" width="100" align="center">
      <template #default="{ row }">
        {{ row.rejectRatio + (row.rejectRatio ? '%' : '') }}
      </template>
    </vxe-column>
    <vxe-column title="解决措施" field="measures" width="200" align="left" />
    <vxe-column
      title="责任人"
      field="director"
      width="120"
      align="center"
      fixed="right"
      :filters="directorOptions"
      :filter-render="FilterValue.userFilterRender"
    >
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.director"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="false"
        />
      </template>
    </vxe-column>
    <vxe-column
      title="执行人"
      field="coordinate"
      width="120"
      align="center"
      fixed="right"
      :filters="coordinateOptions"
      :filter-render="FilterValue.userFilterRender"
    >
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.coordinate"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="![3, 4, 10, 11].includes(row.status) && row.director.includes(userStore.getUser.id)"
          :visiable-user-list="[...getVisiableUserList(), ...supportlibraryList]"
          @change:msg="onCoordinateChange(row)"
          @click.stop
        />
      </template>
    </vxe-column>
    <vxe-column title="提出人" field="proposal" width="120" align="center">
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.proposal"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="false"
        />
      </template>
    </vxe-column>
    <vxe-column
      title="提出时间"
      field="timeOfProposal"
      :formatter="dateFormatter4"
      width="90"
      align="center"
    />
    <vxe-column
      title="计划完成"
      field="timeOfPlan"
      :formatter="dateFormatter4"
      width="90"
      align="center"
      fixed="right"
    />
    <vxe-column
      title="验证时间"
      field="timeOfVerification"
      :formatter="dateFormatter4"
      width="90"
      align="center"
    />
    <vxe-column title="效果确认" field="effect" width="120" align="center" />
    <vxe-column title="确认人" field="effectPerson" width="120" align="center">
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.effectPerson"
          :user-list="props.userList"
          :size="userSize"
          :limit="3"
          :add="false"
        />
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script lang="ts" setup>
import { ProblemVO } from '@/api/project/problem'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import { dateFormatter4 } from '@/utils/formatTime'
import { ProblemApi } from '@/api/project/problem'
import { useUserStore } from '@/store/modules/user'
import download from '@/utils/download'
import { useCache } from '@/hooks/web/useCache'
import { getVisiableUserList } from '../util/permission'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import { SupportLibraryApi } from '@/api/project/supportlibrary'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const contentOptions = ref([{ data: '' }])
const reasonOptions = ref([{ data: '' }])
const directorOptions = ref([{ data: [] }])
const coordinateOptions = ref([{ data: [] }])

interface Node {
  id: number | string
  label: string
}

const props = defineProps({
  list: propTypes.oneOfType([Array<ProblemVO>]).isRequired,
  templateNode: propTypes.oneOfType([Array<Node>]).isRequired,
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired,
  toolbar: propTypes.any.isRequired,
  basicsId: propTypes.number.isRequired
})

const userStore = useUserStore()
const problemTableRef = ref()
const message = useMessage()
const { wsCache } = useCache()

const emits = defineEmits(['update:success', 'show:form'])
/** 拖拽完成事件 */
const rowDragendEvent: any = async () => {
  const $table = problemTableRef.value
  if ($table) {
    const tableData = $table.getTableData().tableData
    let i = 0
    tableData.forEach((element) => {
      element.sort = i++
    })
    await ProblemApi.updateProblemBatch(tableData)
    message.success('修改成功')
    emits('update:success')
  }
}

/** 执行人变更 */
const onCoordinateChange = async (row: ProblemVO) => {
  row.director?.forEach((item) => {
    if (row.coordinate?.includes(item)) {
      const userInfo = props.userList.find((user) => user.id === item)
      message.warning(`${userInfo?.nickname}已经是当前问题的负责人了，不能添加为执行人`)
      row.coordinate = row.coordinate.filter((id) => id !== item)
    }
  })
  if (row.coordinate && row.coordinate.length > 0) {
    await ProblemApi.updateProblemCoordinate(row)
    message.success(`修改成功`)
    emits('update:success', row.id)
  }
}

/** 导出方法 */
const onExport = async ({ options }: any) => {
  try {
    if (!props.basicsId) return
    // 导出的二次确认
    await message.exportConfirm()
    const columns = options.columns.map((item) => item.field)
    if (columns.length === 0) {
      message.warning('未选择需要导出的列')
      return
    }
    // if (columns.includes('date')) {
    //   columns.splice(columns.indexOf('date'), 1, 'startDate', 'endDate')
    // }
    const data = await ProblemApi.exportProblem({
      basicsId: props.basicsId,
      filename: options.filename,
      column: columns
    })
    download.excel(data, `${options.filename}.xlsx`)
  } catch {}
}

/** 监听改变用户组件大小 */
const _windowWidth = ref(28)
const userSize = computed({
  get: () => _windowWidth.value,
  set: (value) => (_windowWidth.value = value)
})

const handleResize = () => {
  const decimalPercentage = 1.6 / 100
  _windowWidth.value = window.innerWidth * decimalPercentage
}

const scrollToRow = (id: number) => {
  requestAnimationFrame(() => {
    const row = problemTableRef.value?.getRowById(id)
    problemTableRef.value?.scrollToRow(row)
    problemTableRef.value?.setCurrentRow(row)
    if (row) {
      emits('show:form', row)
    }
    wsCache.delete('project_page_show_form')
  })
}

defineExpose({
  scrollToRow
})

onMounted(() => {
  nextTick(() => {
    unref(problemTableRef)?.connect(props.toolbar)
  })
  handleResize()
  window.addEventListener('resize', handleResize)
  getSupportLibraryList()
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
:deep(.vxe-body--expanded-column) {
  width: 98%;
}

:deep(.vxe-cell) {
  padding: 0 5px;
}

:deep(.el-tag) {
  --el-tag-font-size: 0.9rem;
  height: 1.5rem;
}

:deep(.vxe-body--row.is--expand-row.row--current) {
  & + .vxe-body--expanded-row {
    .expand-container {
      box-shadow:
        inset 4px 4px 0 0 rgba(163, 226, 255, 0.404),
        /* 外阴影，右下 */ inset -4px -4px 0 0 rgba(163, 226, 255, 0.404); /* 内阴影，左上 */
    }
  }
}

.expand-container {
  background-color: #ebeff3;
  // min-height: 100px;
  overflow: auto;
  display: flex;
  padding: 5px 30px;

  .comment-container {
    width: 80%;
  }

  .file-container {
    width: 20%;
  }
}
:deep(.vxe-body--row.row--hover) {
  background-color: var(--el-color-warning-light-9);
  & + .vxe-body--expanded-row {
    .expand-container {
      box-shadow:
        inset 4px 4px 0 0 var(--el-color-warning-light-9),
        /* 外阴影，右下 */ inset -4px -4px 0 0 var(--el-color-warning-light-9); /* 内阴影，左上 */
    }
  }
}
:deep(.vxe-cell) {
  height: 2.5rem !important;
}
</style>
