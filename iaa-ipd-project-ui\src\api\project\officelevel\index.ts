import request from '@/config/axios'

// 人员职级 VO
export interface OfficeLevelVO {
  id: number // 人员职级ID
  level: string // 人员职级
  userIds: number[] // 人员
}

// 人员职级 API
export const OfficeLevelApi = {
  // 查询人员职级分页
  getOfficeLevelPage: async (data: any) => {
    return await request.post({ url: `/project/office-level/page`, data })
  },

  // 查询人员职级详情
  getOfficeLevel: async (id: number) => {
    return await request.get({ url: `/project/office-level/get?id=` + id })
  },

  // 新增人员职级
  createOfficeLevel: async (data: OfficeLevelVO) => {
    return await request.post({ url: `/project/office-level/create`, data })
  },

  // 修改人员职级
  updateOfficeLevel: async (data: OfficeLevelVO) => {
    return await request.put({ url: `/project/office-level/update`, data })
  },

  // 删除人员职级
  deleteOfficeLevel: async (id: number) => {
    return await request.delete({ url: `/project/office-level/delete?id=` + id })
  },

  // 导出人员职级 Excel
  exportOfficeLevel: async (params) => {
    return await request.download({ url: `/project/office-level/export-excel`, params })
  },
  // 获取人员职级list

  getOfficeLevelList: async () => {
    return await request.get({ url: `/project/office-level/list` })
  }
}
