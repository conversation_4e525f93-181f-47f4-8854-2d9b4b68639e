<template>
  <Dialog :title="formTitle" v-model="visible">
    <el-form :label-width="120" :model="formData" :rules="formRules">
      <el-form-item label="二级分类" v-if="formData.type === '10'" prop="secondaryType">
        <el-select v-model="formData.secondaryType">
          <el-option
            v-for="dict in getStrDictOptions('project_testing_secondary_type')"
            :label="dict.label"
            :value="dict.value"
            :key="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="序号" prop="sort">
        <el-input-number :min="0" v-model="formData.sort" />
      </el-form-item>
      <el-form-item label="测试项" prop="name">
        <el-input type="textarea" v-model="formData.name" />
      </el-form-item>
      <el-form-item label="技术要求">
        <el-input type="textarea" v-model="formData.demand" />
      </el-form-item>
      <el-form-item label="说明">
        <el-input type="textarea" v-model="formData.accountFor" />
      </el-form-item>
      <el-form-item label="工时" prop="workHours">
        <el-input-number :min="0" v-model="formData.workHours" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :loading="loading" @click="onSave(true)">保存并继续添加</el-button>
      <el-button type="primary" :loading="loading" @click="onSave()">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { getStrDictOptions } from '@/utils/dict'
import { TestingTemplateApi, TestingTemplateVO } from '@/api/project/testingtemplate'

const visible = ref(false)
const formTitle = ref('')
const formData = ref<TestingTemplateVO>({
  id: undefined,
  type: undefined,
  secondaryType: undefined,
  sort: undefined,
  name: undefined,
  demand: undefined,
  accountFor: undefined,
  workHours: undefined
})
const formRules = reactive({
  secondaryType: [{ required: true, message: '二级分类不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '评审项不能为空', trigger: 'blur' }],
  workHours: [{ required: true, message: '工时不能为空', trigger: 'blur' }]
})
const loading = ref(false)
const message = useMessage()
const emits = defineEmits(['success'])

const openForm = (type: string, row?: any) => {
  visible.value = true
  formData.value.type = type as any
  if (row) {
    formData.value = row
  }
}

const onSave = async (again: boolean = false) => {
  loading.value = true
  try {
    if (formData.value.id) {
      await TestingTemplateApi.updateTestingTemplate(formData.value)
      message.success('保存成功')
    } else {
      await TestingTemplateApi.createTestingTemplate(formData.value)
      message.success('添加成功')
    }
    emits('success')
    refresh()
    if (!again) {
      visible.value = false
    }
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    type: formData.value.type,
    secondaryType: undefined,
    sort: undefined,
    name: undefined,
    demand: undefined,
    accountFor: undefined,
    workHours: undefined
  }
}

defineExpose({
  openForm
})
</script>
