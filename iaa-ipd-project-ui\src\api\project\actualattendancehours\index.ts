import request from '@/config/axios'
import { get } from 'min-dash'

// 项目实际出勤工时 VO
export interface ActualAttendanceHoursVO {
  id: number // 实际出勤工时ID
  userId: number // 用户ID
  day: Date // 日期
  hours: number // 工时
}

// 项目实际出勤工时 API
export const ActualAttendanceHoursApi = {
  // 查询项目实际出勤工时分页
  getActualAttendanceHoursPage: async (params: any) => {
    return await request.get({ url: `/project/actual-attendance-hours/page`, params })
  },

  // 查询项目实际出勤工时详情
  getActualAttendanceHours: async (id: number) => {
    return await request.get({ url: `/project/actual-attendance-hours/get?id=` + id })
  },

  // 新增项目实际出勤工时
  createActualAttendanceHours: async (data: ActualAttendanceHoursVO) => {
    return await request.post({ url: `/project/actual-attendance-hours/create`, data })
  },

  // 修改项目实际出勤工时
  updateActualAttendanceHours: async (data: ActualAttendanceHoursVO) => {
    return await request.put({ url: `/project/actual-attendance-hours/update`, data })
  },

  // 删除项目实际出勤工时
  deleteActualAttendanceHours: async (id: number) => {
    return await request.delete({ url: `/project/actual-attendance-hours/delete?id=` + id })
  },

  // 导出项目实际出勤工时 Excel
  exportActualAttendanceHours: async (params) => {
    return await request.download({ url: `/project/actual-attendance-hours/export-excel`, params })
  },
  // 导出项目实际出勤工时模板
  exportTemplate: async (day: string) => {
    return await request.download({
      url: `/project/actual-attendance-hours/export-template/` + day
    })
  },
  // 获取项目实际出勤人员列表
  getUserList: async (day: string) => {
    return await request.get({
      url: `/project/actual-attendance-hours/get-user-list/` + day
    })
  },
  // 获取出勤工时列表
  getHoursList: async (userId: number, day: string) => {
    return await request.get({
      url: `/project/actual-attendance-hours/list/${userId}/${day}`
    })
  }
}
