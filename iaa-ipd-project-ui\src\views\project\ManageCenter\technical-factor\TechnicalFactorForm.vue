<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="评审分组" prop="groupSort">
        <el-input v-model="formData.groupSort" placeholder="请输入评审分组" />
      </el-form-item>
      <el-form-item label="评审项" prop="item">
        <el-input v-model="formData.item" placeholder="请输入评审项" />
      </el-form-item>
      <el-form-item label="评审要素" prop="factor">
        <el-input v-model="formData.factor" placeholder="请输入评审要素" />
      </el-form-item>
      <el-form-item label="评审要素描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TechnicalFactorApi, TechnicalFactorVO } from '@/api/project/technicalfactor'

/** 评审要素 表单 */
defineOptions({ name: 'TechnicalFactorForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  groupSort: undefined,
  item: undefined,
  factor: undefined,
  description: undefined,
})
const formRules = reactive({
  groupSort: [{ required: true, message: '评审分组不能为空', trigger: 'blur' }],
  item: [{ required: true, message: '评审项不能为空', trigger: 'blur' }],
  factor: [{ required: true, message: '评审要素不能为空', trigger: 'blur' }],
  description: [{ required: true, message: '评审要素描述不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TechnicalFactorApi.getTechnicalFactor(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TechnicalFactorVO
    if (formType.value === 'create') {
      await TechnicalFactorApi.createTechnicalFactor(data)
      message.success(t('common.createSuccess'))
    } else {
      await TechnicalFactorApi.updateTechnicalFactor(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    groupSort: undefined,
    item: undefined,
    factor: undefined,
    description: undefined,
  }
  formRef.value?.resetFields()
}
</script>