import request from '@/config/axios'

export const Ka<PERSON><PERSON><PERSON><PERSON> = {
  /** 获取项目分布数据 */
  getBasicsDistribution: () => {
    return request.get({ url: '/project/kanban/get-basics-distribution' })
  },
  /** 获取项目进度偏差数据 */
  getBasicsProgressDeviation: (params: any) => {
    return request.get({ url: '/project/kanban/get-basics-progress-deviation', params })
  },
  /** 获取项目问题数量 */
  getBasicsProblemCount: (params: any) => {
    return request.get({ url: '/project/kanban/get-basics-problem-count', params })
  },
  /** 获得项目人员职级分布 */
  getBasicsOfficeLevel: () => {
    return request.get({ url: '/project/kanban/get-team-level' })
  },
  /** 获得项目交付 */
  getBasicsDelivery: (params: any) => {
    return request.get({ url: '/project/kanban/get-basics-delivery', params })
  },
  /** 获得项目复盘 */
  getReanalyzeList: () => {
    return request.get({ url: '/project/kanban/get-reanalyze' })
  },
  /** 获取个人提升 */
  getPersonalAscension: (params: any) => {
    return request.get({ url: '/project/kanban/get-personal-ascension', params })
  },
  /** 获取项目工时 */
  getWorkingHours: (params: any) => {
    return request.get({ url: '/project/kanban/get-working-hours', params })
  },
  /** 获取问题分布 */
  getProblemDistribution: (params: any) => {
    return request.get({ url: '/project/kanban/get-problem-distribution', params })
  },
  /** 获取偏差 */
  getDeviation: (params: any) => {
    return request.get({ url: '/project/kanban/get-deviation', params })
  },
  /** 获取Adcpu数据 */
  getAdcpAchieved: (year: number) => {
    return request.get({ url: '/project/kanban/get-adcp-achieved/' + year })
  },
  /** 获取Adcpu数据 */
  getCruxAchieved: (year: number, type: string) => {
    return request.get({ url: '/project/kanban/get-crux-achieved/' + year + '/' + type })
  },
  /** 获取项目变更情况 */
  getChangeProject: (year: number) => {
    return request.get({ url: '/project/kanban/get-change-project/' + year })
  },
  /** 获取Problem数据 */
  getProblemAchieved: (data: any) => {
    return request.post({ url: '/project/kanban/get-problem-achieved', data })
  },
  /** 获取项目工时数据 */
  getWorkHoursNew: (data: any) => {
    return request.post({ url: '/project/kanban/get-working-hours-new', data })
  },
  /** 获取项目节点详情数据 */
  getCruxNode: (params: any) => {
    return request.get({ url: '/project/kanban/get-crux-node', params })
  },
  /** 获取工时数据详情 */
  getWorkHoursDetail: (params: any) => {
    return request.get({ url: '/project/kanban/get-work-hours-detail', params })
  },
  /** 获取项目问题信息 */
  getProblemInfo: (params: any) => {
    return request.get({ url: '/project/kanban/get-problem-content', params })
  },
}
