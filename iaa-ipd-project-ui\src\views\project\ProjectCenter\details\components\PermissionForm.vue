<template>
  <vxe-table
    :data="formData"
    align="center"
    border
    :header-cell-style="{ padding: '0', height: '40px' }"
    :cell-style="onCellStyleEvent"
  >
    <vxe-column title="权限" field="label" width="140" />
    <vxe-column title="拥有该权限的角色" field="roleIds">
      <template #default="{ row }">
        <el-select
          v-model="row.roleIds"
          multiple
          collapse-tags
          :max-collapse-tags="5"
          :disabled="!props.edit"
          @change="
            formDataChange1(
              'roleIds',
              '拥有' + row.label + '的角色',
              row.roleIds,
              permissionList?.find((item) => item.code === row.code)?.roleIds
            )
          "
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
            :label="dict.label"
            :key="dict.value"
            :value="dict.value"
          />
        </el-select>
      </template>
    </vxe-column>
    <vxe-column title="拥有该权限的人员" field="userIds">
      <template #default="{ row }">
        <user-avatar-list
          v-model="row.userIds"
          :user-list="props.userList"
          :limit="3"
          :add="props.edit"
          :size="30"
          :visiable-user-list="visiableUserList"
          @change:msg="
            formDataChange1(
              'userIds',
              '拥有' + row.label + '的人员',
              row.userIds,
              permissionList?.find((item) => item.code === row.code)?.userIds
            )
          "
        />
      </template>
    </vxe-column>
  </vxe-table>
</template>

<script lang="ts" setup>
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user/index'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { BasicsApi, BasicsVO } from '@/api/project/basics'

const formTemplate = [
  { label: '活动分解权限', value: 'activities_disassemble' },
  { label: '活动修改权限', value: 'activities_modify' },
  { label: '活动删除权限', value: 'activities_delete' },
  { label: '活动导出权限', value: 'activities_export' },
  { label: '问题修改权限', value: 'problem_modify' },
  { label: '问题删除权限', value: 'problem_delete' },
  { label: '问题导出权限', value: 'problem_export' },
  { label: '问题导入权限', value: 'problem_import' },
  { label: '风险修改权限', value: 'risk_modify' },
  { label: '风险删除权限', value: 'risk_delete' },
  { label: '风险导出权限', value: 'risk_export' },
  { label: '预审记录修改权限', value: 'technical_modify' },
  { label: '预审记录删除权限', value: 'technical_delete' },
  { label: '预审记录导出权限', value: 'technical_export' },
  { label: '项目文件下载权限', value: 'project_file_download' },
]

const props = defineProps({
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired,
  basicsId: propTypes.number.isRequired,
  edit: propTypes.bool.def(true),
  propFormData: {
    type: Object as PropType<BasicsVO>,
    default: () => {}
  }
})

const visiableUserList = computed(()=>{
  if (!props.propFormData?.teams) return [];
  
  // 提取所有userIds并扁平化
  const allUserIds = props.propFormData.teams
    .map(team => team.userIds || []) // 处理可能的空数组
    .flat();
  
  // 去重（利用Set）
  return Array.from(new Set(allUserIds));
})

const formData = ref<any[]>([])
// 表单修改记录
const modifyInfo = ref<BasicsModifyInfoVO[]>([])
const permissionList = ref<any[]>([])

const onCellStyleEvent = ({ columnIndex }: any) => {
  if (columnIndex == 0) {
    return {
      backgroundColor: '#f1f1f1',
      padding: 0,
      color: '#565656'
    }
  }
  return {
    padding: 0,
    color: '#565656'
  }
}

/** 项目角色变更 */
const formDataChange1 = (
  key: string,
  title: string,
  currentValue: number[],
  beforeValue: number[]
) => {
  // 对两个数组进行排序
  const sortedCurrent = (currentValue||[]).slice().sort((a, b) => a - b)
  const sortedBefore = (beforeValue||[]).slice().sort((a, b) => a - b)
  let compare = true
  // 比较排序后的数组是否相等
  if (sortedCurrent.length !== sortedBefore.length) {
    compare = false // 长度不同，直接返回 false
  }

  for (let i = 0; i < sortedCurrent.length; i++) {
    if (sortedCurrent[i] !== sortedBefore[i]) {
      compare = false // 元素不同，返回 false
    }
  }
  if (compare) {
    modifyInfo.value = modifyInfo.value.filter((item) => item.modifyField !== key)
    return
  }
  modifyInfo.value.push({
    modifyField: key,
    modifyFieldName: title,
    beforeValue: JSON.stringify(beforeValue),
    afterValue: JSON.stringify(currentValue)
  })
}

const getChangeMsg = () => {
  return modifyInfo.value
}

const getPermission=()=>{
  return formData.value
}

watch(
  () => props.basicsId,
  async () => {
    if (!props.basicsId) return
    permissionList.value = await BasicsApi.getPermission(props.basicsId)

    permissionList.value.forEach((item) => {
      formData.value.find((permission) => {
        if (item.code === permission.code) {
          Object.assign(permission.roleIds, item.roleIds)
          Object.assign(permission.userIds, item.userIds)
        }
      })
    })
  },
  { immediate: true }
)

defineExpose({
  getChangeMsg,
  getPermission
})

onMounted(() => {
  formTemplate.forEach((item) => {
    formData.value.push({
      id: undefined,
      label: item.label,
      code: item.value,
      roleIds: [],
      userIds: []
    })
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-select__wrapper.is-disabled){
  background-color: #fff;
}
</style>