<template>
  <div class="flex justify-between items-center">
    <card-title title="申请人" />
    <el-button type="primary" size="small" plain @click="handleAddApplicant"> 新增 </el-button>
  </div>
  <vxe-table
    :data="applicationList"
    border
    size="small"
    class="custom-table"
    :header-cell-config="{ height: 40 }"
    :cell-config="{ height: 40 }"
  >
    <vxe-column type="seq" min-width="30" align="center" />
    <vxe-column field="applicationName" title="姓名或名称" min-width="80" />
    <vxe-column field="nationality" title="国籍" min-width="80" />
    <vxe-column field="certificateType" title="证件类型" min-width="80">
      <template #default="{ row }">
        <dict-tag :type="DICT_TYPE.PATENT_CERTIFICATE_TYPE" :value="row.certificateType" />
      </template>
    </vxe-column>
    <vxe-column field="address" title="详细地址" min-width="180" />
    <vxe-column title="操作" width="150" align="center">
      <template #default="{ row, rowIndex }">
        <el-button type="danger" link size="small" @click="hanldeDeleteRow(row, rowIndex)">
          删除
        </el-button>
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 申请人编辑对话框 -->
  <Dialog
    v-model="applicantDialogVisible"
    :title="applicantDialogTitle"
    width="600px"
    @close="resetApplicantForm"
  >
    <el-form
      ref="applicantFormRef"
      :model="applicantForm"
      :rules="applicantRules"
      label-width="120px"
      size="small"
    >
      <el-form-item label="姓名或名称" prop="applicationName">
        <el-input v-model="applicantForm.applicationName" />
      </el-form-item>
      <el-form-item label="国籍" prop="nationality">
        <el-input v-model="applicantForm.nationality" />
      </el-form-item>
      <el-form-item label="证件类型" prop="certificateType">
        <el-select
          v-model="applicantForm.certificateType"
          placeholder="请选择证件类型"
          style="width: 100%"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_CERTIFICATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="applicantForm.address" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="saveApplicant(true)">继续添加</el-button>
      <el-button type="primary" @click="saveApplicant()">确定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { SubclassApi } from '@/api/patent/subclass'

const message = useMessage()
const loading = ref(false)
const props = defineProps({
  /** 所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析" */
  tableType: propTypes.oneOfType<0 | 1 | 2 | 3 | 4 | 5 | 6>([]).isRequired,
  belongsId: propTypes.number.isRequired, // 主表ID
  formType: propTypes.oneOfType<'create' | 'edit'>([]).isRequired
})

interface Application {
  id?: number
  applicationName?: string
  applicationType?: number
  nationality?: string
  certificateType?: number
  address?: string
  tableType?: number
  belongsId?: number
}

// 申请人数据
const applicationList = ref<any[]>([])

// 申请人表单相关
const applicantDialogVisible = ref(false)
const applicantDialogTitle = ref('')
const applicantFormRef = ref()
const applicantForm = ref<Application>({})
const applicantRules = {
  applicantName: [{ required: true, message: '请输入姓名或名称', trigger: 'blur' }]
}

// 申请人相关方法
const handleAddApplicant = () => {
  applicantDialogTitle.value = '新增申请人'
  resetApplicantForm()
  applicantDialogVisible.value = true
}

const resetApplicantForm = () => {
  applicantForm.value = {}
}

const saveApplicant = async (again: boolean = false) => {
  try {
    await applicantFormRef.value?.validate()
    applicantForm.value.tableType = props.tableType
    applicantForm.value.belongsId = props.belongsId
    if (props.belongsId && props.tableType) {
      await SubclassApi.batchCreate({
        tableType: props.tableType,
        belongsId: props.belongsId,
        applicationList: [applicantForm.value]
      })
      onList()
    } else {
      applicationList.value.push({ ...applicantForm.value })
    }
    resetApplicantForm()
    if (!again) {
      applicantDialogVisible.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const onList = async () => {
  if (!props.belongsId || !props.tableType) return
  loading.value = true
  try {
    const res = await SubclassApi.querySubclass({
      tableType: props.tableType,
      belongsId: props.belongsId,
      queryType: 'application'
    })
    applicationList.value = res.list
  } finally {
    loading.value = false
  }
}

const getData = () => {
  return applicationList.value
}

const hanldeDeleteRow = async (row: Application, rowIndex: number) => {
  if (row?.id) {
    await SubclassApi.deleteById(row.id, 'application')
    onList()
  } else {
    applicationList.value.splice(rowIndex, 1)
  }
  message.success('删除成功')
}

defineExpose({
  onList,
  getData
})
</script>
