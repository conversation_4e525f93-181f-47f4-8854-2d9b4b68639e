<template>
  <vxe-split :action-config="{ direction: 'next' }" class="h-[calc(100vh-90px)] bg-white">
    <vxe-split-pane show-action>
      <div class="p-10px h-full">
        <ContentWrap>
          <el-form inline size="small" class="form-no-bottom-margin">
            <!-- <el-form-item label="全局搜索">
              <el-input />
            </el-form-item> -->
            <el-form-item label="文件搜索">
              <el-input v-model="queryParams.filename" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="clearFilters">清空筛选</el-button>
            </el-form-item>
          </el-form>
        </ContentWrap>
        <div class="h-[calc(100%-60px-34px)]">
          <vxe-table
            ref="tableRef"
            height="100%"
            :header-cell-style="{
              padding: '0',
              height: '2.5rem',
              fontSize: '.9rem',
              backgroundColor: '#fafafa',
              color: 'var(--primary-text-color)'
            }"
            :row-style="{
              cursor: 'pointer'
            }"
            :cell-style="{
              padding: '0',
              height: '2.5rem',
              fontSize: '.9rem',
              color: 'var(--primary-text-color)'
            }"
            round
            border
            auto-resize
            :row-config="{ isCurrent: true, isHover: true, userKey: true, keyField: 'id' }"
            :column-config="{ resizable: true, isHover: true }"
            :data="problemList"
            show-overflow
            :loading="loading"
            @cell-click="(el: any) => problemChange(el.row)"
            :filter-config="{ remote: true }"
            @filter-change="filterChangeEvent"
          >
            <vxe-column
              title="项目"
              field="basicsName"
              width="150"
              align="left"
              :filters="nameOptions"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column
              title="描述"
              field="content"
              width="200"
              align="left"
              :filters="contentOptions"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column title="图片" field="imgIds" width="60" align="center">
              <template #default="{ row }">
                <template v-if="row.imgIds?.length > 0">
                  <img :src="row.imgIds?.[0]" style="width: 1.5vw; height: 1.5vw" />
                  <div
                    style="
                      position: absolute;
                      top: 0;
                      width: 1vw;
                      height: 1vw;
                      background-color: var(--el-color-primary-light-3);
                      border-radius: 1rem;
                      right: 0;
                      color: #fff;
                    "
                    >{{ row.imgIds?.length }}</div
                  >
                </template>
              </template>
            </vxe-column>
            <vxe-column
              title="类型"
              field="type"
              width="90"
              align="center"
              :filters="problemTypeOptions"
            >
              <template #default="{ row }">
                <DictTag type="project_problem_type" :value="row.type" />
              </template>
            </vxe-column>
            <vxe-column title="阶段" field="stage" width="90" align="center">
              <template #default="{ row }">
                {{ templateNode.find((item) => item.id === row.stage)?.name }}
              </template>
            </vxe-column>
            <vxe-column
              title="状态"
              field="status"
              width="95"
              align="center"
              :filters="FilterValue.statusOptions"
            >
              <template #default="{ row }">
                <DictTag :type="'project_activities_status'" :value="row.status" />
              </template>
            </vxe-column>
            <vxe-column title="进度" field="progress" align="center" width="100">
              <template #default="{ row }">
                <el-progress
                  :text-inside="true"
                  :stroke-width="22"
                  :percentage="row.progress"
                  status="success"
                  class="no-radius"
                />
              </template>
            </vxe-column>
            <vxe-column
              title="等级"
              field="level"
              width="80"
              align="center"
              :filters="FilterValue.problemLevelOptions"
            >
              <template #default="{ row }">
                <DictTag :type="'project_problem_level'" :value="row.level" />
              </template>
            </vxe-column>
            <vxe-column
              title="分类"
              field="category"
              width="110"
              align="center"
              :filters="FilterValue.problemCategoryOptions"
            >
              <template #default="{ row }">
                <DictTag :type="'project_problem_category'" :value="row.category" />
              </template>
            </vxe-column>
            <vxe-column
              title="责任模块"
              field="module"
              width="100"
              align="center"
              :filters="FilterValue.problemModuleOptions"
            >
              <template #default="{ row }">
                <DictTag :type="'project_problem_module'" :value="row.module" />
              </template>
            </vxe-column>
            <vxe-column
              title="原因分析"
              field="reason"
              width="200"
              align="left"
              :filters="reasonOptions"
              :filter-render="FilterValue.textFilterRender"
            />
            <vxe-column title="提出部门" field="proposingDepartment" width="100" align="center">
              <template #default="{ row }">
                <DictTag
                  :type="'project_problem_proposing_department'"
                  :value="row.proposingDepartment"
                />
              </template>
            </vxe-column>
            <vxe-column title="不良比例" field="rejectRatio" width="100" align="center">
              <template #default="{ row }">
                {{ row.rejectRatio + (row.rejectRatio ? '%' : '') }}
              </template>
            </vxe-column>
            <vxe-column title="解决措施" field="measures" width="200" align="left" />
            <vxe-column
              title="责任人"
              field="director"
              width="120"
              align="center"
              :filters="directorOptions"
              :filter-render="FilterValue.userFilterRender"
            >
              <template #default="{ row }">
                <user-avatar-list
                  v-model="row.director"
                  :user-list="userList"
                  :size="26"
                  :limit="3"
                  :add="false"
                />
              </template>
            </vxe-column>
            <vxe-column
              title="执行人"
              field="coordinate"
              width="120"
              align="center"
              :filters="coordinateOptions"
              :filter-render="FilterValue.userFilterRender"
            >
              <template #default="{ row }">
                <user-avatar-list
                  v-model="row.coordinate"
                  :user-list="userList"
                  :size="26"
                  :limit="3"
                  :add="false"
                  :visiable-user-list="getVisiableUserList()"
                  @click.stop
                />
              </template>
            </vxe-column>
            <vxe-column title="提出人" field="creator" width="120" align="center">
              <template #default="{ row }">
                <user-avatar-list
                  v-model="row.creator"
                  :user-list="userList"
                  :size="26"
                  :limit="3"
                  :add="false"
                />
              </template>
            </vxe-column>
            <vxe-column
              title="提出时间"
              field="timeOfProposal"
              :formatter="dateFormatter4"
              width="95"
              align="center"
            />
            <vxe-column
              title="计划完成"
              field="timeOfPlan"
              :formatter="dateFormatter4"
              width="95"
              align="center"
            />
            <vxe-column
              title="验证时间"
              field="timeOfVerification"
              :formatter="dateFormatter4"
              width="95"
              align="center"
            />
            <vxe-column title="效果确认" field="effect" width="120" align="center" />
            <vxe-column title="确认人" field="effectPerson" width="120" align="center">
              <template #default="{ row }">
                <user-avatar-list
                  v-model="row.effectPerson"
                  :user-list="userList"
                  :size="26"
                  :limit="3"
                  :add="false"
                />
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <Pagination
          size="small"
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="onList"
        />
      </div>
    </vxe-split-pane>
    <vxe-split-pane>
      <el-empty description="当前未选择问题" v-if="!currentProblem?.id" class="h-full" />
      <div class="p-10px overflow-auto" v-else>
        <el-tabs
          v-model="currentTab"
          class="drawer-tabs position-sticky top-0 z-10 bg-#fff"
          @tab-change="onTabChange"
        >
          <el-tab-pane name="problem" label="问题" />
          <el-tab-pane name="target" label="输出物" />
        </el-tabs>
        <el-collapse
          v-model="activeNames"
          ref="collapseRef"
          class="custom-collapse"
          v-if="currentTab === 'problem'"
        >
          <el-collapse-item title="基础信息" name="1">
            <el-form label-width="90px" ref="formRef" class="custom-form">
              <el-form-item label="描述" prop="content">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="currentProblem.content"
                  :disabled="!false"
                />
              </el-form-item>
              <el-form-item label="图片" prop="imgIds">
                <UploadImgs
                  v-model="currentProblem.imgIds!"
                  height="60px"
                  width="60px"
                  :disabled="!false"
                />
              </el-form-item>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="等级" prop="level">
                    <el-select v-model="currentProblem.level" :disabled="!false">
                      <el-option
                        v-for="dict in getStrDictOptions('project_problem_level')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="分类" prop="category">
                    <el-select v-model="currentProblem.category" :disabled="!false">
                      <el-option
                        v-for="dict in getStrDictOptions('project_problem_category')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="责任模块" prop="module">
                    <el-select v-model="currentProblem.module" :disabled="!false">
                      <el-option
                        v-for="dict in getStrDictOptions('project_problem_module')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="提出部门" prop="proposingDepartment">
                    <el-select v-model="currentProblem.proposingDepartment" :disabled="!false">
                      <el-option
                        v-for="dict in getStrDictOptions('project_problem_proposing_department')"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="阶段" prop="stage">
                    <el-select v-model="currentProblem.stage" :disabled="!false">
                      <el-option
                        v-for="item in templateNode"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="不良比例(%)" prop="rejectRatio">
                    <el-input-number
                      v-model="currentProblem.rejectRatio"
                      :min="1"
                      :max="100"
                      :disabled="!false"
                      class="!w-full"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="状态">
                    <DictTag type="project_activities_status" :value="currentProblem.status!" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="提出时间" prop="timeOfProposal">
                    <el-date-picker
                      type="date"
                      v-model="currentProblem.timeOfProposal"
                      :disabled="!false"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="计划完成" prop="timeOfPlan">
                    <el-date-picker
                      type="date"
                      v-model="currentProblem.timeOfPlan"
                      :disabled="!false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="责任人" prop="director">
                <user-avatar-list
                  v-model="currentProblem.director!"
                  :user-list="userList"
                  :size="28"
                  :limit="10"
                  :add="false"
                  :visiable-user-list="getVisiableUserList()"
                />
              </el-form-item>
              <el-form-item label="执行人">
                <user-avatar-list
                  v-model="currentProblem.coordinate!"
                  :user-list="userList"
                  :size="28"
                  :limit="10"
                  :add="false"
                  :visiable-user-list="getVisiableUserList()"
                />
              </el-form-item>
              <el-form-item label="进度" v-if="currentProblem.id">
                <el-progress
                  :percentage="currentProblem.progress"
                  class="w-100% no-radius"
                  :text-inside="true"
                  :stroke-width="20"
                  status="success"
                />
              </el-form-item>
              <el-form-item label="原因分析" prop="reason">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="currentProblem.reason"
                  :disabled="!false"
                />
              </el-form-item>

              <el-form-item label="解决措施" prop="measures">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="currentProblem.measures"
                  :disabled="!false"
                />
              </el-form-item>

              <template v-if="currentProblem.id && !false">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="验证时间">
                      <el-date-picker
                        type="date"
                        v-model="currentProblem.timeOfVerification"
                        :disabled="true"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="效果确认">
                      <el-input v-model="currentProblem.effect" :disabled="true" />
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="确认人">
                      <user-avatar-list
                        v-model="currentProblem.effectPerson!"
                        :user-list="userList"
                        :size="28"
                        :limit="10"
                        :add="false"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </template>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="跟进记录" name="2" v-show="!false">
            <Comment ref="commentRef" category="problem" :limit="5" bgColor="#fff" />
          </el-collapse-item>
        </el-collapse>
        <template v-else-if="currentTab === 'target'">
          <vxe-table
            class="w-100%"
            :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
            :cell-style="{ padding: '5px', height: '30px' }"
            show-overflow
            :data="attachmentList"
            align="center"
            border
          >
            <vxe-column title="文件名" field="name" min-width="200" align="left">
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
                >
                  {{ row.name }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column title="版本" field="currentVersion" width="60" />
            <vxe-column title="审签状态" field="approvalStatus" width="90">
              <template #default="{ row }">
                <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
              </template>
            </vxe-column>
            <vxe-column
              title="审核通过时间"
              field="approvalTime"
              :formatter="dateFormatter3"
              width="120"
            />
          </vxe-table>
        </template>
      </div>
    </vxe-split-pane>
  </vxe-split>
  <AttachmentPreview ref="attachmentPreviewRef" />
</template>

<script lang="ts" setup>
import { ProblemVO, ProblemApi } from '@/api/project/problem'
import { getVisiableUserList } from '@/views/project/ProjectCenter/main/util/permission'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import { dateFormatter4, dateFormatter3 } from '@/utils/formatTime'
import { UserVO, getAllUser } from '@/api/system/user'
import AttachmentPreview from '@/views/project/ProjectCenter/main/components/AttachmentPreview.vue'
import { AttachmentRespVO } from '@/api/project/attachment'
import { getStrDictOptions } from '@/utils/dict'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { AttachmentApi } from '@/api/project/attachment'

interface Node {
  id: number | string
  name: string
}
const nameOptions = ref([{ data: '' }])
const contentOptions = ref([{ data: '' }])
const reasonOptions = ref([{ data: '' }])
const directorOptions = ref([{ data: [] }])
const coordinateOptions = ref([{ data: [] }])
const problemTypeOptions = computed<Array<{ label: string; value: any }>>(() => {
  return getStrDictOptions('project_problem_type')?.map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
})

const currentProblem = ref<ProblemVO>({})
const problemList = ref<ProblemVO[]>([])
const total = ref(0)
const loading = ref(false)
const userList = ref<UserVO[]>([])
const templateNode = ref<Node[]>([])
const queryParams = ref({
  pageSize: 30,
  pageNo: 1,
  filters: {},
  filename: ''
})
const currentTab = ref('problem')
const attachmentPreviewRef = ref()
const attachmentList = ref<AttachmentRespVO[]>([])
const activeNames = ref(['1', '2'])
const tableRef = ref()
const commentRef = ref()

const onTabChange = () => {
  if (currentTab.value === 'target') {
    onListAttachment()
  } else {
    commentRef.value?.listEvent(currentProblem.value.id)
  }
}

const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'problem',
    dynamicId: currentProblem.value.id
  })
  attachmentList.value = res
}

const onList = async () => {
  loading.value = true
  try {
    const res = await ProblemApi.getProblemBank(queryParams.value)
    problemList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const onListUser = async () => {
  const res = await getAllUser()
  userList.value = res
}

const onListTemplate = async () => {
  templateNode.value = await ActivitiesTemplateApi.listActivitiesTemplate({ parentId: 0 })
}

const problemChange = async (row: any) => {
  currentProblem.value = row

  await nextTick()
  onTabChange()
}

const clearFilters = () => {
  const $table = tableRef.value
  if ($table) {
    // 清除排序状态，如果本地筛选，会自动更新数据
    $table.clearFilter()
    queryParams.value.filters = {}
    queryParams.value.filename = ''
    handleQuery()
  }
}

const filterChangeEvent = ({ filterList }) => {
  filterList.forEach((item) => {
    if (item.datas?.[0]) {
      queryParams.value.filters[item.field] = item.datas.length > 1 ? item.datas : item.datas[0]
    }
    if (item.values?.[0]) {
      queryParams.value.filters[item.field] = item.values.length > 1 ? item.values : item.values[0]
    }
    if (['director', 'coordinate'].includes(item.field)) {
      queryParams.value.filters[item.field] =
        item.datas[0]?.type + '|' + item.datas[0]?.userList.join(',')
    }
  })
  if (filterList.length === 0) {
    queryParams.value.filters = {}
  }
  queryParams.value.pageNo = 1
  onList()
}

const handleQuery = () => {
  queryParams.value.pageNo = 1
  onList()
}

onMounted(() => {
  onList()
  onListUser()
  onListTemplate()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}

.form-no-bottom-margin :deep(.el-form-item) {
  margin-bottom: 0 !important;
}
</style>
