<template>
  <!-- 对话框(添加 / 修改) -->
  <NoModalDrawer :title="dialogTitle" v-model="visible" size="60%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ dialogTitle }}</div>
        <div class="flex" v-if="formType === 'edit'">
          <el-tooltip content="修改">
            <el-button link :loading="loading" @click="onModify()">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除">
            <el-button link :loading="loading" @click="handleDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      :disabled="!edit"
    >
      <el-row>
        <el-col :span="24">
          <CardTitle title="基本信息" />
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="提出人" prop="submittedPeople">
            <el-input v-model="formData.submittedPeople" placeholder="请输入提出人" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="客户" prop="customers">
            <el-input v-model="formData.customers" placeholder="请输入客户" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="产品型号" prop="productModel">
            <TagsInput v-model="formData.productModel" :disabled="!edit" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <CardTitle title="目标专利" />
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="目标专利" prop="targetPatent">
            <el-input v-model="formData.targetPatent" placeholder="请输入目标专利" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="基本信息" prop="targetPatentInformation">
            <el-input
              v-model="formData.targetPatentInformation"
              placeholder="请输入目标专利基本信息"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="保护技术点" prop="targetPatentPoints">
            <el-input
              v-model="formData.targetPatentPoints"
              placeholder="请输入目标专利保护技术点"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="目标专利附图" prop="targetPatentDrawings">
            <UploadImgs v-model="formData.targetPatentDrawings" width="100px" height="100px" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-tabs v-model="currentTab" @tab-change="onTableChange">
      <el-tab-pane label="侵权分析" name="infringement">
        <el-form label-width="160" :disabled="!edit">
          <el-form-item label="侵权分析报告" prop="analysisReport">
            <el-input v-model="formData.analysisReport" placeholder="请输入侵权分析报告" />
          </el-form-item>
          <el-form-item label="规避方案" prop="avoidanceScheme">
            <el-input
              v-model="formData.avoidanceScheme"
              placeholder="如侵权风险较高，合理的规避方案"
            />
          </el-form-item>
          <el-form-item label="是否继续项目" prop="isContinue">
            <el-radio-group v-model="formData.isContinue">
              <el-radio value="0" size="large">是</el-radio>
              <el-radio value="1" size="large">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="专利申请与布局" prop="avoidanceRemark">
            <el-input v-model="formData.avoidanceRemark" placeholder="规避方案的专利申请与布局" />
          </el-form-item>
          <el-form-item label="交叉许可" prop="crossLicensing">
            <el-input v-model="formData.crossLicensing" placeholder="与专利权人许可、交叉许可" />
          </el-form-item>
          <el-form-item label="跟进人" prop="followPerson">
            <el-input v-model="formData.followPerson" placeholder="规避方案跟进人" />
          </el-form-item>
          <el-form-item label="规避方案专利申请号" prop="patentApplication">
            <el-input v-model="formData.patentApplication" placeholder="规避方案的专利申请与布局" />
          </el-form-item>
          <el-form-item label="许可方式、费用及周期" prop="costsCycles">
            <el-input
              v-model="formData.costsCycles"
              placeholder="许可方式、费用及周期"
              type="textarea"
              :rows="5"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="附件" name="attachment">
        <AttachmentInfo
          ref="attachmentInfoRef"
          :table-type="6"
          :belongs-id="formData.id"
          :form-type="formType"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer v-if="formType === 'create'">
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { infringementApi } from '@/api/patent/infringement'
import AttachmentInfo from '../components/AttachmentInfo.vue'
import TagsInput from '../components/TagsInput.vue'
const visible = ref(false)
const formData = ref({
  id: undefined,
  submittedPeople: undefined,
  customers: undefined,
  productModel: [] as string[],
  attachmentIds: [] as string[],
  targetPatent: undefined,
  targetPatentInformation: undefined,
  targetPatentPoints: undefined,
  targetPatentDrawings: [] as string[],
  analysisReport: undefined,
  analysisReportAttachment: [] as string[],
  avoidanceScheme: undefined,
  isContinue: undefined,
  avoidanceRemark: undefined,
  crossLicensing: undefined,
  followPerson: undefined,
  costsCycles: undefined,
  patentApplication: undefined
})
const formRules = reactive({
  productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
  targetPatentInformation: [
    { required: true, message: '请输入目标专利基本信息', trigger: 'change' }
  ],
  targetPatentPoints: [{ required: true, message: '请输入目标专利保护技术点', trigger: 'change' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()
const attachmentInfoRef = ref()
const edit = ref(false)
const currentTab = ref('infringement')

const openForm = async (rowId?: number) => {
  visible.value = true
  if (rowId) {
    formType.value = 'edit'
    dialogTitle.value = '修改产品专利侵权分析'
    const data = await infringementApi.getProductInfringement(rowId)
    // 时间字段转换：时间戳 -> YYYY-MM-DD 字符串
    formData.value = data
    edit.value = false
    await nextTick()
    onTableChange()
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加产品专利侵权分析'
    edit.value = true
    refresh()
  }
}

const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    const attachments = attachmentInfoRef.value?.getData()
    await infringementApi.createProductInfringement({
      ...formData.value,
      attachments
    })
    message.success('创建成功')

    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    submittedPeople: undefined,
    customers: undefined,
    productModel: [] as string[],
    attachmentIds: [] as string[],
    targetPatent: undefined,
    targetPatentInformation: undefined,
    targetPatentPoints: undefined,
    targetPatentDrawings: [] as string[],
    analysisReport: undefined,
    analysisReportAttachment: [] as string[],
    avoidanceScheme: undefined,
    isContinue: undefined,
    avoidanceRemark: undefined,
    crossLicensing: undefined,
    followPerson: undefined,
    costsCycles: undefined,
    patentApplication: undefined
  }
}

const onModify = async () => {
  if (!edit.value) {
    edit.value = true
    return
  } else {
    await formRef.value.validate()
    await infringementApi.updateProductInfringement(formData.value)
    message.success('更新成功')
    emit('success')
  }
}

/** 删除按钮操作 */
const handleDelete = async () => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await infringementApi.deleteProductInfringement(formData.value.id!)
  message.success('删除成功')
  // 刷新列表并保持滚动位置
  emit('success')
}

/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'attachment':
      attachmentInfoRef.value?.onList()
      break
  }
}

defineExpose({
  openForm
})
</script>
<style lang="scss" scoped>
:deep(.el-tabs__header.is-top) {
  background-color: #f1f1f1;
}
</style>
