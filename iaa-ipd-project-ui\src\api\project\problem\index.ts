import request from '@/config/axios'

// 项目问题信息 VO
export interface ProblemVO {
  id?: number // 问题ID
  basicsId?: number // 项目ID
  content?: string // 问题描述
  imgIds?: string[] // 问题图片
  stage?: number // 阶段
  type?: string // 问题类型
  status?: number //问题状态
  progress?: number //问题进度
  level?: string // 问题等级
  category?: string // 问题分类
  module?: string // 责任模块
  reason?: string // 原因分析
  proposingDepartment?: string // 提出部门
  rejectRatio?: number // 不良比例
  measures?: string // 解决措施
  proposal?: number[] //提出人
  director?: number[] // 责任人
  coordinate?: number[] // 责任人
  timeOfProposal?: Date // 问题提出时间
  timeOfPlan?: Date // 计划完成时间
  timeOfActual?: Date // 实际完成时间
  timeOfVerification?: Date // 验证时间
  effect?: string // 效果确认
  effectPerson?: number[] // 确认人
  processInstanceId?: string //当前流程
  share?: boolean // 是否公开
}

// 项目问题信息 API
export const ProblemApi = {
  // 查询项目问题信息分页
  getProblemPage: async (params: any) => {
    return await request.get({ url: `/project/problem/page`, params })
  },
  getProblemExportPage: async (data: any) => {
    return await request.post({ url: `/project/problem/page-export`, data })
  },
  // 查询项目问题信息列表
  getProblemList: async (params: any) => {
    return await request.get({ url: '/project/problem/list', params })
  },

  // 查询项目问题信息详情
  getProblem: async (id: number) => {
    return await request.get({ url: `/project/problem/get?id=` + id })
  },

  // 新增项目问题信息
  createProblem: async (data: ProblemVO) => {
    return await request.post({ url: `/project/problem/create`, data })
  },

  // 修改项目问题信息
  updateProblem: async (data: ProblemVO) => {
    return await request.put({ url: `/project/problem/update`, data })
  },
  // 批量修改项目问题信息
  updateProblemBatch: async (data: ProblemVO[]) => {
    return await request.put({ url: `/project/problem/update-batch`, data })
  },

  // 删除项目问题信息
  deleteProblem: async (id: number) => {
    return await request.delete({ url: `/project/problem/delete?id=` + id })
  },

  // 导出项目问题信息 Excel
  exportProblem: async (data) => {
    return await request.downloadPost({ url: `/project/problem/export-excel`, data })
  },
  // 导出项目问题信息 Excel
  exportProblemMulti: async (data) => {
    return await request.downloadPost({ url: `/project/problem/export-multi`, data })
  },
  // 更新问题反馈
  updateProblemFeedback: async (data: any) => {
    return await request.post({ url: `/project/problem/feedback`, data })
  },
  // 更新问题
  updateProblemCoordinate: async (data: any) => {
    return await request.post({ url: `/project/problem/coordinate`, data })
  },
  /** 获取各阶段问题数量 */
  getStageCount: async (basicsId: number) => {
    return await request.get({ url: `/project/problem/get-stage-count/${basicsId}` })
  },
  /** 更新问题公开状态 */
  updateProblemShare: async (params: any) => {
    return await request.get({ url: '/project/problem/update-problem-share', params })
  },
  /** 获取问题库分页 */
  getProblemBank: async (data: any) => {
    return await request.post({ url: '/project/problem/get-problem-bank', data })
  },
  /** 导出问题模板 */
  exportProblemTemplate:async()=>{
    return await request.download({url:`/project/problem/export-template`})
  }
}
