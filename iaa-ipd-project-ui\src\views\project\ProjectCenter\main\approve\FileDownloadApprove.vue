<template>
  <el-form label-width="100px">
    <el-form-item> 项目输出物 下载权限申请审批单 </el-form-item>
    <el-form-item label="申请人">
      <UserAvatarList v-model="formData.creator" :user-list="userList" :add="false" :size="30" />
    </el-form-item>
    <el-form-item label="项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script lang="ts" setup>
import { AttachmentDownloadFlowApi } from '@/api/bpm/attachment-download'
import { UserVO, getSimpleUserList } from '@/api/system/user'
const { query } = useRoute()

const formData = ref<any>({})
const userList = ref<UserVO[]>([])
const getFlow = async () => {
  const id = query.id as string
  const res = await AttachmentDownloadFlowApi.getAttachmentDownloadFlow(id)
  formData.value = res
}

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

onMounted(() => {
  getFlow()
  getUserList()
})
</script>
