<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="评审分类" prop="category">
        <el-input v-model="formData.category" placeholder="请输入评审分类" />
      </el-form-item>
      <el-form-item label="项目决策评审方面" prop="aspect">
        <el-input v-model="formData.aspect" placeholder="请输入项目决策评审方面" />
      </el-form-item>
      <el-form-item label="需要关注的内容" prop="attention">
        <el-input v-model="formData.attention" placeholder="请输入需要关注的内容" />
      </el-form-item>
      <el-form-item label="考虑的问题" prop="problem">
        <el-input v-model="formData.problem" placeholder="请输入考虑的问题" />
      </el-form-item>
      <el-form-item label="评审负责人" prop="director">
        <UserAvatarList
          v-model="formData.director!"
          :size="28"
          :limit="5"
          :user-list="props.userList"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DecisionTemplateApi, DecisionTemplateVO } from '@/api/project/decisiontemplate'
import { UserVO } from '@/api/system/user'
import { propTypes } from '@/utils/propTypes'

/** 项目决策评审模板 表单 */
defineOptions({ name: 'DecisionTemplateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  category: undefined,
  aspect: undefined,
  attention: undefined,
  problem: undefined,
  director: undefined
})
const formRules = reactive({
  category: [{ required: true, message: '评审分类不能为空', trigger: 'blur' }],
  // aspect: [{ required: true, message: '项目决策评审方面不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DecisionTemplateApi.getDecisionTemplate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DecisionTemplateVO
    if (formType.value === 'create') {
      await DecisionTemplateApi.createDecisionTemplate(data)
      message.success(t('common.createSuccess'))
    } else {
      await DecisionTemplateApi.updateDecisionTemplate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    category: undefined,
    aspect: undefined,
    attention: undefined,
    problem: undefined,
    director: undefined
  }
  formRef.value?.resetFields()
}
</script>
