import { BasicsModifyInfoVO } from '@/api/bpm/basics'

export const formDataChangeString = (
  key: string,
  title: string,
  currentValue: string,
  beforeValue: string,
  modifyInfo: BasicsModifyInfoVO[]
) => {
  if (currentValue === beforeValue) {
    // 使用 === 比较
    // 直接修改原数组：通过 findIndex 找到需要移除的项并 splice
    const index = modifyInfo.findIndex((item) => item.modifyField === key)
    if (index !== -1) {
      modifyInfo.splice(index, 1)
    }
    return
  }
  const exists = modifyInfo.find((item) => item.modifyField === key)
  if (exists) {
    exists.beforeValue = beforeValue
    exists.afterValue = currentValue
  } else {
    modifyInfo.push({
      modifyField: key,
      modifyFieldName: title,
      beforeValue: beforeValue,
      afterValue: currentValue
    })
  }
}

/** 项目角色变更 */
export const formDataChangeArray = (
  key: string,
  title: string,
  currentValue: number[],
  beforeValue: number[],
  modifyInfo: BasicsModifyInfoVO[]
) => {
  // 对两个数组进行排序
  const sortedCurrent = currentValue?.slice()?.sort((a, b) => a - b)
  const sortedBefore = beforeValue?.slice()?.sort((a, b) => a - b)
  let compare = true
  // 比较排序后的数组是否相等
  if (sortedCurrent?.length !== sortedBefore?.length) {
    compare = false // 长度不同，直接返回 false
  }

  for (let i = 0; i < sortedCurrent?.length; i++) {
    if (sortedCurrent?.[i] !== sortedBefore?.[i]) {
      compare = false // 元素不同，返回 false
    }
  }
  if (compare) {
    // 使用 === 比较
    // 直接修改原数组：通过 findIndex 找到需要移除的项并 splice
    const index = modifyInfo.findIndex((item) => item.modifyField === key)
    if (index !== -1) {
      modifyInfo.splice(index, 1)
    }
    return
  }
  const exists = modifyInfo.find((item) => item.modifyField === key)

  if (exists) {
    exists.beforeValue = JSON.stringify(beforeValue)
    exists.afterValue = JSON.stringify(currentValue)
  } else {
    modifyInfo.push({
      modifyField: key,
      modifyFieldName: title,
      beforeValue: JSON.stringify(beforeValue),
      afterValue: JSON.stringify(currentValue)
    })
  }
}
