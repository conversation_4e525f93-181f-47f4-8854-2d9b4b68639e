<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" v-loading="loading">
    <div v-if="baseInfoList && baseInfoList.length > 0">
      <div>请确认以下项目归属：</div>
      <div v-for="(base, index) in baseInfoList" :key="index" class="base-item">
        <div class="base-item-title">{{ base.name }}</div>
        <div>
          <el-select multiple placeholder="请选择项目组，可选多个" v-model="base.categoryIds">
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div v-else>当前项目组下无项目，可放心删除</div>
    <template #footer>
      <el-button @click="submitForm">确认</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { BasicsApi, BasicsVO } from '@/api/project/basics'
import { CategoryApi, CategoryVO } from '@/api/project/category'

const baseInfoList = ref<BasicsVO[]>([]) //项目列表
const categoryList = ref<CategoryVO[]>([])
const loading = ref(false) //加载状态
const dialogTitle = ref('项目组删除')
const dialogVisible = ref(false)
const id = ref<number>(undefined as unknown as number)

const message = useMessage() // 消息弹窗
const emits = defineEmits(['success'])

/** 查询当前分组项目列表 */
const getBaseInfoList = async (categoryId: number) => {
  baseInfoList.value = []
  loading.value = true
  try {
    const data = await BasicsApi.getBasicsList({ categoryId: categoryId })
    baseInfoList.value = data
    for (let base of baseInfoList.value) {
      base.categoryIds = base.categoryIds?.filter((item) => item != categoryId)
    }
  } finally {
    loading.value = false
  }
}
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  sort: undefined,
  customActivity: undefined,
  createTime: []
})
/** 查询列表 */
const getCategoryList = async () => {
  loading.value = true
  try {
    const data = await CategoryApi.getCategoryList(queryParams)
    categoryList.value = data
  } finally {
    loading.value = false
  }
}

/** 打开弹窗 */
const openForm = async (categoryName: string, categoryId: number) => {
  id.value=categoryId;
  await getCategoryList()
  categoryList.value = categoryList.value.filter((item) => item.id != categoryId)
  dialogTitle.value = categoryName + '组删除'
  await getBaseInfoList(categoryId)
  dialogVisible.value = true
}

/** 提交表单 */
const submitForm = async() => {
  for(let base of baseInfoList.value){
    if(base.categoryIds!.length<=0){
      message.error('项目组删除失败，请确认项目是否已分配项目组');
      return;
    }
  }
  for(let base of baseInfoList.value){
    await BasicsApi.updateBasics(base);
  }
  await CategoryApi.deleteCategory(id.value)
  message.success('项目组删除成功');
  emits('success');
  dialogVisible.value=false;
}

defineExpose({ openForm })
</script>

<style lang="scss" scoped>
.base-item {
  padding: 5px;
  border: 0.3px dashed var(--el-color-primary);
  margin-bottom: 10px;
  .base-item-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--regular-text-color);
  }
}
</style>
