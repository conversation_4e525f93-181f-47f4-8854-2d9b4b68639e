import request from '@/config/axios'



export const OperationApi = {
  /** 分页获取知识产权运营 */
  getOperationPage: (data: any) => {
    return request.post({ url: '/patent/operation/page', data })
  },
  /** 创建知识产权运营 */
  createOperation: (data: any) => {
    return request.post({ url: '/patent/operation/create', data })
  },
  /** 更新知识产权运营 */
  updateOperation: (data: any) => {
    return request.post({ url: '/patent/operation/update', data })
  },
  /** 删除知识产权运营 */
  deleteOperation: (id: number) => {
    return request.get({ url: `/patent/operation/delete/${id}` })
  },
  /** 查询知识产权运营详情 */
  getOperation: async (id: any) => {
    return await request.get({ url: `/patent/operation/get?id=` + id })
  }
}
