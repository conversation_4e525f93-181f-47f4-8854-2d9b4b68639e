<template>
  <Dialog title="用户选择" v-model="dialogVisible" width="60%" append-to-body>
    <el-row :gutter="20">
      <el-col :span="6">
        <ContentWrap>
          <DeptTree @node-click="handleDeptNodeClick" />
        </ContentWrap>
      </el-col>
      <el-col :span="11">
        <el-card shadow="never">
          <template #header>
            <div class="user-header">
              <span>用户信息</span>
              <el-input
                v-model="username"
                type="text"
                style="width: 200px"
                placeholder="请输入用户名"
                size="small"
              >
                <template #prefix>
                  <Icon icon="ep:search" />
                </template>
              </el-input>
            </div>
          </template>
          <div class="user-container">
            <el-tag
              v-for="user in tempUserList.filter((item) => item.id != 1)"
              :key="user.id"
              @click="checkedUser(user)"
              size="large"
            >
              {{ user.nickname }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
      <el-col :span="7">
        <el-card shadow="never">
          <template #header>
            <div class="user-header">
              <span>已选用户</span>
              <el-button type="danger" size="small" plain @click="clearAllCheckUser"
                >清空</el-button
              >
            </div>
          </template>
          <div class="user-container">
            <el-tag
              v-for="user in checkedSelectedUser"
              :key="user.id"
              type="success"
              size="large"
              closable
              @close="uncheckedUser(user)"
              class="w-70px"
            >
              {{ user.nickname }}
            </el-tag>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <el-button type="primary" @click="success">确定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import DeptTree from './DeptTree.vue'

const dialogVisible = ref(false) //用户选择窗口显示
const tempUserList = ref<UserVO[]>([]) //当前展示的UserList
const selectedUsers = ref<number[]>([]) //排除显示的用户
const checkedSelectedUser = ref<UserVO[]>([]) //已选中的用户
const props = defineProps({
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired
})
const username = ref('')
const message = useMessage()

/** 部门树选择列表 */
const handleDeptNodeClick = (row: any) => {
  let ids = [row.id]
  getDeptIds(row, ids)
  tempUserList.value = props.userList
    .filter((item) => !selectedUsers.value.includes(item.id))
    .filter((item) => ids.includes(item.deptId))
}
/** 递归获取当前部门下的所有部门ID */
const getDeptIds = (row: any, ids: number[]) => {
  if (row.children) {
    row.children.forEach((element: any) => {
      ids.push(element.id)
      getDeptIds(element, ids)
    })
  }
}
/** 显示用户选择窗体 */
const showDialog = (selecteds: number[]) => {
  selectedUsers.value = selecteds
  tempUserList.value = props.userList.filter((item) => !selecteds.includes(item.id))
  dialogVisible.value = true
}

const emits = defineEmits(['checked'])
/** 选中用户 */
const checkedUser = (user: UserVO) => {
  const exists = checkedSelectedUser.value.some((item) => item.id === user.id)
  if (!exists) checkedSelectedUser.value.push(user)
}
/** 取消选择用户 */
const uncheckedUser = (user: UserVO) => {
  const index = checkedSelectedUser.value.findIndex((item) => item.id === user.id)
  if (index !== -1) checkedSelectedUser.value.splice(index, 1)
}
/** 确认选择用户 */
const success = () => {
  dialogVisible.value = false
  emits('checked', checkedSelectedUser.value)
  checkedSelectedUser.value = []
}

const clearAllCheckUser = async () => {
  await message.confirm('确定清空已选用户？')
  checkedSelectedUser.value = []
}

watch(username, (val) => {
  tempUserList.value = props.userList
    .filter((item) => !selectedUsers.value.includes(item.id))
    .filter((item) => item.nickname.includes(val))
})

defineExpose({
  showDialog
})
</script>

<style scoped lang="scss">
.user-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}
.user-container {
  display: grid;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  grid-gap: 15px;
  height: 50vh;
  overflow: auto;
  align-items: start;
  align-content: flex-start;

  .el-tag {
    margin: 5px;
    cursor: pointer;
    height: 30px;
  }
}

.is-fullscreen .user-container {
  height: 75vh !important;
}
</style>
