<template>
  <el-drawer
    v-model="visible"
    :title="props.title"
    :modal="showModal"
    :direction="props.direction"
    :show-close="false"
    :destroy-on-close="true"
    :before-close="props.beforeClose"
    :size="props.size"
    :modal-class="!showModal ? 'no-modal-mask-layer' : ''"
    ref="drawerRef"
  >
    <template #header v-if="$slots.header">
      <slot name="header"> </slot>
    </template>
    <div :id="`close-btn${props.id}`" class="position-fixed z-index-999">
      <el-button type="danger" style="width: 20px; height: 80px;padding: 0" @click="close">
        <Icon icon="fa-solid:angle-double-right" />
      </el-button>
    </div>
    <slot name="default"></slot>
    <template #footer v-if="$slots.footer">
      <slot name="footer"> </slot>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  title: propTypes.string.def(''),
  direction: {
    type: String as PropType<'ltr' | 'rtl' | 'ttb' | 'btt'>,
    default: 'rtl'
  },
  size: propTypes.string.def('30%'),
  beforeClose: {
    type: Function as PropType<(done: () => void) => void>,
    default: () => {
      return () => {}
    }
  },
  showModal: propTypes.bool.def(false),
  id: propTypes.string.def('')
})

const visible = ref(false)
const drawerRef = ref()

const initDrawer = async () => {
  await nextTick()
  const closeBtn = document.getElementById(`close-btn${props.id}`)! as HTMLElement
  if (closeBtn) {
    switch (props.direction) {
      case 'ltr':
        closeBtn.style.top = 'calc(50% - 25px)'
        closeBtn.style.left = props.size
        break
      case 'rtl':
        closeBtn.style.top = 'calc(50% - 80px)'
        closeBtn.style.left = 'calc(100vw - ' + props.size + ')'
        break
      case 'ttb':
        closeBtn.style.width = '50px'
        closeBtn.style.height = '30px'
        closeBtn.style.top = props.size
        closeBtn.style.left = 'calc(50vw - 25px)'
        break
      case 'btt':
        closeBtn.style.width = '50px'
        closeBtn.style.height = '30px'
        closeBtn.style.top = 'calc(100vh - ' + props.size + ' - 30px)'
        closeBtn.style.left = 'calc(50vw - 25px)'
        break
    }
  }
}

// getter
watch(
  () => props.modelValue,
  () => {
    visible.value = props.modelValue
    if (props.modelValue) {
      initDrawer()
    } else {
      const maskLayer = document.querySelector('.no-modal-mask-layer')! as HTMLElement
      if (maskLayer) {
        maskLayer.style.display = 'none'
      }
      drawerRef.value?.handleClose()
    }
  },
  { immediate: true }
)
const emit = defineEmits(['update:modelValue'])

const close = () => {
  const maskLayer = document.querySelector('.no-modal-mask-layer')! as HTMLElement
  if (maskLayer) {
    maskLayer.style.display = 'none'
  }
  drawerRef.value.handleClose()
  emit('update:modelValue', false)
}
</script>
