<template>
  <div class="flex justify-between items-center">
    <CardTitle title="同族专利" />
    <el-button type="primary" plain @click="sameVisiable = true" :disabled="false">添加行</el-button>
  </div>
  <vxe-table
    :data="groupInfoList"
    align="center"
    border
    show-overflow
    :header-cell-config="{ height: 40 }"
    :cell-config="{ height: 40 }"
  >
    <vxe-column type="seq" width="50" />
    <vxe-column title="公开号" field="publicNo" />
    <vxe-column title="专利号" field="patentNo" />
    <vxe-column title="专利名称" field="patentName" />
    <vxe-column title="操作" field="operator">
      <template #default="{ row, rowIndex }">
        <el-button type="danger" link @click="hanldeDeleteRow(row, rowIndex)">删除</el-button>
      </template>
    </vxe-column>
  </vxe-table>
  <!-- <div class="flex justify-between">
    <el-button class="!w-100%" type="primary" plain @click="sameVisiable = true">添加行</el-button>
    <el-button class="!w-45%" type="warning" plain>引用</el-button>
  </div> -->
  <Dialog title="添加同族专利" v-model="sameVisiable">
    <el-form
      ref="formRef"
      class="custom-form"
      label-width="100"
      :model="addFormData"
      :rules="addFormDataRules"
    >
      <el-form-item label="公开号" prop="publicNo">
        <el-input v-model="addFormData.publicNo" />
      </el-form-item>
      <el-form-item label="专利号">
        <el-input v-model="addFormData.patentNo" />
      </el-form-item>
      <el-form-item label="专利名" prop="patentName">
        <el-input v-model="addFormData.patentName" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="saveRow(true)">继续添加</el-button>
      <el-button type="primary" plain @click="saveRow()">添加</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import { SubclassApi } from '@/api/patent/subclass'

const props = defineProps({
  /** 所属表：0 查询词管理,1 专利数据库,2 专利情报,3 专利挖掘与布局,4 专利维保,5 专利运营,6 产品专利侵权分析" */
  tableType: propTypes.oneOfType<0 | 1 | 2 | 3 | 4 | 5 | 6>([]).isRequired,
  belongsId: propTypes.number.isRequired, // 主表ID
  formType: propTypes.oneOfType<'create' | 'edit'>([]).isRequired
})
interface GroupInfo {
  id?: number
  tableType?: number
  belongsId?: number
  publicNo?: string
  patentNo?: string
  patentName?: string
  inputMethod?: number
  referenceId?: number
}
const groupInfoList = ref<GroupInfo[]>([])
const sameVisiable = ref(false)
const addFormData = ref<GroupInfo>({})
const addFormDataRules = reactive({
  publicNo: [{ required: true, message: '请输入公开号', trigger: 'blur' }],
  patentName: [{ required: true, message: '请输入专利名称', trigger: 'blur' }]
})
const formRef = ref()
const loading = ref(false)
const message = useMessage()

const saveRow = async (again: boolean = false) => {
  await formRef.value?.validate()
  loading.value = true
  try {
    addFormData.value.tableType = props.tableType
    addFormData.value.belongsId = props.belongsId
    addFormData.value.inputMethod = 1
    if (props.tableType && props.belongsId) {
      await SubclassApi.batchCreate({
        tableType: props.tableType,
        belongsId: props.belongsId,
        groupList: [addFormData.value]
      })
      onList()
    } else {
      groupInfoList.value.push({ ...addFormData.value })
    }
    refresh()
    if (!again) {
      sameVisiable.value = false
    }
  } finally {
    loading.value = false
  }
}

const onList = async () => {
  if (!props.belongsId || !props.tableType) {
    return
  }
  loading.value = true
  try {
    const res = await SubclassApi.querySubclass({
      tableType: props.tableType,
      belongsId: props.belongsId,
      queryType: 'group'
    })
    groupInfoList.value = res.list
  } finally {
    loading.value = false
  }
}

const hanldeDeleteRow = async (row: GroupInfo, rowIndex: number) => {
  if (row?.id) {
    await SubclassApi.deleteById(row.id,'group')
    onList()
  } else {
    groupInfoList.value.splice(rowIndex, 1)
  }
  message.success('删除成功')
}

const refresh = () => {
  addFormData.value = {}
}
const getData = () => {
  return groupInfoList.value
}

defineExpose({
  onList,
  getData
})
</script>
