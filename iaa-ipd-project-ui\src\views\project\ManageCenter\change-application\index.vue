<template>
  <div class="h-full overflow-auto">
    <!-- ADCP 按期达成 -->
    <el-card shadow="never">
      <template #header>
        <el-button type="primary" plain size="small" @click="openForm()">发起变更</el-button>
      </template>
      <!-- 列表 -->
      <div class="h-[calc(100vh-150px)]">
        <div class="h-[calc(100%-52px)]">
          <vxe-table
            height="100%"
            :header-cell-config="{ height: 40 }"
            :cell-config="{ height: 40 }"
            :data="list"
            :loading="loading"
            border
            show-overflow
            align="center"
          >
            <vxe-column title="申请人" field="userId" width="100">
              <template #default="{ row }">
                {{ getUserNickname(row.userId) }}
              </template>
            </vxe-column>
            <vxe-column title="申请日期" field="applicationDate" width="100">
              <template #default="{ row }">
                {{ formatToDate(row.applicationDate) }}
              </template>
            </vxe-column>
            <vxe-column title="变更项目" field="basicsId" width="200" align="left">
              <template #default="{ row }">
                {{ basicsList.find((basics) => basics.id === row.basicsId)?.name }}
              </template>
            </vxe-column>
            <vxe-column title="项目所处节点" field="currentCrux" width="200" align="left">
              <template #default="{ row }">
                {{
                  basicsList.find((item) => item.id === row.basicsId)?.currentCruxName ||
                  '项目所有已分解关键节点已完成'
                }}
              </template>
            </vxe-column>
            <vxe-column title="销售代表" field="salesUserIds" width="120">
              <template #default="{ row }">
                {{ getUserNickname(row.salesUserIds) }}
              </template>
            </vxe-column>
            <vxe-column title="产品代表" field="productUserIds" width="120">
              <template #default="{ row }">
                {{ getUserNickname(row.productUserIds) }}
              </template>
            </vxe-column>
            <vxe-column title="变更类型" field="changeType" width="120">
              <template #default="{ row }">
                {{ row.changeType == 1 ? '客户需求变更' : '产品需求变更' }}
              </template>
            </vxe-column>
            <vxe-column title="是否涉及成本" field="hasCost" width="120">
              <template #default="{ row }">
                {{ getDictLabel('infra_boolean_string', row.hasCost) }}
              </template>
            </vxe-column>
            <vxe-column title="变更原因" field="reason" min-width="150" />
            <vxe-column title="状态" field="status" width="100">
              <template #default="{ row }">
                <DictTag type="project_target_approval_status" :value="row.status" />
              </template>
            </vxe-column>
            <vxe-column title="操作" field="operation" width="150" fiexd="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="toBpm(row.processInstanceId)">
                  跳转流程
                </el-button>
                <el-button
                  type="primary"
                  link
                  size="small"
                  v-if="row.status == 0 && row.creator === getUser.id"
                  @click="delAttachment(row)"
                >
                  取消
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <!-- 分页 -->
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          size="small"
        />
      </div>
    </el-card>
    <Dialog title="发起变更申请" v-model="formVisible" width="60%">
      <ChangeApplicationForm ref="changeApplicationFormRef" />
      <template #footer>
        <el-button type="primary" @click="onSubmit" :loading="loading">发起流程</el-button>
      </template>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { formatToDate } from '@/utils/dateUtil'
import ChangeApplicationForm from './ChangeApplicationForm.vue'
import { ChangeApplicationApi } from '@/api/bpm/change-application'
import { getDictLabel } from '@/utils/dict'
import { getSimpleUserList } from '@/api/system/user'
import { BasicsApi } from '@/api/project/basics'
import { useUserStore } from '@/store/modules/user'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { propTypes } from '@/utils/propTypes'

const formVisible = ref(false)
const { getUser } = useUserStore()

const props = defineProps({
  basicsId: propTypes.number
})

const list = ref<any[]>([])
const total = ref(0)
const queryParams = ref({
  pageNo: 1,
  pageSize: 20,
  basicsId: undefined
})
const loading = ref(false)
const changeApplicationFormRef = ref()
const message = useMessage()
const userList = ref<any[]>([])
const basicsList = ref<any[]>([])
const router = useRouter()

const getList = async () => {
  loading.value = true
  try {
    queryParams.value.basicsId = props.basicsId as any
    const res = await ChangeApplicationApi.pageFlow(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const getUserList = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}

const getUserNickname = (ids: number | number[]) => {
  if (typeof ids === 'number') {
    return userList.value.find((user) => user.id === ids)?.nickname
  } else {
    return userList.value
      .filter((user) => ids.includes(user.id))
      ?.map((user) => user.nickname)
      ?.join(',')
  }
}

const onSubmit = async () => {
  loading.value = true
  try {
    const data = await unref(changeApplicationFormRef)?.getData()
    await ChangeApplicationApi.createFlow(data)
    unref(changeApplicationFormRef)?.refresh()
    formVisible.value = false
    getList()
    message.success('流程创建成功')
  } finally {
    loading.value = false
  }
}

const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入取消原因', '取消流程', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })

  await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  message.success('删除成功')
  getList()
}

const getBasicsList = async () => {
  const res = await BasicsApi.getBasicsChangeApplicationList()
  basicsList.value = res
}

const openForm = (row?: any) => {
  unref(changeApplicationFormRef)?.refresh(row)
  formVisible.value = true
}

const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

onMounted(async () => {
  await getUserList()
  await nextTick()
  getBasicsList()
  getList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 5px;
}
:deep(.el-card__header) {
  padding: 10px 5px;
}
</style>
