<template>
  <div class="comment-input position-relative bg-#fff">
    <UploadImgs
      v-model="imgs"
      height="60px"
      width="60px"
      class="position-absolute top--62px left-0px"
      v-if="imgView === true"
    />
    <el-mention
      ref="mentionRef"
      v-model="comment"
      :options="options"
      type="textarea"
      whole
      :prefix="['@']"
      :placeholder="props.placeholder"
      :check-is-whole="checkIsWhole"
      @search="handleSearch"
      @select="handleSelect"
      @keydown.enter.stop="onSend"
      v-loading="props.loading"
    />
    <div class="flex justify-between p-x-10px">
      <div class="editor !w-80px">
        <el-tooltip content="图片">
          <el-button link type="primary" @click="imgView = !imgView">
            <img src="@/assets/svgs/img.svg" class="w-2rem h-2rem" />
          </el-button>
        </el-tooltip>
        <el-tooltip content="提及他人" v-if="props.at">
          <el-button link type="primary" @click="onAppendValue('@')">
            <span style="font-size: 1.5rem">@</span>
          </el-button>
        </el-tooltip>
        <!-- <el-tooltip content="活动链接" v-if="props.sharp">
          <el-button link type="primary" @click="onAppendValue('#')">
            <span style="font-size: 1.5rem">#</span>
          </el-button>
        </el-tooltip> -->
      </div>

      <div class="send !w-2.5rem">
        <el-tooltip content="发送">
          <el-button link @click="onSend" :loading="props.loading">
            <img src="@/assets/svgs/send.svg" class="w-2rem h-2rem" />
          </el-button>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ActivitiesVO } from '@/api/project/activities'
import { UserVO } from '@/api/system/user'
import { propTypes } from '@/utils/propTypes'
import type { MentionOption } from 'element-plus'
import { CommentVO } from './comment'
import { debounce } from 'min-dash'

const props = defineProps({
  modelValue: propTypes.oneOfType<CommentVO>([]).def({ content: '', imgs: '' }),
  placeholder: propTypes.string.def('请输入'),
  userList: propTypes.oneOf<UserVO[]>([]).isRequired,
  activitiesList: propTypes.oneOf<ActivitiesVO[]>([]).def([]),
  at: propTypes.bool.def(true),
  sharp: propTypes.bool.def(true),
  loading: propTypes.bool.def(false)
})

const comment = ref('')
const imgs = ref<string | string[]>('')
const imgView = ref(false)
const options = ref<MentionOption[]>([])
const mentionRef = ref()

const MOCK_DATA: Record<string, string[]> = {
  '@': [],
  '#': []
}
const notSend = ref(false)
const handleSearch = (_: string, prefix: string) => {
  notSend.value = true
  options.value = (MOCK_DATA[prefix] || []).map((value) => ({
    value
  }))
}

const handleSelect = () => {
  setTimeout(() => {
    notSend.value = false
  }, 200)
}

const onAppendValue = (value: string) => {
  comment.value += value
  nextTick(() => {
    const textarea = mentionRef.value?.$el?.querySelector('.el-textarea__inner')
    if (textarea) {
      ;(textarea as HTMLTextAreaElement).focus()
    }
  })
}
/** 检查是否完整的删除 */
const checkIsWhole = (pattern: string, prefix: string) => {
  return (MOCK_DATA[prefix] || []).includes(pattern)
}

const emit = defineEmits(['update:modelValue', 'send'])

const onChange = () => {
  emit('update:modelValue', { content: comment.value, imgs: imgs.value })
}

const onSend = debounce(() => {
  if (notSend.value) return
  emit('send', { content: comment.value, imgs: imgs.value })
}, 100)

watch(
  () => [comment.value, imgs.value],
  () => onChange()
)

watch(
  () => props.modelValue,
  () => {
    comment.value = props.modelValue.content
    imgs.value = props.modelValue.imgs
  },
  { immediate: true }
)
watch(
  () => props.userList,
  () => {
    if (props.userList) {
      MOCK_DATA['@'] = props.userList.map((user) => `${user.nickname}[${user.id}]`)
    }
  },
  { immediate: true }
)

watch(
  () => props.activitiesList,
  () => {
    MOCK_DATA['#'] = props.activitiesList.map((activity) => `${activity.name}[${activity.id}]`)
  }
)
</script>

<style lang="scss" scoped>
:deep(.el-textarea),
:deep(.el-textarea__wrapper),
:deep(.el-textarea__inner) {
  width: 100% !important;
  box-shadow: none;

  &:hover,
  &.is-focus {
    box-shadow: none;
  }
}

.comment-input {
  border: 0.3px solid #e6e6e6;
  border-radius: 5px;
}
</style>
