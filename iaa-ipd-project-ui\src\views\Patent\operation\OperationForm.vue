<template>
  <!-- 对话框(添加 / 修改) -->
  <NoModalDrawer :title="dialogTitle" v-model="visible" size="80%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ dialogTitle }}</div>
        <div class="flex" v-if="formType === 'edit'">
          <el-tooltip content="修改">
            <el-button link :loading="loading" @click="onModify()">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除">
            <el-button link :loading="loading" @click="handleDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <vxe-split class="h-100%" width="100%">
      <vxe-split-pane>
        <div class="p-10px">
          <el-form
            ref="formRef"
            :model="maintenanceData"
            v-loading="loading"
            label-width="100px"
            class="custom-form"
            size="small"
            :disabled="true"
          >
            <el-row>
              <el-col :span="24">
                <card-title title="著录项信息" />
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="公开号" prop="publicNo">
                  <el-input v-model="maintenanceData.publicNo" />
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="专利名称" prop="patentName">
                  <el-input v-model="maintenanceData.patentName" />
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="专利类型" prop="patentType">
                  <el-select
                    v-model="maintenanceData.patentType"
                    placeholder="请选择法律状态"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="申请状态" prop="status">
                  <el-select
                    v-model="maintenanceData.status"
                    placeholder="请选择专利申请状态"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_MAINTENANCE_STATUS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="申请号" prop="applicationNo">
                  <el-input v-model="maintenanceData.applicationNo" />
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="申请日期" prop="applicationDate">
                  <el-date-picker
                    v-model="maintenanceData.applicationDate"
                    value-format="YYYY-MM-DD"
                    placeholder="申请日期"
                    class="!w-100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="获取方式" prop="acquisitionMethod">
                  <el-input v-model="maintenanceData.acquisitionMethod" />
                </el-form-item>
              </el-col>

              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="授权日期" prop="authorizationDate">
                  <el-date-picker
                    v-model="maintenanceData.authorizationDate"
                    value-format="YYYY-MM-DD"
                    placeholder="申请日期"
                    class="!w-100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="有效期(年数)" prop="validityPeriod">
                  <el-input v-model="maintenanceData.validityPeriod" type="number" />
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="法律状态" prop="legalStatus">
                  <el-select
                    v-model="maintenanceData.legalStatus"
                    placeholder="请选择法律状态"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24" :sm="12" :md="12">
                <el-form-item label="当前权力人" prop="ownership">
                  <el-input v-model="maintenanceData.ownership" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="维持评估" prop="maintenanceAssessment">
                  <el-input
                    v-model="maintenanceData.maintenanceAssessment"
                    type="textarea"
                    :rows="5"
                    placeholder="请输入维持评估"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-tabs
            v-model="currentTab"
            class="position-sticky top-0 z-10 bg-#fff"
            @tab-change="onTableChange"
          >
            <el-tab-pane label="保护信息" name="function">
              <el-form label-width="100px" class="custom-form mt-10px">
                <el-form-item label="保护技术点" prop="protectionPoint">
                  <el-input v-model="maintenanceData.protectionPoint" :disabled="true" />
                </el-form-item>
                <el-form-item label="保护产品型号" prop="models">
                  <TagsInput v-model="maintenanceData.models" :disabled="true" />
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="人员信息" name="person">
              <InventorInfo
                ref="inventorInfoRef"
                :table-type="4"
                :belongs-id="maintenanceData.id"
                form-type="edit"
              />
              <ApplicationInfo
                ref="applicationInfoRef"
                :table-type="4"
                :belongs-id="maintenanceData.id"
                form-type="edit"
              />
              <AgencyInfo
                ref="agencyInfoRef"
                :table-type="4"
                :belongs-id="maintenanceData.id"
                form-type="edit"
              />
            </el-tab-pane>
            <el-tab-pane label="同族专利" name="group">
              <GroupInfo
                ref="groupInfoRef"
                :table-type="4"
                :belongs-id="maintenanceData.id"
                form-type="edit"
              />
            </el-tab-pane>
            <el-tab-pane label="附图" name="imgs">
              <UploadImgs
                v-model="maintenanceData.imgs!"
                height="80px"
                width="80px"
                :disabled="true"
              />
            </el-tab-pane>
            <el-tab-pane label="专利文件" name="attachment">
              <AttachmentInfo
                class="mt-10px"
                ref="attachmentRef"
                :table-type="4"
                :belongs-id="maintenanceData.id"
                form-type="edit"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </vxe-split-pane>
      <vxe-split-pane show-action>
        <div class="p-10px">
          <el-button
            type="primary"
            plain
            class="!w-100%"
            @click="maintenanceDialogRef?.openForm()"
            v-if="edit"
            >选择专利</el-button
          >
          <ContentWrap
            :title="formData.originalType === 0 ? '许可/转让信息' : '无效/诉讼信息'"
            shadow="never"
          >
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              v-loading="loading"
              label-width="150px"
              :disabled="!edit"
              size="small"
            >
              <el-form-item label="类型" prop="recordType">
                <el-select v-model="formData.recordType">
                  <el-option
                    v-for="dict in getIntDictOptions('patent_operation_record_type')"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                    v-show="
                      formData.originalType === 0
                        ? [0, 1].includes(dict.value)
                        : [2, 3].includes(dict.value)
                    "
                  />
                </el-select>
              </el-form-item>
              <template v-if="typeOpertion === 0">
                <el-form-item label="许可人" prop="licensorPeople">
                  <el-input v-model="formData.licensorPeople" placeholder="请输入许可人或转让人" />
                </el-form-item>
                <el-form-item label="许可时间" prop="licensorDate">
                  <el-date-picker
                    v-model="formData.licensorDate"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择许可或转让时间"
                    class="!w-100%"
                  />
                </el-form-item>
                <el-form-item label="费用" prop="costs">
                  <el-input v-model="formData.costs" type="number" />
                </el-form-item>
              </template>
              <template v-if="typeOpertion === 1">
                <el-form-item label="申请人" prop="applicant">
                  <el-input v-model="formData.applicant" />
                </el-form-item>
                <el-form-item label="被申请人" prop="beApplicant">
                  <el-input v-model="formData.beApplicant" />
                </el-form-item>
                <el-form-item label="涉及产品型号" prop="patentModel">
                  <el-input v-model="formData.patentModel" />
                </el-form-item>
                <el-form-item label="诉讼案件来源" prop="litigationSource">
                  <el-input v-model="formData.litigationSource" />
                </el-form-item>
                <el-form-item label="内部侵权分析报告" prop="analysisReport">
                  <el-input type="textarea" v-model="formData.analysisReport" />
                </el-form-item>
                <el-form-item label="诉讼选择或可行性" prop="litigationSelect">
                  <el-input v-model="formData.litigationSelect" />
                </el-form-item>
                <el-form-item label="诉讼或无效案件案号" prop="litigationCase">
                  <el-input v-model="formData.litigationCase" />
                </el-form-item>
                <el-form-item label="委托机构及律师" prop="entrustedLawyer">
                  <el-input v-model="formData.entrustedLawyer" />
                </el-form-item>
                <el-form-item label="开庭时间" prop="trialDate">
                  <el-date-picker
                    v-model="formData.trialDate"
                    value-format="YYYY-MM-DD"
                    placeholder="申请日期"
                    class="!w-100%"
                  />
                </el-form-item>
                <el-form-item label="诉讼或无效结论" prop="litigationConclusion">
                  <el-input
                    v-model="formData.litigationConclusion"
                    :rows="5"
                    type="textarea"
                    placeholder="请输入诉讼或无效结论"
                  />
                </el-form-item>
              </template>
            </el-form>
          </ContentWrap>
          <AttachmentInfo
            ref="attachmentInfoRef1"
            v-if="formData.id"
            :table-type="5"
            :belongs-id="formData.id"
            :form-type="formType"
          />
        </div>
      </vxe-split-pane>
    </vxe-split>
    <MaintenanceDialog ref="maintenanceDialogRef" @success="handleSuccess" />
    <template #footer v-if="formType === 'create'">
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { OperationApi } from '@/api/patent/operation'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import TagsInput from '../components/TagsInput.vue'
import AttachmentInfo from '../components/AttachmentInfo.vue'
import AgencyInfo from '../components/AgencyInfo.vue'
import ApplicationInfo from '../components/ApplicationInfo.vue'
import InventorInfo from '../components/InventorInfo.vue'
import GroupInfo from '../components/GroupInfo.vue'
import MaintenanceDialog from './MaintenanceDialog.vue'
import { MaintenanceApi } from '@/api/patent/maintenance'

const typeOpertion = ref()
const visible = ref(false)
const formData = ref({
  id: undefined,
  operationId: undefined,
  maintenanceId: undefined,
  originalType: typeOpertion.value,
  recordType: undefined,
  licensorPeople: undefined,
  licensorDate: undefined,
  costs: undefined,
  applicant: undefined,
  beApplicant: undefined,
  patentModel: undefined,
  litigationSource: undefined,
  analysisReport: undefined,
  litigationSelect: undefined,
  litigationCase: undefined,
  entrustedLawyer: undefined,
  trialDate: undefined,
  litigationConclusion: undefined
})

const maintenanceData = ref({
  id: undefined,
  originalApplicant: undefined,
  ownership: undefined,
  applicationNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  protectionPoint: undefined,
  imgs: [] as string[],
  models: [] as string[],
  patentType: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  validityPeriod: undefined,
  inventor: undefined,
  actualInventor: undefined,
  acquisitionMethod: undefined,
  legalStatus: 0,
  agency: undefined,
  families: undefined,
  status: 0,
  maintenanceAssessment: undefined
})
const currentTab = ref('function')
const inventorInfoRef = ref()
const applicationInfoRef = ref()
const agencyInfoRef = ref()
const groupInfoRef = ref()
const attachmentRef = ref()
const maintenanceDialogRef = ref()
const attachmentInfoRef1 = ref()
/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'person':
      inventorInfoRef.value?.onList()
      applicationInfoRef.value?.onList()
      agencyInfoRef.value?.onList()
      break
    case 'group':
      groupInfoRef.value?.onList()
      break
    case 'attachment':
      attachmentRef.value?.onList()
      break
  }
}

const formRules = reactive({
  recordType: [{ required: true, message: '请输入选择记录类型', trigger: 'blur' }],
  licensorPeople: [{ required: true, message: '请输入许可人', trigger: 'blur' }],
  licensorDate: [{ required: true, message: '请输入许可时间', trigger: 'blur' }],
  costs: [{ required: true, message: '请输入费用', trigger: 'blur' }],
  litigationCase: [{ required: true, message: '请输入诉讼或无效案件案号', trigger: 'blur' }],
  litigationConclusion: [{ required: true, message: '请输入诉讼或无效结论', trigger: 'blur' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()
const edit = ref(false)

const openForm = async (type?: any, rowId?: number) => {
  typeOpertion.value = type
  visible.value = true
  if (rowId) {
    formType.value = 'edit'
    dialogTitle.value = '修改知识产权运营'
    const data = await OperationApi.getOperation(rowId)
    onMaintenance(data.maintenanceId)
    formData.value = data
    await nextTick()
    attachmentInfoRef1.value?.onList()
    edit.value = false
  } else {
    formType.value = 'create'
    dialogTitle.value = '新增授权许可或转让记录'
    resetForm()
    edit.value = true
  }
}
//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    operationId: undefined,
    maintenanceId: undefined,
    originalType: typeOpertion.value,
    recordType: undefined,
    licensorPeople: undefined,
    licensorDate: undefined,
    costs: undefined,
    applicant: undefined,
    beApplicant: undefined,
    patentModel: undefined,
    litigationSource: undefined,
    analysisReport: undefined,
    litigationSelect: undefined,
    litigationCase: undefined,
    entrustedLawyer: undefined,
    trialDate: undefined,
    litigationConclusion: undefined
  }

  maintenanceData.value = {
    id: undefined,
    originalApplicant: undefined,
    ownership: undefined,
    applicationNo: undefined,
    publicNo: undefined,
    patentName: undefined,
    protectionPoint: undefined,
    imgs: [] as string[],
    models: [] as string[],
    patentType: undefined,
    applicationDate: undefined,
    authorizationDate: undefined,
    validityPeriod: undefined,
    inventor: undefined,
    actualInventor: undefined,
    acquisitionMethod: undefined,
    legalStatus: 0,
    agency: undefined,
    families: undefined,
    status: 0,
    maintenanceAssessment: undefined
  }
}
const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    await OperationApi.createOperation(formData.value)
    message.success(dialogTitle.value + '成功')
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

const onModify = async () => {
  if (!edit.value) {
    edit.value = true
    return
  } else {
    await formRef.value.validate()
    await OperationApi.updateOperation(formData.value)
    message.success(dialogTitle.value + '成功')
    emit('success')
  }
}

const handleDelete = async () => {}

//选择某一条知识产权后
const handleSuccess = async (rows: any) => {
  formData.value.maintenanceId = rows.id
  onMaintenance(rows.id)
}

const onMaintenance = async (maintenanceId: number) => {
  const data = await MaintenanceApi.getMaintenance(maintenanceId)
  // 时间字段转换：时间戳 -> YYYY-MM-DD 字符串
  maintenanceData.value = data
  onTableChange()
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header.is-top) {
  background-color: #f1f1f1;
}
</style>
