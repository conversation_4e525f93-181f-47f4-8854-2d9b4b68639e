<template>
  <el-tabs
    v-model="currentTab"
    class="drawer-tabs position-sticky top-0 z-10 bg-#fff"
    @tab-change="onTabChange"
  >
    <el-tab-pane name="technical" label="预审记录" />
    <el-tab-pane name="target" label="输出物" />
  </el-tabs>
  <el-form label-width="100px" ref="formRef" class="custom-form" v-if="currentTab === 'technical'">
    <el-form-item label="所属项目">
      <el-input v-model="formData.basicsName" :disabled="true" />
    </el-form-item>
    <el-row>
      <el-col :span="8">
        <el-form-item label="等级">
          <el-input v-model="formData.basicsLevel" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="类型">
          <el-input v-model="formData.basicsMold" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="平台">
          <el-input v-model="formData.basicsPlatform" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="问题图片">
      <UploadImgs v-model="formData.imgIds!" height="60px" width="60px" :disabled="true" />
    </el-form-item>
    <el-form-item label="针对的要素" prop="targetFactor">
      <div
        class="w-full h-36px leading-[36px] text-1rem p-2px rounded-5px cursor-pointer"
        :style="{
          backgroundColor: 'var(--el-disabled-bg-color)'
        }"
      >
        {{ factorData.label }}
      </div>
    </el-form-item>
    <el-form-item label="预计完成" prop="endDate">
      <el-date-picker
        v-model="formData.endDate"
        type="date"
        placeholder="选择日期"
        :disabled="true"
      />
    </el-form-item>
    <el-form-item label="问题描述" prop="description">
      <el-input v-model="formData.description" type="textarea" :rows="5" :disabled="true" />
    </el-form-item>
    <el-form-item label="建议">
      <el-input v-model="formData.suggestion" type="textarea" :rows="5" :disabled="true" />
    </el-form-item>
    <el-form-item label="负责人" prop="director">
      <user-avatar-list
        v-model="formData.director!"
        :user-list="userList"
        :size="28"
        :limit="10"
        :add="false"
      />
    </el-form-item>
    <el-form-item label="状态">
      <DictTag type="project_activities_status" :value="formData.status!" />
    </el-form-item>
    <el-form-item label="进度">
      <el-progress
        :percentage="formData.progress"
        class="w-100% no-radius"
        :text-inside="true"
        :stroke-width="20"
        status="success"
      />
    </el-form-item>
    <Comment ref="commentRef" category="technical" :limit="5" bgColor="#fff" :disabled="false" />
  </el-form>
  <template v-else-if="currentTab === 'target'">
    <vxe-table
      class="w-100%"
      :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
      :cell-style="{ padding: '5px', height: '30px' }"
      show-overflow
      :data="attachmentList"
      align="center"
      border
    >
      <vxe-column title="文件名" field="name" min-width="200" align="left">
        <template #default="{ row }">
          <el-link
            type="primary"
            @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
          >
            {{ row.name }}
          </el-link>
        </template>
      </vxe-column>
      <vxe-column title="版本" field="currentVersion" width="60" />
      <vxe-column title="审签状态" field="approvalStatus" width="90">
        <template #default="{ row }">
          <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
        </template>
      </vxe-column>
      <vxe-column
        title="审核通过时间"
        field="approvalTime"
        :formatter="dateFormatter3"
        width="120"
      />
    </vxe-table>
  </template>
  <AttachmentPreview ref="attachmentPreviewRef" />
</template>

<script lang="ts" setup>
import { TechnicalFlowApi } from '@/api/bpm/technical'
import { propTypes } from '@/utils/propTypes'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { TechnicalFactorApi, TechnicalFactorVO } from '@/api/project/technicalfactor'
import { AttachmentApi } from '@/api/project/attachment'
import AttachmentPreview from '../../components/AttachmentPreview.vue'
import { dateFormatter3 } from '@/utils/formatTime'

const props = defineProps({
  processInstanceId: propTypes.string.def('')
})
const formData = ref<any>({})
const userList = ref<UserVO[]>([])
const currentTab = ref('technical')
const attachmentList = ref<any[]>([])
const attachmentPreviewRef = ref()
const commentRef = ref()

const onTabChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'target':
      onListAttachment()
  }
}

const onListComment = async() =>{
  commentRef.value?.listEvent(formData.value.technicalId)
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'technical',
    dynamicId: formData.value.technicalId
  })
  attachmentList.value = res
}
/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

const factorData = ref({
  label: '',
  id: 0
})
const factorList = ref<TechnicalFactorVO[]>([])

watch(
  () => props.processInstanceId,
  async () => {
    if (!props.processInstanceId) return
    formData.value = await TechnicalFlowApi.getTechnicalFlow(props.processInstanceId)
    onListComment()
    const res = await TechnicalFactorApi.getTechnicalFactorListByGroup(-1)
    factorList.value = res
    factorData.value.id = formData.value.targetFactor
    factorData.value.label =
      factorList.value.find((item) => item.id === formData.value.targetFactor)?.factor || ''
  },
  { immediate: true }
)

onMounted(() => {
  getUserList()
})
</script>
