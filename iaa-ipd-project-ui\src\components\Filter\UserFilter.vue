<template>
  <div v-if="currOption" class="my-filter-input">
    <div>
      <el-radio-group
        v-model="currOption.data.type"
        @change="
          () => {
            currOption!.data.userList = []
            changeOptionEvent()
          }
        "
      >
        <el-radio label="只看我的" value="my" />
        <el-radio label="指定人员" value="other" />
      </el-radio-group>
    </div>
    <div v-if="currOption.data.type === 'other'">
      <div class="search">
        <el-input size="small" v-model="nickname" @input="filterUserEvent" />
      </div>
      <div class="p-5px">
        <el-checkbox v-model="isCheckedAll" @change="checkedAllEvent">全选</el-checkbox>
      </div>
      <div class="h-150px overflow-auto p-l-5px p-r-5px">
        <el-checkbox-group v-model="currOption.data.userList" @change="changeOptionEvent">
          <el-checkbox
            v-for="item in cloneUserList"
            :key="item.id"
            :value="item.id"
            :label="item.nickname"
          />
        </el-checkbox-group>
      </div>
    </div>
    <div class="my-fc-footer">
      <el-button type="primary" plain @click="confirmEvent" size="small">筛选</el-button>
      <el-button @click="resetEvent" size="small">重置</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'
import type { VxeTableDefines } from 'vxe-table'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { cloneDeep } from 'lodash-es'

const nickname = ref('')
const userList = ref<UserVO[]>([])
const cloneUserList = ref<UserVO[]>([])
const props = defineProps({
  renderParams: propTypes.any.def({})
})

const isCheckedAll = ref(false)

const currOption = ref<VxeTableDefines.FilterOption>()

const currField = computed(() => {
  const { column } = props.renderParams || {}
  return column ? column.field : ''
})

const onListUser = async () => {
  const res = await getSimpleUserList()
  userList.value = res?.filter((item: any) => item.id != 1)
  cloneUserList.value = cloneDeep(userList.value)
}

const load = () => {
  const { renderParams } = props
  if (renderParams) {
    const { column } = renderParams
    const option = column.filters[0]
    currOption.value = option
    onListUser()
  }
}

const changeOptionEvent = () => {
  const { renderParams } = props
  const option = currOption.value
  if (renderParams && option) {
    const { $table } = renderParams
    const checked = !!option.data.userList
    $table.updateFilterOptionStatus(option, checked)
  }
}

const checkedAllEvent = () => {
  const option = currOption.value
  if (option) {
    if (isCheckedAll.value) {
      option.data.userList = userList.value.map((item) => item.id)
    } else {
      option.data.userList = []
    }
    changeOptionEvent()
  }
}

const filterUserEvent = () => {
  cloneUserList.value = userList.value.filter((item) => {
    return item.nickname.includes(nickname.value)
  })
}

const confirmEvent = ($event) => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.confirmFilterEvent($event)
  }
}

const resetEvent = () => {
  const { renderParams } = props
  if (renderParams) {
    const { $table } = renderParams
    $table.clearFilter()
  }
}

watch(currField, () => {
  load()
})

load()
</script>

<style scoped>
.my-filter-input {
  padding: 10px;
}

.my-fc-footer {
  text-align: center;
  margin-top: 8px;
}

:deep(.el-checkbox) {
  display: block;
}
</style>
