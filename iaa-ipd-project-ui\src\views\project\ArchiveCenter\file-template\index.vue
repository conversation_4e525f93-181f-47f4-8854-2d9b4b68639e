<template>
  <el-row :gutter="5">
    <el-col :span="4" :xs="24">
      <FileTree
        :category-list="categoryList"
        @node-click="onOpenSpecifiedLevelFolder"
      />
    </el-col>
    <el-col :span="20" :xs="24">
      <el-card shadow="never">
        <template #header>
          <div class="flex justify-between content-center">
            <div class="flex flex-wrap content-center items-center folder-path">
              <XButton pre-icon="ep:arrow-left" @click="onBackedFolder" />
              <el-breadcrumb>
                <el-breadcrumb-item @click="onOpenSpecifiedLevelFolder(0)"
                  >模板池</el-breadcrumb-item
                >
                <el-breadcrumb-item
                  v-for="(path, pathIdx) in checkedPath"
                  :key="pathIdx"
                  @click="onOpenSpecifiedLevelFolder(path.value)"
                >
                  {{ path.label }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="w-260px">
              <el-input class="!w-200px" placeholder="请输入文件名称" v-model="queryParams.name" />
              <XButton type="primary" plain pre-icon="ep:search" @click="getCurrentLevelFileList" />
            </div>
          </div>
        </template>
        <div class="h-full">
          <div class="h-40px leading-40px opertion">
            <el-button size="small" v-if="openFolder" @click="fileUploadRef?.openDialog()">
              <img src="@/assets/opertion/upload.png" class="mr-10px w-20px h-20px" />
              上传文件
            </el-button>
            <el-button
              size="small"
              v-if="activeFile && !activeFile?.hasFolder"
              @click="handleRename"
            >
              <img src="@/assets/opertion/support.png" class="mr-10px w-20px h-20px" />
              重命名
            </el-button>
            <el-button size="small" v-if="activeFile && !activeFile?.hasFolder" @click="shareFile">
              <img src="@/assets/opertion/share.png" class="mr-10px w-20px h-20px" />
              分享
            </el-button>
            <el-button
              size="small"
              v-if="activeFile && !activeFile?.hasFolder"
              @click="handleDownloadFile"
            >
              <img src="@/assets/opertion/download.png" class="mr-10px w-20px h-20px" />
              下载
            </el-button>
            <el-button
              size="small"
              v-if="activeFile && !activeFile?.hasFolder"
              @click="handleDelete"
            >
              <img src="@/assets/opertion/delete.png" class="mr-10px w-20px h-20px" />
              删除
            </el-button>
          </div>
          <div class="flex">
            <draggable
              v-loading="loading"
              v-contextmenu="{ name: 'context-menu-1', id: '456' }"
              @click="activeFile = openFolder"
              @contextmenu="activeFile = openFolder"
              :list="currentLevelFileList"
              class="file-list"
              :delay="10"
              handle=".draggable"
              forceFallback
              scroll
              :sort="false"
              @end="handleFileMove"
              item-key="uri"
            >
              <template #item="{ element }">
                <div
                  :class="{
                    'file-item': true,
                    'is-active':
                      activeFile?.id == element.id && activeFile?.hasFolder == element.hasFolder,
                    draggable: !element.hasFolder
                  }"
                  @click.stop="handleSelected(element)"
                  @contextmenu.stop="handleSelected(element)"
                  @dblclick.stop="openFileOrFolder(element)"
                  v-contextmenu="{ name: 'context-menu-1', id: '123' }"
                  :key="`${element.hasFolder}${element.id}`"
                >
                  <img :src="getImagePath(element.uri, element.hasFolder)" class="w-77px h-77px" />
                  <div class="file-name">
                    {{ element.name }}
                  </div>
                </div>
              </template>
            </draggable>
            <div class="w-300px file-info">
              <FileAttribute
                direction="bottom"
                :file="activeFile"
                :versions="templateVersions"
                @open="openFileOrFolder"
                @upload:file="fileUploadRef?.openDialog(1, activeFile?.id)"
              />
            </div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
  <context-menu name="context-menu-1">
    <context-menu-item :disabled="!activeFile" divider @click="openFileOrFolder(activeFile!)">
      <Icon icon="ep:arrow-right" />
      打开
    </context-menu-item>
    <context-menu-item :disabled="!activeFile" @click="handleDownloadFile">
      <img src="@/assets/opertion/download.png" class="w-20px h-20px" />
      下载
    </context-menu-item>
    <context-menu-item :disabled="!activeFile || activeFile?.hasFolder" @click="handleRename">
      <img src="@/assets/opertion/support.png" class="w-20px h-20px" />
      重命名
    </context-menu-item>
    <context-menu-item :disabled="!activeFile || activeFile?.hasFolder" @click="shareFile">
      <img src="@/assets/opertion/share.png" class="w-20px h-20px" />
      分享
    </context-menu-item>
    <context-menu-item
      :disabled="!activeFile || activeFile?.hasFolder"
      divider
      @click="handleDelete"
    >
      <img src="@/assets/opertion/delete.png" class="w-20px h-20px" />
      删除
    </context-menu-item>
    <context-menu-item :disabled="!activeFile" @click="attributeDialog.visible = true">
      <Icon icon="ep:info-filled" />
      属性
    </context-menu-item>
  </context-menu>
  <FileUpload
    ref="fileUploadRef"
    v-model:openFolder="openFolder"
    @success="getCurrentLevelFileList"
  />
  <Dialog title="文件属性" v-model="attributeDialog.visible">
    <FileAttribute direction="bottom" :file="activeFile" />
  </Dialog>

  <OfficeEditor ref="officeEditorRef" />

  <FileRename ref="fileRenameRef" @success="getCurrentLevelFileList" />

  <Dialog title="文件分享(请复制链接后转发)" v-model="fileShareDialog.visible">
    <div id="fileShareUrl"></div>
    <template #footer>
      <el-button type="primary" @click="copyInnerHTML">一键复制</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { FileTemplateApi, FileTemplateVO } from '@/api/project/file/template'
import { downloadFile } from '@/api/infra/file'
import { findPath } from '@/utils/tree'
import FileAttribute from './components/FileAttribute.vue'
import FileUpload from './components/FileUpload.vue'
import FileTree from './components/FileTree.vue'
import FileRename from './components/FileRename.vue'
import { getImagePath } from '@/utils/icon'
import draggable from 'vuedraggable'
import { useCache } from '@/hooks/web/useCache'
import { FileTemplateCategoryApi } from '@/api/project/file/template-category'
import {  handleTree } from '@/utils/tree'

interface Path {
  label: string
  value: number
}

const { wsCache } = useCache()
const message = useMessage()
const fileUploadRef = ref()
const fileRenameRef = ref()
const officeEditorRef = ref()
const currentLevelFileList = ref<FileTemplateVO[]>([])
const activeFile = ref<FileTemplateVO | any>(undefined as any)
const openFolder = ref<FileTemplateVO | any>({} as any)
const checkedPath = ref<Path[]>([])
const categoryList = ref<FileTemplateVO[]>([])
const templateVersions = ref<FileTemplateVO[]>([])
const loading = ref(false)

const attributeDialog = ref({
  visible: false
})

const fileShareDialog = ref({
  visible: false
})

//#region 文件夹相关操作及初始化数据

const queryParams = ref({
  name: '',
  categoryId: 0
})

/** 获取当前层级的文件列表 */
const getCurrentLevelFileList = async () => {
  loading.value = false
  try {
    const res = await FileTemplateApi.getFileTemplatePage(queryParams.value)
    currentLevelFileList.value = res
    queryParams.value.name = ''
  } finally {
    loading.value = false
  }
}
/** 打开文件夹 */
const onOpenFolder = async (row: FileTemplateVO) => {
  queryParams.value.categoryId = row.id
  checkedPath.value.push({ label: row.name, value: row.id })
  openFolder.value = row
  getCurrentLevelFileList()
}
/** 打开指定层级文件夹 */
const onOpenSpecifiedLevelFolder = async (categoryId: number) => {
  if (categoryId === 0) {
    checkedPath.value = []
    openFolder.value = undefined
  } else {
    checkedPath.value = []
    const tempPath = findPath(categoryList.value, (node: any) => node.id === categoryId)
    tempPath?.forEach((item) => {
      checkedPath.value.push({ label: item.name, value: item.id })
    })
    openFolder.value = tempPath?.[tempPath.length - 1]
  }
  queryParams.value.categoryId = categoryId
  await getCurrentLevelFileList()
}
/** 返回上一级文件夹 */
const onBackedFolder = () => {
  if (checkedPath.value && checkedPath.value.length > 1) {
    onOpenSpecifiedLevelFolder(checkedPath.value[checkedPath.value.length - 2].value)
  } else if (checkedPath.value && checkedPath.value.length > 0) {
    onOpenSpecifiedLevelFolder(0)
  }
}
/** 打开文件夹或者打开文件 */
const openFileOrFolder = async (row: FileTemplateVO) => {
  if (!row) return
  if (row.hasFolder) {
    onOpenFolder(row)
  } else {
    officeEditorRef.value.open(row.infraFileId, row.name)
  }
}
// 重命名
const handleRename = async () => {
  if (!activeFile.value) return
  fileRenameRef.value.openDialog(activeFile.value)
}

const copyInnerHTML = () => {
  const element = document.getElementById('fileShareUrl')
  if (!element) return
  const content = element.textContent || element.innerHTML // 优先读取文本内容
  if (content) {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        message.success('访问地址已复制到剪贴板')
      })
      .catch(() => {
        message.error('复制失败，请手动复制')
      })
  }
}

const shareFile = async () => {
  if (!activeFile.value) return
  fileShareDialog.value.visible = true
  await nextTick()
  const url = `${window.location.origin}/archive-center/file-template?type=view&categoryId=${activeFile.value.categoryId}&id=${activeFile.value.id}`
  const element = document.getElementById('fileShareUrl')!
  element.textContent = url // 直接设置文本内容
  element.style.wordBreak = 'break-all' // 保持换行效果（可选）
}

/** 下载模板 */
const handleDownloadFile = async () => {
  if (!activeFile.value) return
  try {
    await downloadFile(activeFile.value.uri, activeFile.value.name)
  } catch (error) {
    message.alertError('下载文件失败')
  }
}
/** 删除文件 */
const handleDelete = async () => {
  if (!activeFile.value) return
  await message.confirm(`确定删除模板:${activeFile.value?.name}?,删除后将不可恢复。`)
  await FileTemplateApi.deleteFileTemplate(activeFile.value.id)
  message.success('删除成功')
  getCurrentLevelFileList()
}
/** 选中文件 */
const handleSelected = async (row: FileTemplateVO) => {
  activeFile.value = row
  if (row.hasFolder) return
  templateVersions.value = await FileTemplateApi.getFileTemplateAllVersion(row.id)
}

const handleFileMove = (event: any) => {
  // console.log('结束dragEnd：', event)
  // const { oldIndex, newIndex } = event
  // console.log('拖拽开始时的 oldIndex:', oldIndex, 'newIndex:', newIndex)
}

//#endregion
const route = useRoute()

const handleRouteQuery = async () => {
  const query = route.query
  switch (query?.type) {
    case 'view':
      queryParams.value.categoryId = Number(query?.categoryId)
      await onOpenSpecifiedLevelFolder(Number(query?.categoryId))
      const row = currentLevelFileList.value.find(
        (item) => item.id === Number(query?.id) && !item.hasFolder
      )
      const viewFileId = wsCache.get('before-preview-file-id')
      if (!viewFileId||viewFileId!==Number(query?.id)) {
        openFileOrFolder(row!)
      }
      wsCache.add('before-preview-file-id', Number(query?.id), { exp: 3600 })
      break
  }
}

/** 获取分类 */
const getCategoryList = async () => {
  const res = await FileTemplateCategoryApi.getFileTemplateCategoryPage({
    pageNo: 1,
    pageSize: -1
  })
  categoryList.value = []
  categoryList.value.push(...handleTree(res))
}

onMounted(() => {
  getCategoryList()
  const query = route.query
  if (query.type) {
    handleRouteQuery()
  } else {
    getCurrentLevelFileList()
  }
})
</script>

<style lang="scss" scoped>
.body-container {
  height: calc(100vh - 200px);
  overflow: auto;
}

:deep(.el-card__body) {
  padding: 0px;
}

.file-info,
.file-list {
  height: calc(100vh - 240px) !important;
}

.file-info {
  background-color: #fdfdfd;
}

.file-list {
  padding: 20px;
  padding-bottom: 3px;
  width: calc(100% - 300px);
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: hidden;
  align-content: flex-start;
  justify-content: flex-start;
  flex-direction: row;
}

.file-item {
  width: 81px;
  height: 146px;
  text-align: center;
  margin-left: 5px;

  .file-name {
    font-size: 12px;
    color: #999999;
  }
}

.file-item:hover,
.file-item.is-active {
  background-color: var(--el-color-primary-light-9);
  border-radius: 2px;
}

.opertion {
  border-bottom: 1px #f1f1f1 solid;
  padding: 0 20px;
}

.folder-path {
  width: 600px;
  border: 0.3px solid #f1f1f1;
  background-color: #fdfdfd;
  border-radius: 3px;
}

:deep(.el-breadcrumb__item) {
  cursor: pointer;
}
</style>
