<template>
  <div class="text-2rem text-center border-b-blueGray border-1px">项目变更申请单</div>
  <el-form
    class="custom-border-form"
    label-width="130px"
    :model="formData"
    :rules="formRules"
    ref="formRef"
    :disabled="formType == 'view'"
  >
    <el-row>
      <el-col :span="24">
        <div class="text-1.2rem text-center bg-[var(--el-color-warning-light-9)]">变更基本信息</div>
      </el-col>
      <el-col :span="12">
        <el-form-item label="申请人">
          <span class="pl-10px" v-if="formType === 'create'">{{ getUser.nickname }}</span>
          <span class="pl-10px" v-if="formType === 'view'">
            {{ userList.find((item) => item.id == formData.userId)?.nickname }}
          </span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="申请日期">
          <span class="pl-10px" v-if="formType === 'create'">
            {{ moment().format('YYYY-MM-DD') }}
          </span>
          <span class="pl-10px" v-if="formType === 'view'">
            {{ formatToDate(formData.applicationDate) }}
          </span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="变更项目" prop="basicsId">
          <el-select
            v-model="formData.basicsId"
            filterable
            @change="onBasicsChange"
            :disabled="!editBasics"
          >
            <el-option
              v-for="basics in basicsList"
              :key="basics.id"
              :label="`${basics.name}`"
              :value="basics.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="项目所处节点">
          <span class="pl-10px">
            {{
              formData.basicsId
                ? basicsList.find((item) => item.id === formData.basicsId)?.currentCruxName ||
                  '项目所有已分解关键节点已完成'
                : ''
            }}
          </span>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="销售代表">
          <UserAvatarList
            v-model="formData.salesUserIds!"
            :user-list="userList"
            :add="false"
            :size="28"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品代表">
          <UserAvatarList
            v-model="formData.productUserIds!"
            :user-list="userList"
            :add="false"
            :size="28"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="变更类型" prop="changeType">
          <el-radio-group v-model="formData.changeType">
            <el-radio label="客户需求变更" :value="1" />
            <el-radio label="产品需求变更" :value="2" />
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="涉及成本">
          <el-radio-group v-model="formData.hasCost" :disabled="true">
            <el-radio label="是" :value="true" />
            <el-radio label="否" :value="false" />
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="变更原因" prop="reason">
          <el-input type="textarea" :rows="6" v-model="formData.reason" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="附件">
          <div v-for="file in attachments" :key="file.id" class="w-100% flex justify-between">
            <el-link type="primary" @click="officeEditorRef?.open(file.id, file.name)">{{
              file.name
            }}</el-link>
            <el-button
              type="danger"
              size="small"
              plain
              v-if="formType === 'create'"
              @click="deleteAttachment(file.id)"
              >X</el-button
            >
          </div>
          <el-button
            size="small"
            @click="fileUploadRef?.openDialog()"
            class="!w-100%"
            v-if="formType === 'create'"
          >
            <img src="@/assets/opertion/upload.png" class="mr-10px w-20px h-20px" />
            上传文件
          </el-button>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <div class="text-1.2rem text-center bg-[var(--el-color-warning-light-9)]">变更内容</div>
      </el-col>
      <el-col :span="24">
        <el-form-item label="变更对象">
          <vxe-table
            class="!w-100%"
            align="center"
            :header-cell-config="{ height: 30 }"
            :cell-config="{ height: 30 }"
            :data="formRecord"
            border
          >
            <vxe-column title="变更类型">
              <template #default="{ row }">
                <el-select v-model="row.changeType" size="small" @change="onChangeType">
                  <el-option
                    v-for="type in changeType"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
              </template>
            </vxe-column>
            <vxe-column title="变更内容">
              <template #default="{ row }">
                <el-select
                  v-if="['10', '11', '12', '13', '14'].includes(row.changeType)"
                  size="small"
                  v-model="row.changeContent"
                >
                  <el-option
                    v-for="activities in basicsList
                      .find((item) => item.id === formData.basicsId)
                      ?.cruxActivities?.filter((item) => item.progress != 100)"
                    :key="activities.id"
                    :label="activities.name"
                    :value="String(activities.id)"
                  />
                </el-select>
                <div
                  v-else-if="
                    !['10', '11', '12', '13', '14'].includes(row.changeType) &&
                    formType === 'create'
                  "
                >
                  无需填写
                </div>
              </template>
            </vxe-column>
            <vxe-column title="变更前">
              <template #default="{ row }">
                <template v-if="formType === 'create'">
                  <span v-if="['10', '11'].includes(row.changeType)">
                    {{
                      basicsList
                        .find((item) => item.id === formData.basicsId)
                        ?.cruxActivities?.filter((item) => item.progress != 100)
                        .find((item) => String(item.id) == row.changeContent)?.[
                        row.changeType === '10' ? 'startDate' : 'endDate'
                      ]
                    }}
                  </span>
                  <span v-else-if="['12', '13'].includes(row.changeType)">
                    {{
                      userList
                        .filter((item) => currentDirectorOrCoordinate(row)?.includes(item.id))
                        ?.map((item) => item.nickname)
                        ?.join(',')
                    }}
                  </span>
                  <el-input
                    size="small"
                    v-else-if="'15' === row.changeType"
                    type="number"
                    v-model="row.beforeValue"
                  />
                  <span v-else-if="row.changeType === '18'">
                    {{ basicsList.find((item) => item.id === formData.basicsId)?.releaseDate }}
                  </span>
                </template>
                <template v-else>
                  <span v-if="['10', '11', '18', '15'].includes(row.changeType)">
                    {{ row.beforeValue }}
                  </span>
                  <span v-else-if="['12', '13'].includes(row.changeType)">
                    {{
                      userList
                        .filter((item) => row.beforeValue.includes(item.id))
                        ?.map((item) => item.nickname)
                        ?.join(',')
                    }}
                  </span>
                </template>
              </template>
            </vxe-column>
            <vxe-column title="变更后">
              <template #default="{ row }">
                <el-date-picker
                  v-if="['10', '11'].includes(row.changeType)"
                  type="date"
                  v-model="row.afterValue"
                  value-format="YYYY-MM-DD"
                  size="small"
                  :disabled-date="disabledDate"
                  class="!w-100%"
                />
                <UserAvatarList
                  v-else-if="['12', '13'].includes(row.changeType)"
                  v-model="row.afterValue"
                  :user-list="userList"
                  :size="22"
                  :add="formType === 'create'"
                  :visiable-user-list="[
                    ...supportlibraryList,
                    ...basicsList.find((item) => item.id === formData.basicsId)?.optionalUsers
                  ]"
                />
                <el-input
                  size="small"
                  v-else-if="'15' === row.changeType"
                  type="number"
                  v-model="row.afterValue"
                />
                <el-date-picker
                  v-else-if="row.changeType === '18'"
                  v-model="row.afterValue"
                  value-format="YYYY-MM-DD"
                  size="small"
                  :disabled-date="disabledDate"
                  class="!w-100%"
                />
              </template>
            </vxe-column>
            <vxe-column title="操作" v-if="formType === 'create'">
              <template #default="{ rowIndex }">
                <el-button type="danger" link size="small" @click="deleteRow(rowIndex)">
                  删除
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
          <el-button
            type="primary"
            size="small"
            class="!w-full"
            plain
            v-if="formType === 'create'"
            @click="onAddRecord"
          >
            添加
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <FileUpload ref="fileUploadRef" @success="setAttachments" />
  <OfficeEditor ref="officeEditorRef" :hasDialog="true" :download="true" />
</template>

<script lang="ts" setup>
import { getSimpleUserList } from '@/api/system/user'
import { useUserStore } from '@/store/modules/user'
import { BasicsApi } from '@/api/project/basics'
import moment from 'moment'
import { dateUtil, formatToDate } from '@/utils/dateUtil'
import { cloneDeep } from 'lodash-es'
import { ChangeApplicationApi } from '@/api/bpm/change-application'
import { SupportLibraryApi } from '@/api/project/supportlibrary'
import FileUpload from '@/views/Patent/components/MainFileUpload.vue'
import { deleteFile, getByIds } from '@/api/infra/file'
import { propTypes } from '@/utils/propTypes'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}
const fileUploadRef = ref()
const officeEditorRef = ref()
const setAttachments = async (fileItemList: any[]) => {
  for (const item of fileItemList) {
    attachments.value.push({
      name: item.attachmentName,
      url: item.attachmentUrl,
      id: item.infraFileId
    })
    formData.value.attachments.push(item.infraFileId)
  }
}

const attachments = ref<any[]>([])

const props = defineProps({
  basicsId: propTypes.number
})

const deleteAttachment = async (id: number) => {
  attachments.value = attachments.value.filter((item) => item.id != id)
  formData.value.attachments = formData.value.attachments.filter((id) => id !== id)
  await deleteFile(id)
  message.success('删除成功')
}

const changeType = reactive([
  { label: '修改里程碑节点开始时间', value: '10' },
  { label: '修改里程碑节点结束时间', value: '11' },
  { label: '修改里程碑节点负责人', value: '12' },
  { label: '修改里程碑节点执行人', value: '13' },
  { label: '删除里程碑节点', value: '14' },
  { label: '修改目标成本', value: '15' },
  { label: '项目暂停', value: '16' },
  //{ label: '项目终止', value: '17' },
  { label: '修改项目交付时间', value: '18' }
])

const basicsList = ref<any[]>([])
const userList = ref<any[]>([])
const formType = ref('create')
const { getUser } = useUserStore()
const formData = ref({
  id: undefined,
  userId: undefined,
  applicationDate: undefined,
  basicsId: undefined,
  salesUserIds: undefined,
  productUserIds: undefined,
  changeType: undefined,
  hasCost: undefined,
  reason: undefined,
  attachments: [] as number[]
})
const formRules = reactive({
  basicsId: [{ required: true, message: '请选择项目', trigger: 'blur' }],
  changeType: [{ required: true, message: '请选择变更类型', trigger: 'blur' }],
  reason: [
    { required: true, message: '请填写变更原因', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value && value.trim().length < 15) {
          callback(new Error('变更原因不得少于15个字'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})
const formRecord = ref<any[]>([])
const message = useMessage()
const formRef = ref()

const route = useRoute()
const editBasics = ref(true)

const getUserList = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}

const getBasicsList = async () => {
  const res = await BasicsApi.getBasicsChangeApplicationList()
  basicsList.value = res
}

const onBasicsChange = () => {
  const basics = basicsList.value.find((item) => item.id == formData.value.basicsId)
  formData.value.salesUserIds = basics?.salesUserIds
  formData.value.productUserIds = basics?.productUserIds
}

const onAddRecord = () => {
  if (!formData.value.basicsId) {
    message.error('请选择项目')
    return
  }
  formRecord.value.push({
    changeType: undefined,
    changeContent: undefined,
    beforeValue: undefined,
    afterValue: undefined
  })
}

const disabledDate = (time: Date) => {
  return dateUtil().subtract(1, 'days').isAfter(time)
}

// 新增计算属性方法
const currentDirectorOrCoordinate = (row: any) => {
  const activities = basicsList.value
    .find((item) => item.id === formData.value.basicsId)
    ?.cruxActivities?.filter((item) => item.progress != 100)
    .find((item) => String(item.id) == row.changeContent)

  return activities ? activities[row.changeType === '12' ? 'director' : 'coordinate'] : []
}

const deleteRow = (rowIndex: number) => {
  formRecord.value.splice(rowIndex, 1)
}

const onChangeType = () => {
  formData.value.hasCost = formRecord.value.some((item) => item.changeType == '15') as any
}

const initFlowData = async (processInstanceId: string) => {
  const res = await ChangeApplicationApi.getFlow(processInstanceId)
  formData.value = res
  formRecord.value = res.record
  if (formData.value.attachments && formData.value.attachments.length > 0) {
    attachments.value = await getByIds(formData.value.attachments)
  }
}

const getData = async () => {
  await unref(formRef).validate()
  if (formRecord.value.length === 0) {
    message.error('请添加变更内容')
    throw Error()
  }
  formData.value.userId = getUser.id as any
  formData.value.applicationDate = moment().format('YYYY-MM-DD') as any

  const tempRecord = cloneDeep(formRecord.value)

  tempRecord.forEach((record) => {
    if (['10', '11', '12', '13', '15', '18'].includes(record.changeType) && !record.afterValue) {
      message.error(
        changeType.find((item) => item.value === record.changeType)?.label +
          '行未填写修改后内容，请填写'
      )
      throw Error()
    }
    if (['10', '11'].includes(record.changeType)) {
      record.beforeValue = basicsList.value
        .find((item) => item.id === formData.value.basicsId)
        ?.cruxActivities?.filter((item) => item.progress != 100)
        .find((item) => String(item.id) == record.changeContent)?.[
        record.changeType === '10' ? 'startDate' : 'endDate'
      ]
    } else if (['12', '13'].includes(record.changeType)) {
      record.beforeValue = basicsList.value
        .find((item) => item.id === formData.value.basicsId)
        ?.cruxActivities?.filter((item) => item.progress != 100)
        .find((item) => String(item.id) == record.changeContent)?.[
        record.changeType === '12' ? 'director' : 'coordinate'
      ]
      record.beforeValue = JSON.stringify(record.beforeValue)
      record.afterValue = JSON.stringify(record.afterValue)
    } else if (['18'].includes(record.changeType)) {
      record.beforeValue = basicsList.value.find(
        (item) => item.id === formData.value.basicsId
      )?.releaseDate
    }
  })

  return { ...formData.value, record: tempRecord }
}

const refresh = (data?: any) => {
  if (!data) {
    formData.value = {
      id: undefined,
      userId: undefined,
      applicationDate: undefined,
      basicsId: undefined,
      salesUserIds: undefined,
      productUserIds: undefined,
      changeType: undefined,
      hasCost: undefined,
      reason: undefined,
      attachments: [] as number[]
    }
    formRecord.value = []
  } else {
    formData.value = data
    formRecord.value = data.record
  }
}

defineExpose({
  getData,
  refresh
})

watch(
  () => props.basicsId,
  async() => {
    if (props.basicsId) {
      formData.value.basicsId = props.basicsId as any
      editBasics.value = false
      await getBasicsList()
      onBasicsChange()
    }
  },
  { immediate: true,deep:true }
)

onMounted(() => {
  getUserList()
  getBasicsList()
  getSupportLibraryList()
  const id = route.query.id as string
  if (id) {
    formType.value = 'view'
    initFlowData(id)
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content) {
  color: #333 !important;
}

:deep(.el-radio.is-disabled.is-checked) {
  .el-radio__inner {
    border-color: var(--el-color-primary) !important;
    background-color: var(--el-color-primary) !important;
  }
  .el-radio__label {
    color: var(--el-color-primary) !important;
  }
}
</style>
