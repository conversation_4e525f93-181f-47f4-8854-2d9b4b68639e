<template>
  <el-row :gutter="10">
    <el-col :span="3">
      <ContentWrap>
        <el-tree
          :data="getStrDictOptions(DICT_TYPE.PROJECT_TEMPLATE_CATEGORY)"
          :props="{ label: 'label' }"
          node-key="value"
          ref="treeRef"
          highlight-current
          @node-click="nodeChange"
          class="tree-class"
        />
      </ContentWrap>
    </el-col>
    <el-col :span="21">
      <ContentWrap>
        <el-form inline size="small" class="form-no-bottom-margin">
          <el-form-item label="必需输出">
            <el-button type="success" circle />
          </el-form-item>
          <el-form-item label="可选输出">
            <el-button type="info" circle />
          </el-form-item>
          <el-form-item label="无需输出">
            <el-button circle />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              v-if="queryParams.category"
              @click="templateFormRef.open(queryParams.category)"
            >
              新增
            </el-button>
          </el-form-item>
        </el-form>
      </ContentWrap>
      <ContentWrap>
        <div style="height: calc(100vh - 230px)">
          <vxe-table
            ref="tableRef"
            border
            show-overflow
            align="center"
            :header-cell-style="{ padding: 0 }"
            :cell-style="{ padding: 0, height: '30px' }"
            height="100%"
            v-if="queryParams.category"
            :data="templateList"
            :tree-config="{
              transform: true,
              rowField: 'id',
              parentField: 'parentId',
              expandAll: true
            }"
            :header-cell-config="{ height: 24 }"
            :cell-config="{ height: 30 }"
          >
            <vxe-column title="序号" field="orderNo" width="120" tree-node align="left" />
            <vxe-column title="活动标题" field="name" width="200" align="left">
              <template #default="{ row }">
                <el-link type="primary" :underline="false" @click="templateFormRef.openRow(row)">
                  {{ row.name }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column title="活动内容" field="content" width="200" align="left" />
            <vxe-column title="活动描述" field="description" width="200" align="left" />
            <vxe-column title="默认角色" field="defaultRole" width="90">
              <template #default="{ row }">
                <DictTag
                  :type="'project_team_role'"
                  :value="row.defaultRole"
                  v-if="row.defaultRole !== undefined"
                />
              </template>
            </vxe-column>
            <vxe-column title="输出类型" field="targetType" width="90">
              <template #default="{ row }">
                <DictTag
                  :type="'project_activities_target_type'"
                  :value="row.targetType"
                  v-if="row.targetType !== undefined"
                />
              </template>
            </vxe-column>
            <vxe-column title="关键活动" field="isCrux" width="60">
              <template #default="{ row }">
                <DictTag type="infra_boolean_string" :value="row.isCrux" />
              </template>
            </vxe-column>
            <vxe-column title="默认工时" field="defaultManDay" width="60" />
            <vxe-colgroup
              v-for="platform in getStrDictOptions('project_platform')"
              :key="platform.value"
              :title="platform.label"
              :field="`platform_${platform.value}`"
            >
              <vxe-colgroup
                v-for="type in getStrDictOptions('project_type')"
                :key="type.value"
                :title="type.label"
                :field="`platform_${platform.value}_type_${type.value}`"
              >
                <template v-for="level in getStrDictOptions('project_level')" :key="level.value">
                  <vxe-column
                    :title="level.label"
                    :field="`${platform.value}_${type.value}_${level.value}`"
                    v-if="
                      (platform.value === '10' && ['10', '11', '12'].includes(level.value)) ||
                      (platform.value === '20' &&
                        type.value === '10' &&
                        ['12', '13', '14', '15', '16'].includes(level.value)) ||
                      (platform.value === '20' &&
                        type.value === '20' &&
                        ['13', '14', '15', '16', '17'].includes(level.value))
                    "
                    width="50px"
                  >
                    <template #default="{ row }">
                      <el-button
                        circle
                        :type="
                          row.rules[platform.value][type.value][level.value] === 1
                            ? 'info'
                            : row.rules[platform.value]?.[type.value][level.value] === 2
                              ? 'success'
                              : undefined
                        "
                        size="small"
                      />
                    </template>
                  </vxe-column>
                </template>
              </vxe-colgroup>
            </vxe-colgroup>
          </vxe-table>
          <el-empty v-else description="请选择模板分类" />
        </div>
      </ContentWrap>
    </el-col>
  </el-row>
  <TemplateForm
    ref="templateFormRef"
    :template-list="templateList || []"
    :definition-list="definitionList || []"
    :user-group-list="userGroupList || []"
    :file-template-list="fileTemplateList || []"
    @success="handleQuery"
  />
</template>

<script lang="ts" setup>
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import TemplateForm from './TemplateForm.vue'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { cloneDeep } from 'lodash-es'
import * as DefinitionApi from '@/api/bpm/definition'
import { FileTemplateApi } from '@/api/project/file/template'
import * as UserGroupApi from '@/api/bpm/userGroup'

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  category: ''
})

const templateFormRef = ref()
const templateList = ref<any[]>([]) // 模板列表
const definitionList = ref<any[]>([]) // 流程列表
const fileTemplateList = ref<any[]>([]) // 文件模板列表
const userGroupList = ref<any[]>([]) // 流程用户分组列表

/** 切换项目分类 */
const nodeChange = async (node: any) => {
  await nextTick()
  queryParams.category = node.value
  handleQuery()
}

const handleQuery = async () => {
  const data = await ActivitiesTemplateApi.listActivitiesTemplate({
    categoryId: queryParams.category
  })
  templateList.value = cloneDeep(data) // 使用深拷贝避免直接修改原始数据
  await nextTick()
  tableRef.value.setAllTreeExpand(true)
}
/** 加载所有的流程 */
const onListDefinition = async () => {
  const res = await DefinitionApi.getProcessDefinitionList({
    suspensionState: 1,
    category: 'activities_target_bpm'
  })
  definitionList.value = res
}

const tableRef = ref()

// 加载所有的文件模板
const onListFileTemplate = async () => {
  fileTemplateList.value = await FileTemplateApi.getFileTemplatePage({})
}
/** 加载所有的流程用户分组 */
const onListUserGroup = async () => {
  userGroupList.value = await UserGroupApi.getUserGroupSimpleList()
}
onMounted(() => {
  onListDefinition()
  onListFileTemplate()
  onListUserGroup()
})
</script>

<style lang="scss" scoped>
.tree-class {
  height: calc(100vh - 150px);
}
</style>
