import request from '@/config/axios'

export const ProblemFlowApi = {
  /** 创建问题 */
  createProblem: async (data: any) => {
    return await request.post({ url: '/bpm/problem/create', data })
  },
  /** 修改问题 */
  updateProblem: async (data: any) => {
    return await request.post({ url: '/bpm/problem/update', data })
  },
  /** 删除问题 */
  deleteProblem: async (data: any) => {
    return await request.post({ url: '/bpm/problem/delete', data })
  },
  /** 查询问题 */
  getProblem: async (processInstanceId: string) => {
    return await request.get({ url: `/bpm/problem/get/${processInstanceId}` })
  },
  /** 完成问题 */
  completeProblem: async (data: any) => {
    return await request.post({ url: '/bpm/problem/complete', data })
  }
}
