<template>
  <Dialog title="重命名" v-model="visible" :before-close="beforeClose">
    <el-form>
      <el-form-item label="原名">
        {{ tempRow.name }}
      </el-form-item>
      <el-form-item label="新名">
        <el-input v-model="cloneRow.name" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { FileTemplateApi } from '@/api/project/file/template'
import { cloneDeep } from 'lodash-es'

const message = useMessage()

const tempRow = ref<any>()
const visible = ref(false)
const cloneRow = ref<any>()

const openDialog = (row: any) => {
  tempRow.value = row
  cloneRow.value = cloneDeep(row)
  const names = cloneRow.value.name.split('.')
  cloneRow.value.name = names[0]
  visible.value = true
}

const emits = defineEmits(['success'])

const save = async () => {
  const tempRowNames = tempRow.value.name.split('.')
  if (tempRowNames[0] === cloneRow.value.name) {
    message.alertError('文件名未修改，无需保存')
    return
  }
  if (!cloneRow.value.name.endsWith(tempRowNames[tempRowNames.length - 1])) {
    cloneRow.value.name = cloneRow.value.name + '.' + tempRowNames[tempRowNames.length - 1]
  }
  await FileTemplateApi.fileTemplateRename(tempRow.value.id, cloneRow.value.name)
  message.success('保存成功')
  beforeClose()
  emits('success')
}

const beforeClose = (done?: any) => {
  tempRow.value = null
  cloneRow.value = null
  if (done) {
    done()
  } else {
    visible.value = false
  }
}

defineExpose({
  openDialog
})
</script>
