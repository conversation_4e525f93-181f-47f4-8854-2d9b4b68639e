<template>
  <Dialog :title="formType==='create'?'添加查询词':'修改查询词'" v-model="visible">
    <el-form
      label-width="120px"
      :model="formData"
      :rules="formRules"
      ref="formRef"
      v-loading="loading"
    >
      <el-form-item label="查询词" prop="word">
        <el-input v-model="formData.word" placeholder="请输入查询词" type="textarea" :rows="5" />
      </el-form-item>
      <el-form-item label="查询时间范围" prop="startDate">
        <el-date-picker
          v-model="formData.startDate"
          value-format="YYYY-MM-DD"
          placeholder="开始时间"
          class="!w-[calc(50%-15px)]"
          @change="changeDate"
        />
        ——
        <el-date-picker
          v-model="formData.endDate"
          value-format="YYYY-MM-DD"
          placeholder="结束时间"
          class="!w-[calc(50%-15px)]"
          :disabled-date="disabledDate"
          @change="changeDate"
        />
      </el-form-item>
    </el-form>
    <template #footer v-if="formType === 'create'">
      <!-- <el-checkbox class="!mr-10px" v-model="approval">直接发起审批</el-checkbox> -->
      <el-button :loading="loading" @click="submitForm(true)">继续添加</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm()">添加</el-button>
    </template>
    <template #footer v-else>
     
      <el-button type="primary" :loading="loading" @click="submitForm()">保存</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { SearchWordFlowApi } from '@/api/bpm/patent/search-word'
import moment from 'moment'

const visible = ref(false)
const formData = ref({
  id: undefined,
  word: undefined,
  startDate: undefined,
  endDate: undefined
})
const formRules = reactive({
  word: [{ required: true, message: '请输入搜索词', trigger: 'blur' }],
  startDate: [{ required: true, message: '请选择时间范围', trigger: 'change' }]
})

const formType = ref('create')
const formRef = ref()
const loading = ref(false)
// const approval = ref(true)
const message = useMessage()
const emits = defineEmits(['success'])

const openForm = (rows?: any) => {
  visible.value = true
  if (rows) {
    formType.value = 'update'
    formData.value = rows
  } else {
    formType.value = 'create'
    refresh()
  }
}

const submitForm = async (again: boolean = false) => {
  await formRef.value.validate()
  loading.value = true
  try {
    await SearchWordFlowApi.createSearchWord(formData.value)
    message.success('专利查询词确认流程创建成功')
    refresh()
    if (!again) {
      visible.value = false
    }
    emits('success')
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    word: undefined,
    startDate: undefined,
    endDate: undefined
  }
}

const disabledDate = (current: any) => {
  return moment(current).isBefore(moment(formData.value?.startDate))
}

const changeDate = () => {
  const { startDate, endDate } = formData.value

  if (startDate && endDate && moment(endDate).isBefore(moment(startDate))) {
    formData.value.endDate = undefined
    message.error('结束时间不能早于开始时间')
  }
}

defineExpose({
  openForm
})
</script>
