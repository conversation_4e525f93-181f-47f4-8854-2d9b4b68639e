export const getImagePath = (fileName: string, hasFolder: boolean) => {
  let iconName = ''
  if (hasFolder) {
    iconName = 'folder.png'
  } else {
    const fileInfo = fileName.split('.')
    switch (fileInfo?.[fileInfo.length-1]) {
      case 'doc':
      case 'docx':
        iconName = 'doc.png'
        break
      case 'xls':
      case 'xlsx':
        iconName = 'xls.png'
        break
      case 'ppt':
      case 'pptx':
        iconName = 'ppt.png'
        break
      case 'pdf':
        iconName = 'pdf.png'
        break
      case 'folder':
        iconName = 'folder.png'
        break
      case 'zip':
      case 'rar':
      case '7z':
        iconName = 'zip.png'
        break
      default:
        iconName = 'unknown.png'
    }
  }

  return new URL(`/src/assets/file/${iconName}`, import.meta.url).href
}
