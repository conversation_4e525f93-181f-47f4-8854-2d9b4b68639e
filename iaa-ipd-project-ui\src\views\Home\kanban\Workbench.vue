<template>
  <div :class="['h-full workbench-container', isMobile ? 'flex flex-col' : 'flex']">
    <!-- 移动端时个人信息区域移到顶部 -->
    <div v-if="isMobile" class="w-full mb-16px">
      <div class="modern-card user-info-mobile">
        <div class="flex items-center">
          <el-avatar :src="avatar" :size="40" class="mr-12px user-avatar">
            <img src="@/assets/imgs/avatar.gif" alt="" />
          </el-avatar>
          <div>
            <div class="text-sm font-medium text-gray-800">{{ username }}</div>
            <div class="text-xs text-blue-500"> 欢迎回来！ </div>
          </div>
        </div>
      </div>
    </div>

    <div :class="['h-full', isMobile ? 'w-full' : 'w-[calc(100%-20%)] p-l-10px p-r-10px']">
      <div class="modern-card filter-card">
        <el-radio-group size="small" v-model="dataPerson.type" class="filter-radio-group">
          <el-radio-button label="个人数据" value="my" />
          <el-radio-button
            label="他人数据"
            value="other"
            v-hasRole="['r&d_manager', 'super_admin', 'porject_manager']"
          />
        </el-radio-group>
        <el-select
          v-model="dataPerson.personId"
          size="small"
          class="!w-160px ml-10px filter-select"
          v-if="dataPerson.type === 'other'"
          filterable
          @change="onDataPersonChange"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </div>
      <div
:class="[
        'modern-card main-content-card mt-16px',
        isMobile ? 'h-auto mb-16px' : 'h-50% flex'
      ]">
        <div :class="['calendar-list', isMobile ? 'w-full' : 'w-70%']">
          <div :class="['flex items-center', isMobile ? 'flex-col gap-2' : 'justify-between']">
            <CardTitle
              :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的事项`"
            />

            <el-radio-group
              size="small"
              v-model="listType"
              @change="listChange"
              :class="isMobile ? 'flex-wrap' : ''"
            >
              <el-radio :class="isMobile ? 'text-sm' : 'text-1rem'" label="所有" value="all" />
              <el-radio value="activities">
                <div :class="['flex items-center', isMobile ? 'text-sm' : 'text-1rem']">
                  <div class="w-16px h-16px bg-[var(--el-color-primary)] m-2px"></div>
                  <span>活动</span>
                </div>
              </el-radio>
              <el-radio value="problem">
                <el-tooltip
                  content="A类问题是影乡响产品核心功能、安规或用户体验的严重问题，必须立即解决。
                              B类问题对用户体验有一定影响，但不会导致产品完全无法使用，可以在A类问题解决后逐步处理。
                              C类问题对产品核心功能和用户体验影响较小，通常是优化和改进类问题，可以在项目后期或资源充足时处理。"
                >
                  <div :class="['flex items-center', isMobile ? 'text-sm' : 'text-1rem']">
                    <div class="w-16px h-16px bg-[var(--el-color-danger)] m-2px"></div>
                    <span>问题</span>
                  </div>
                </el-tooltip>
              </el-radio>
              <el-radio value="technical">
                <div :class="['flex items-center', isMobile ? 'text-sm' : 'text-1rem']">
                  <div class="w-16px h-16px bg-[var(--el-color-warning)] m-2px"></div>
                  <span>预审记录</span>
                </div>
              </el-radio>
            </el-radio-group>

            <el-radio-group
              v-model="calendarType"
              :class="isMobile ? '' : 'm-l-10px'"
              @change="onCalendarTodoList"
            >
              <el-radio label="所有" value="all" />
              <el-radio label="按天" value="day" />
            </el-radio-group>
          </div>
          <div class="calendar-todo-list" v-if="filterCalendarTodoList.length > 0">
            <div
              v-for="item in filterCalendarTodoList"
              :key="item.id"
              :class="['calendar-todo-item', 'text-1rem', 'cursor-pointer']"
              @click="
                toBpm({
                  categoryId: item.categoryIds[0],
                  basicsId: item.basicsId,
                  page: item.type,
                  stage: item.stage,
                  id: item.id
                })
              "
            >
              <div class="flex w-100% position-relative !p-l-30px">
                <div
                  :class="[
                    item.type == 'activities' && '!bg-[var(--el-color-primary)]',
                    item.type == 'problem' && '!bg-[var(--el-color-danger)]',
                    item.type == 'technical' && '!bg-[var(--el-color-warning)]',
                    'position-absolute',
                    'left-[-5px]',
                    'top-[-5px]',
                    'text-.9rem',
                    'text-white',
                    'w-2.5rem',
                    'tag'
                  ]"
                >
                  {{
                    item.type == 'activities' ? '活动' : item.type == 'problem' ? '问题' : '预审'
                  }}
                </div>
                <UserAvatarList
                  v-model="item.director"
                  :size="30"
                  :limit="3"
                  :add="false"
                  :user-list="userList"
                  class="w-120px"
                />
                <div class="w-50% whitespace-nowrap overflow-hidden text-ellipsis">
                  <div class="w-100%">{{ item.name }}</div>
                  <div class="w-100% text-.9rem text-#888888">项目：{{ item.basicsName }}</div>
                </div>
                <div class="flex justify-between items-center w-[calc(50%-120px)]">
                  <div>{{ formatDate(item.endDate, 'YYYY-MM-DD') }}</div>
                </div>
                <DictTag size="small" type="project_activities_status" :value="item.status" />
              </div>
              <el-progress :percentage="item.progress" class="w-100%" />
            </div>
          </div>
          <el-empty v-else description="当前选择日期暂无事项" />
        </div>
        <el-calendar
          v-if="!isMobile"
          ref="calendar"
          class="h-100% w-30% rounded-5px"
          v-model="currentDate"
        >
          <template #header>
            <CardTitle title="日历" />
            <span>
              <el-date-picker
                v-model="currentDate"
                type="month"
                :clearable="false"
                class="!w-100px"
                size="small"
              />
            </span>
          </template>
          <template #date-cell="{ data }">
            <div class="position-relative">
              <el-badge
                :value="getDateCount(data.day)"
                class="w-full"
                :show-zero="false"
                :offset="[-4, 8]"
              >
                <div :class="[data.isSelected && 'is-selected', 'p-t-5px', 'p-b-5px', 'w-36px']">
                  {{ data.day.split('-').slice(2).join('-') }}
                </div>
              </el-badge>
            </div>
          </template>
        </el-calendar>
      </div>

      <!-- 移动端日历单独显示 -->
      <div v-if="isMobile" class="w-full rounded-5px bg-white p-10px mb-10px">
        <el-calendar ref="calendar" class="h-auto w-full" v-model="currentDate">
          <template #header>
            <div class="flex justify-between items-center">
              <CardTitle title="日历" />
              <el-date-picker
                v-model="currentDate"
                type="month"
                :clearable="false"
                class="!w-120px"
                size="small"
              />
            </div>
          </template>
          <template #date-cell="{ data }">
            <div class="position-relative">
              <el-badge
                :value="getDateCount(data.day)"
                class="w-full"
                :show-zero="false"
                :offset="[-4, 8]"
              >
                <div
                  :class="[
                    data.isSelected && 'is-selected',
                    'p-t-5px',
                    'p-b-5px',
                    'w-full',
                    'text-center'
                  ]"
                >
                  {{ data.day.split('-').slice(2).join('-') }}
                </div>
              </el-badge>
            </div>
          </template>
        </el-calendar>
      </div>
      <div :class="[isMobile ? 'flex flex-col gap-16px' : 'h-[calc(50%-60px)] mt-16px flex gap-16px']">
        <div
:class="[
          'modern-card todo-card',
          isMobile ? 'w-full' : 'w-33%'
        ]">
          <div :class="isMobile ? 'h-auto mb-10px' : 'h-40px'">
            <CardTitle
              :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的流程待办`"
            />
          </div>
          <div
            :class="['overflow-auto', isMobile ? 'max-h-300px' : 'h-[calc(100%-60px)]']"
            v-if="auditList.length > 0"
          >
            <div
              v-for="item in auditList"
              :key="item.id"
              :class="['todo-item', isMobile ? 'mobile-todo-item' : '']"
              @click="handleAudit(item)"
            >
              <div class="w-10px h-10px rounded-50% bg-[var(--el-color-primary)]"></div>
              <el-tooltip
                :content="item.formVariables?.['approve_title'] || item.processInstance.name"
              >
                <div :class="['whitespace-nowrap overflow-hidden text-ellipsis w-90%']">
                  {{ item.formVariables?.['approve_title'] || item.processInstance.name }}
                </div>
              </el-tooltip>
              <div :class="[isMobile ? 'w-full text-xs text-gray-500' : 'w-50%']"
                >提交人:{{ item?.processInstance?.startUser?.nickname }}</div
              >
              <div
                :class="[
                  'overflow-hidden whitespace-nowrap text-ellipsis',
                  isMobile ? 'w-full text-xs text-gray-400' : 'w-50%'
                ]"
              >
                {{ formatDate(item.createTime) }}
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无待办" />
        </div>
        <div
:class="[
          'modern-card mention-card',
          isMobile ? 'w-full' : 'w-33%'
        ]">
          <div :class="isMobile ? 'h-auto mb-10px' : 'h-40px'">
            <CardTitle
              :title="`@${dataPerson.type === 'my' ? '我' : dataPerson.personName}的事项`"
            />
          </div>
          <div v-infinite-scroll="loadMention" v-if="mentionList.length > 0">
            <div
              class="mention-item"
              v-for="item in mentionList"
              :key="item.id"
              @click="handleMention(item)"
            >
              <div class="whitespace-nowrap overflow-hidden text-ellipsis"
                >项目：{{ item.basicsName }}</div
              >
              <div class="whitespace-nowrap overflow-hidden text-ellipsis"
                >{{ getTypeName(item.type) }}：{{ item.itemName }}</div
              >
              <!-- @click="handleMention(item)" -->
              <el-tooltip :content="item.mentionContent">
                <div class="whitespace-nowrap overflow-hidden text-ellipsis">{{
                  item.mentionContent
                }}</div>
              </el-tooltip>
              <div>{{ formatToDateTime(item.createTime) }}</div>
            </div>
          </div>
          <el-empty
            v-else
            :description="`当前无@${dataPerson.type === 'my' ? '我' : dataPerson.personName}的事项`"
          />
        </div>
        <div
:class="[
          'modern-card log-card',
          isMobile ? 'w-full' : 'w-33%'
        ]">
          <div :class="isMobile ? 'h-auto mb-10px' : 'h-40px'">
            <CardTitle
              :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的操作记录`"
            />
          </div>
          <div
            :class="['overflow-auto', isMobile ? 'max-h-300px' : 'h-[calc(100%-40px)]']"
            v-infinite-scroll="loadLog"
            v-if="logList.length > 0"
          >
            <el-timeline>
              <el-timeline-item v-for="item in logList" :key="item.id">
                <div class="el-timeline-item__timestamp">
                  {{ formatDate(item.createTime) }}-<span class="el-timeline-item__user">{{
                    item.createName
                  }}</span>
                </div>
                {{ item.content }}
                <div v-if="item.modifyFieldName">
                  <template v-if="shouldShowDictTag(item.modifyField)">
                    字段:{{ item.modifyFieldName }} 从:{{
                      formatDictLabel(item.modifyField, item.beforeValue)
                    }}
                    修改为:{{ formatDictLabel(item.modifyField, item.afterValue) }}
                  </template>
                  <template
                    v-else-if="
                      ['managers', 'userIds', 'director', 'coordinate'].includes(
                        item.modifyField
                      ) ||
                      getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                        (dict) => dict.value === item.modifyField
                      )?.label
                    "
                  >
                    字段:{{ item.modifyFieldName }} 从:{{
                      getUserNickName(userList, item.beforeValue)
                    }}
                    修改为:{{ getUserNickName(userList, item.afterValue) }}
                  </template>
                  <template v-else>
                    字段:{{ item.modifyFieldName }} 从:{{ item.beforeValue }} 修改为:{{
                      item.afterValue
                    }}
                  </template>
                </div>
                <div v-if="item.processInstanceId">
                  <el-link type="primary" @click="toBpm(item.processInstanceId)">跳转流程</el-link>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <el-empty v-else description="当前无操作记录" />
        </div>
      </div>
    </div>

    <!-- 桌面端右侧个人信息区域 -->
    <div v-if="!isMobile" class="w-20% pl-16px">
      <div class="modern-card user-info-desktop mb-16px">
        <div class="flex items-center">
          <el-avatar :src="avatar" :size="50" class="mr-16px user-avatar">
            <img src="@/assets/imgs/avatar.gif" alt="" />
          </el-avatar>
          <div>
            <div class="font-medium text-gray-800">{{ username }}</div>
            <div class="text-sm text-blue-500"> 欢迎回来！ </div>
          </div>
        </div>
      </div>
      <div class="modern-card personal-data-card h-[calc(100%-110px)]">
        <div class="p-10px">
          <CardTitle :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的数据`" />
        </div>
        <div class="bg-white h-[calc(100%-40px)] w-full">
          <div class="p-10px h-20% w-full">
            <div
              v-for="dict in getIntDictOptions('project_activities_status')"
              :key="dict.value"
              :class="['data-item', `type-${dict.colorType || 'default'}`]"
              v-show="dict.label !== '暂停'"
            >
              <div>{{ dict.label }}</div>
              <div>{{ getStatusCount(dict.value) }}</div>
              <div><Icon :icon="getIcon(dict.value)" class="!text-1.8rem" /></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端个人数据区域 -->
    <div v-if="isMobile" class="modern-card personal-data-mobile">
      <div class="mb-12px">
        <CardTitle :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的数据`" />
      </div>
      <div class="grid grid-cols-2 gap-12px">
        <div
          v-for="dict in getIntDictOptions('project_activities_status')"
          :key="dict.value"
          :class="['mobile-data-item', `type-${dict.colorType || 'default'}`]"
          v-show="dict.label !== '暂停'"
        >
          <div class="text-sm">{{ dict.label }}</div>
          <div class="text-lg font-bold">{{ getStatusCount(dict.value) }}</div>
          <div><Icon :icon="getIcon(dict.value)" class="!text-1.2rem" /></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { dateUtil, formatToDateTime } from '@/utils/dateUtil'
import { InstrumentApi } from '@/api/project/instrument'
import { formatDate } from '@/utils/formatTime'
import { getSimpleUserList } from '@/api/system/user'
import { useCache } from '@/hooks/web/useCache'
import { getIntDictOptions } from '@/utils/dict'
import * as TaskApi from '@/api/bpm/task'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { BasicsApi } from '@/api/project/basics'
import { CommentApi } from '@/api/infra/comment'

const userStore = useUserStore()
const appStore = useAppStore()
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const userList = ref<any[]>([])
const router = useRouter()
const { wsCache } = useCache()

const dataPerson = ref({
  type: 'my',
  personId: useUserStore().getUser.id,
  personName: useUserStore().getUser.nickname
})

const viewUserList = ref<any[]>([])

// 移动端检测
const isMobile = computed(() => appStore.getMobile)

const calendar = ref()
const currentDate = ref<Date>(new Date())
const calendarType = ref('all')
const listType = ref('all')
const calendarTotalList = ref<any[]>([])
const calendarTodoList = ref<any[]>([])
const filterCalendarTodoList = ref<any[]>([])
const statusCountList = ref<any[]>([])
const auditList = ref<any[]>([])
const logList = ref<any[]>([])
const logTotal = ref(0)
const logQueryParams = ref({
  pageNo: 1,
  pageSize: 10,
  creator: userStore.getUser.id
})

const listChange = () => {
  if (listType.value === 'all') {
    filterCalendarTodoList.value = calendarTodoList.value
  } else {
    filterCalendarTodoList.value = calendarTodoList.value.filter(
      (item) => item.type === listType.value
    )
  }
}

const toBpm = (query: any) => {
  wsCache.set('project_page_show_form', query)
  router.push({
    name: 'ProjectCenter'
  })
}

const getIcon = (status: number) => {
  switch (status) {
    case 1:
      return 'fa-solid:running'
    case 2:
      return 'ep:warning-filled'
    case 3:
      return 'ep:success-filled'
    case 4:
      return 'fa:compass'
    case 10:
      return 'fa:pencil-square'
    case 11:
      return 'fa:calendar-times-o'
  }
}

const getLogList = async () => {
  logQueryParams.value.creator = dataPerson.value.personId
  const res = await BasicsApi.getBasicsLogPage(logQueryParams.value)
  logList.value = logList.value.concat(res.list)
  logTotal.value = res.total
}

const loadLog = () => {
  logQueryParams.value.pageNo++
  if (logQueryParams.value.pageNo > Math.ceil(logTotal.value / logQueryParams.value.pageSize))
    return
  getLogList()
}

const mentionQueryParams = ref({
  pageNo: 1,
  pageSize: 10,
  userId: userStore.getUser.id,
  status: 0
})

const mentionList = ref<any[]>([])
const mentionTotal = ref(0)

const getMentionList = async () => {
  mentionQueryParams.value.userId = dataPerson.value.personId
  const res = await CommentApi.getMentionPage(mentionQueryParams.value)
  mentionList.value = mentionList.value.concat(res.list)
  mentionTotal.value = res.total
}

const loadMention = () => {
  mentionQueryParams.value.pageNo++
  if (
    mentionQueryParams.value.pageNo >
    Math.ceil(mentionTotal.value / mentionQueryParams.value.pageSize)
  )
    return
  getMentionList()
}

const getTypeName = (type: string) => {
  switch (type) {
    case 'activities':
      return '活动'
    case 'problem':
      return '问题'
    case 'risk':
      return '风险'
    case 'technical':
      return '技术评审'
    default:
      return ''
  }
}

const handleMention = async (item: any) => {
  const res = await BasicsApi.getSimpleBasics(item.basicsId)
  wsCache.set('project_page_show_form', {
    categoryId: res.categoryIds?.[0],
    basicsId: item.basicsId,
    page: item.type,
    stage: item.stage,
    id: item.itemId
  })
  router.push({
    name: 'ProjectCenter'
  })
}

/** 查询任务列表 */
const getTodoList = async () => {
  const data = await TaskApi.getTaskTodoPage({ pageNo: 1, pageSize: -1 })
  auditList.value = data.list
}

/** 获取指定日期的总数 */
const getDateCount = (date: string) => {
  return calendarTotalList.value.find((item) => item.date === date)?.total
}
/** 获取日历待办总数 */
const calcCalendarTotalList = async () => {
  const day = dateUtil(currentDate.value)
  const firstDayOfMonth = day.startOf('month') // 当月第一天
  const lastDayOfMonth = day.endOf('month') // 当月最后一天

  // 1. 起始日：当月第一天所在周的周日
  const start = firstDayOfMonth.clone().subtract(firstDayOfMonth.day(), 'day') // day() 返回 0（周一）到 6（周日），减去对应天数到周日

  // 2. 结束日：当月最后一天所在周的周六
  const endDayOfWeek = lastDayOfMonth.day()
  const daysToAdd = (6 - endDayOfWeek + 7) % 7 // 计算到周六需要加的天数
  const end = lastDayOfMonth.clone().add(daysToAdd, 'day')
  const res = await InstrumentApi.getCalendarCountList(
    start.format('YYYY-MM-DD'),
    end.format('YYYY-MM-DD'),
    dataPerson.value.personId
  )
  calendarTotalList.value = res
}
/** 获取指定日期的待办 */
const onCalendarTodoList = async () => {
  const res = await InstrumentApi.getCalendarTodoList(
    dateUtil(currentDate.value).format('YYYY-MM-DD'),
    calendarType.value,
    dataPerson.value.personId
  )
  calendarTodoList.value = res
  filterCalendarTodoList.value = res
}
/** 获取用户列表 */
const onUserList = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}

const onStatusCountList = async () => {
  const res = await InstrumentApi.getStatusCountList()
  statusCountList.value = res
}

const getStatusCount = (dictVal: number) => {
  return statusCountList.value.find((item) => item.status == dictVal)?.total || 0
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}

const viewTypeChange = () => {
  if (dataPerson.value.type === 'my') {
    dataPerson.value.personId = useUserStore().getUser.id as any
    dataPerson.value.personName = useUserStore().getUser.nickname as any
  }

  wsCache.set('workbench_view_type', dataPerson.value)
}

const onDataPersonChange = () => {
  dataPerson.value.personName = userList.value.find(
    (item) => item.id === dataPerson.value.personId
  )?.nickname
  calcCalendarTotalList()
  onCalendarTodoList()
  onUserList()
  onStatusCountList()
  getTodoList()
  logList.value = []
  getLogList()
  getMentionList()
}

watch(
  () => currentDate.value,
  (newVal, oldVal) => {
    calendarType.value = 'day'
    onCalendarTodoList()
    // 1. 将值转为 dayjs 对象（根据你的 dateUtil 判断是否需要调整）
    const newDate = dateUtil(newVal)
    const oldDate = dateUtil(oldVal)

    // 2. 判断是否跨月（年份或月份不同）
    if (newDate.year() !== oldDate.year() || newDate.month() !== oldDate.month()) {
      calcCalendarTotalList()
    }
  }
)
onMounted(() => {
  const res = wsCache.get('workbench_view_type')
  if (res) {
    dataPerson.value = res
  }
  calcCalendarTotalList()
  onCalendarTodoList()
  onUserList()
  onStatusCountList()
  getTodoList()
  getLogList()
  getMentionList()
})
</script>

<style lang="scss" scoped>
/* 工作台容器 */
.workbench-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 16px;
  min-height: 100vh;
}

/* 现代化卡片基础样式 */
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

/* 特定卡片样式 */
.filter-card {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);

  .filter-radio-group {
    :deep(.el-radio-button__inner) {
      border-radius: 8px;
      border: none;
      background: #f8fafc;
      color: #64748b;
      transition: all 0.2s;

      &:hover {
        background: #e2e8f0;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }

  .filter-select {
    :deep(.el-input__inner) {
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      transition: all 0.2s;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
  }
}

.main-content-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

.user-info-mobile, .user-info-desktop {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .user-avatar {
    border: 3px solid rgba(255, 255, 255, 0.3);
  }
}

.todo-card {
  background: linear-gradient(135deg, rgba(254, 202, 202, 0.1) 0%, rgba(252, 165, 165, 0.1) 100%);
  border-left: 4px solid #ef4444;
}

.mention-card {
  background: linear-gradient(135deg, rgba(196, 181, 253, 0.1) 0%, rgba(167, 139, 250, 0.1) 100%);
  border-left: 4px solid #8b5cf6;
}

.log-card {
  background: linear-gradient(135deg, rgba(134, 239, 172, 0.1) 0%, rgba(74, 222, 128, 0.1) 100%);
  border-left: 4px solid #22c55e;
}

.personal-data-card, .personal-data-mobile {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
  border-left: 4px solid #f59e0b;
}

:deep(.el-card__body),
:deep(.el-calendar__header),
:deep(.el-calendar__body) {
  padding: 5px !important;
}
:deep(.el-calendar-table) {
  .el-calendar-day {
    height: 3.2rem;
    line-height: calc(3.2rem - 10px);
    font-size: 1rem;
    padding: 0;
    text-align: center;
  }
  th {
    font-size: 1rem;
    padding: 5px 0;
  }
}
.calendar-list {
  height: 100%;
  padding: 5px;

  .calendar-todo-list {
    height: calc(100% - 30px);
    padding: 10px 5px;
    overflow: auto;

    & > * + * {
      margin-top: 12px;
    }

    .calendar-todo-item {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
      border: 1px solid rgba(226, 232, 240, 0.8);
      padding: 12px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;

      /* 新增阴影效果 */
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      & > * {
        padding: 2px;
      }

      :deep(.el-tag) {
        font-size: 1rem;
        height: 24px;
      }
    }

    .calendar-todo-item:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.data-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  height: 100%;
  border-radius: 12px;
  padding: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: blur(10px);

  & > * + * {
    margin-top: 8px;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
}

.data-item + .data-item {
  margin-top: 12px;
}
.type-default,
.type-primary {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
.type-danger {
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}
.type-warning {
  color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}
.type-success {
  color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
}

.todo-item {
  display: flex;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: var(--secondary-text-color);
  cursor: pointer;
  justify-content: flex-start;
  align-items: center;
  background: linear-gradient(135deg, rgba(248, 252, 255, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  height: 4rem;
  border-radius: 12px;
  padding: 0px 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);

  &:hover {
    background: linear-gradient(135deg, rgba(249, 248, 255, 0.9) 0%, rgba(243, 244, 246, 0.9) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
  }
}

.todo-item + .todo-item {
  margin-top: 8px;
}

.mention-item {
  padding: 12px;
  margin-bottom: 8px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 10px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: rgba(139, 92, 246, 0.3);
  }
}

:deep(.el-timeline-item__content) {
  white-space: pre-wrap;
  font-size: 0.8rem;
  color: var(--regular-text-color);
  background-color: #f8fcff;
  border-radius: 5px;
  padding: 5px;
}
.el-timeline-item__timestamp {
  padding: 3px;
  font-size: 0.8rem;

  .el-timeline-item__user {
    color: var(--el-color-warning);
    font-size: 0.8rem;
  }
}

:deep(.el-link__inner) {
  width: 100%;
  justify-content: flex-start;
}

// :deep(.el-progress-bar__outer) {
//   background-color: #fff !important;
// }

:deep(.el-progress-bar__inner) {
  background-color: var(--el-color-success) !important;
}

:deep(.el-progress__text) {
  color: #888888 !important;
}

:deep(.el-radio__label) {
  font-size: 1rem !important;
}

.tag {
  border-top-left-radius: 5px;
  border-bottom-right-radius: 5px;
  text-align: center;
}

:deep(.dict-tag),
:deep(.el-tag) {
  border-radius: 8px !important;
  text-align: center;
  position: absolute;
  top: -3px;
  right: -3px;
  font-size: 0.8rem !important;
  width: 3.5rem;
  height: 1.5rem !important;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}

/* 增强交互反馈 */
.calendar-todo-item {
  &:active {
    transform: translateY(0px) scale(0.98);
  }
}

.todo-item {
  &:active {
    transform: translateY(0px) scale(0.98);
  }
}

.mention-item {
  &:active {
    transform: translateY(0px) scale(0.98);
  }
}

.data-item {
  &:active {
    transform: translateY(0px) scale(0.95);
  }
}

.mobile-data-item {
  &:active {
    transform: translateY(0px) scale(0.95);
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-card {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 脉冲动画用于重要元素 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.user-avatar {
  &:hover {
    animation: pulse 1s infinite;
  }
}

/* 移动端专用样式 */
@media (max-width: 768px) {
  .workbench-container {
    padding: 12px;
  }

  .modern-card {
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
  }

  .calendar-list {
    .calendar-todo-list {
      height: auto !important;
      max-height: 400px;

      .calendar-todo-item {
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 10px;

        & > div {
          padding: 4px 2px;
        }

        .tag {
          font-size: 0.8rem !important;
          width: 2.5rem;
          height: 1.2rem !important;
        }
      }
    }
  }

  .mobile-todo-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    height: auto !important;
    line-height: 1.4 !important;
    padding: 16px !important;
    gap: 6px;
    border-radius: 10px;

    & > div {
      margin-left: 0 !important;
    }
  }

  .mobile-data-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 90px;
    border-radius: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    & > * + * {
      margin-top: 6px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }
  }

  :deep(.el-calendar-table) {
    .el-calendar-day {
      height: 3rem !important;
      line-height: calc(3rem - 10px) !important;
      font-size: 0.9rem !important;
    }

    th {
      font-size: 0.9rem !important;
      padding: 3px 0 !important;
    }
  }

  :deep(.el-radio-group) {
    .el-radio {
      margin-right: 8px !important;

      .el-radio__label {
        font-size: 0.9rem !important;
      }

      .el-radio__input {
        .el-radio__inner {
          width: 16px !important;
          height: 16px !important;
        }
      }
    }
  }

  :deep(.el-button) {
    min-height: 44px !important; /* 触摸友好的最小高度 */
  }

  :deep(.el-date-picker) {
    .el-input__inner {
      height: 44px !important;
    }
  }

  :deep(.el-timeline-item__content) {
    font-size: 0.75rem !important;
    padding: 8px !important;
    margin-bottom: 8px;
    border-radius: 8px !important;
  }

  .el-timeline-item__timestamp {
    font-size: 0.7rem !important;

    .el-timeline-item__user {
      font-size: 0.7rem !important;
    }
  }
}

/* 全局组件美化 */
:deep(.el-calendar) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.8);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

:deep(.el-empty) {
  .el-empty__description {
    color: #94a3b8;
    font-size: 0.9rem;
  }

  .el-empty__image {
    opacity: 0.6;
  }
}

:deep(.el-timeline) {
  .el-timeline-item__wrapper {
    padding-left: 20px;
  }

  .el-timeline-item__tail {
    border-left: 2px solid #e2e8f0;
  }

  .el-timeline-item__node {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    width: 12px;
    height: 12px;
  }
}

:deep(.el-badge) {
  .el-badge__content {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    font-size: 0.75rem;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  }
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

:deep(.el-input) {
  .el-input__inner {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}
.mention-item {
  padding: 5px 10px;
  background-color: #f8fcff;
  font-size: 0.85rem !important;
  border-radius: 5px;
  cursor: pointer;
  color: var(--secondary-text-color);
}

.mention-item + .mention-item {
  margin-top: 10px;
}
</style>
