<template>
  <div :class="['h-full', isMobile ? 'flex flex-col' : 'flex']">
    <!-- 数据类型选择区域 -->
    <div
      v-if="dataPerson.type === 'other'"
      class="w-10% rounded-5px bg-white ml-5px p-5px h-100% person-selector-panel"
    >
      <div>
        <el-radio-group size="small" v-model="dataPerson.type" @change="viewTypeChange">
          <el-radio-button label="个人数据" value="my" />
          <el-radio-button
            label="他人数据"
            value="other"
            v-hasRole="['r&d_manager', 'super_admin', 'porject_manager']"
          />
        </el-radio-group>
      </div>
      <el-input
        v-model="userName"
        class="mt-10px mb-10px"
        clearable
        placeholder="请输入用户名称"
        size="small"
      >
        <template #prefix>
          <Icon icon="ep:search" />
        </template>
      </el-input>
      <el-tree
        ref="treeRef"
        :data="viewUserList"
        :props="{ label: 'nickname' }"
        :current-node-key="dataPerson.personId"
        class="h-[calc(100%-100px)] overflow-auto"
        :filter-node-method="filterNode"
        highlight-current
        node-key="id"
        @node-click="onDataPersonChange"
      >
        <template #default="{ node, data }">
          <span :id="`user-${data.id}`">{{ node.label }}</span>
        </template>
      </el-tree>
    </div>

    <!-- 浮动展开按钮 (仅在个人数据模式下显示) -->
    <div
      v-if="dataPerson.type === 'my'"
      class="floating-expand-btn"
      @click="expandPersonSelector"
      v-hasRole="['r&d_manager', 'super_admin', 'porject_manager']"
    >
      <el-tooltip content="查看他人数据" placement="right">
        <el-button type="primary" size="small" class="w-24px !h-80px expand-button">
          他人数据
          <Icon icon="ep:right" />
        </el-button>
      </el-tooltip>
    </div>
    <div :class="['h-full', isMobile ? 'w-full' : 'w-[calc(100%-20%)] p-l-10px p-r-10px']">
      <div
        :class="['w-100% rounded-5px bg-white p-10px', isMobile ? 'h-auto mb-10px' : 'h-50% flex']"
      >
        <div :class="['calendar-list', isMobile ? 'w-full' : 'w-70%']">
          <div :class="['flex items-center', isMobile ? 'flex-col gap-2' : 'justify-between']">
            <CardTitle
              :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的事项`"
            />

            <el-radio-group
              size="small"
              v-model="listType"
              @change="listChange"
              :class="isMobile ? 'flex-wrap' : ''"
            >
              <el-radio :class="isMobile ? 'text-sm' : 'text-1rem'" label="所有" value="all" />
              <el-radio value="activities">
                <div :class="['flex items-center', isMobile ? 'text-sm' : 'text-1rem']">
                  <div class="w-16px h-16px bg-[var(--el-color-primary)] m-2px"></div>
                  <span>活动</span>
                </div>
              </el-radio>
              <el-radio value="problem">
                <el-tooltip
                  content="A类问题是影乡响产品核心功能、安规或用户体验的严重问题，必须立即解决。
                              B类问题对用户体验有一定影响，但不会导致产品完全无法使用，可以在A类问题解决后逐步处理。
                              C类问题对产品核心功能和用户体验影响较小，通常是优化和改进类问题，可以在项目后期或资源充足时处理。"
                >
                  <div :class="['flex items-center', isMobile ? 'text-sm' : 'text-1rem']">
                    <div class="w-16px h-16px bg-[var(--el-color-danger)] m-2px"></div>
                    <span>问题</span>
                  </div>
                </el-tooltip>
              </el-radio>
              <el-radio value="technical">
                <div :class="['flex items-center', isMobile ? 'text-sm' : 'text-1rem']">
                  <div class="w-16px h-16px bg-[var(--el-color-warning)] m-2px"></div>
                  <span>预审记录</span>
                </div>
              </el-radio>
            </el-radio-group>

            <el-radio-group
              v-model="calendarType"
              :class="isMobile ? '' : 'm-l-10px'"
              @change="onCalendarTodoList"
            >
              <el-radio label="所有" value="all" />
              <el-radio label="按天" value="day" />
            </el-radio-group>
          </div>
          <div class="calendar-todo-list" v-if="filterCalendarTodoList.length > 0">
            <div
              v-for="item in filterCalendarTodoList"
              :key="item.id"
              :class="['calendar-todo-item', 'text-1rem', 'cursor-pointer']"
              @click="
                toBpm({
                  categoryId: item.categoryIds[0],
                  basicsId: item.basicsId,
                  page: item.type,
                  stage: item.stage,
                  id: item.id
                })
              "
            >
              <div class="flex w-100% position-relative !p-l-30px">
                <div
                  :class="[
                    item.type == 'activities' && '!bg-[var(--el-color-primary)]',
                    item.type == 'problem' && '!bg-[var(--el-color-danger)]',
                    item.type == 'technical' && '!bg-[var(--el-color-warning)]',
                    'position-absolute',
                    'left-[-5px]',
                    'top-[-5px]',
                    'text-.9rem',
                    'text-white',
                    'w-2.5rem',
                    'tag'
                  ]"
                >
                  {{
                    item.type == 'activities' ? '活动' : item.type == 'problem' ? '问题' : '预审'
                  }}
                </div>
                <UserAvatarList
                  v-model="item.director"
                  :size="30"
                  :limit="3"
                  :add="false"
                  :user-list="userList"
                  class="w-120px"
                />
                <div class="w-50% whitespace-nowrap overflow-hidden text-ellipsis">
                  <div class="w-100%">{{ item.name }}</div>
                  <div class="w-100% text-.9rem text-#888888">项目：{{ item.basicsName }}</div>
                </div>
                <div class="flex justify-between items-center w-[calc(50%-120px)]">
                  <div>{{ formatDate(item.endDate, 'YYYY-MM-DD') }}</div>
                </div>
                <DictTag size="small" type="project_activities_status" :value="item.status" />
              </div>
              <el-progress :percentage="item.progress" class="w-100%" />
            </div>
          </div>
          <el-empty v-else description="当前选择日期暂无事项" />
        </div>
        <el-calendar ref="calendar" class="h-100% w-30% rounded-5px" v-model="currentDate">
          <template #header>
            <CardTitle title="日历" />
            <span>
              <el-date-picker
                v-model="currentDate"
                type="month"
                :clearable="false"
                class="!w-100px"
                size="small"
              />
            </span>
          </template>
          <template #date-cell="{ data }">
            <div class="position-relative">
              <el-badge
                :value="getDateCount(data.day)"
                class="w-full"
                :show-zero="false"
                :offset="[-4, 8]"
              >
                <div :class="[data.isSelected && 'is-selected', 'p-t-5px', 'p-b-5px', 'w-36px']">
                  {{ data.day.split('-').slice(2).join('-') }}
                </div>
              </el-badge>
            </div>
          </template>
        </el-calendar>
      </div>

      <div :class="[isMobile ? 'flex flex-col gap-10px' : 'h-[calc(50%-10px)] m-t-10px flex']">
        <div
          :class="[
            'bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-10px',
            isMobile ? 'w-full' : 'w-33%'
          ]"
        >
          <div :class="isMobile ? 'h-auto mb-10px' : 'h-40px'">
            <CardTitle
              :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的流程待办`"
            />
          </div>
          <div
            :class="['overflow-auto', isMobile ? 'max-h-300px' : 'h-[calc(100%-60px)]']"
            v-if="auditList.length > 0"
          >
            <div
              v-for="item in auditList"
              :key="item.id"
              :class="['todo-item', isMobile ? 'mobile-todo-item' : '']"
              @click="handleAudit(item)"
            >
              <div class="w-10px h-10px rounded-50% bg-[var(--el-color-primary)]"></div>
              <el-tooltip
                :content="item.formVariables?.['approve_title'] || item.processInstance.name"
              >
                <div :class="['whitespace-nowrap overflow-hidden text-ellipsis w-90%']">
                  {{ item.formVariables?.['approve_title'] || item.processInstance.name }}
                </div>
              </el-tooltip>
              <div :class="[isMobile ? 'w-full text-xs text-gray-500' : 'w-50%']"
                >提交人:{{ item?.processInstance?.startUser?.nickname }}</div
              >
              <div
                :class="[
                  'overflow-hidden whitespace-nowrap text-ellipsis',
                  isMobile ? 'w-full text-xs text-gray-400' : 'w-50%'
                ]"
              >
                {{ formatDate(item.createTime) }}
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无待办" />
        </div>
        <div
          :class="[
            'bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-10px',
            isMobile ? 'w-full' : 'w-33% m-l-10px'
          ]"
        >
          <div :class="isMobile ? 'h-auto mb-10px' : 'h-40px'">
            <CardTitle
              :title="`@${dataPerson.type === 'my' ? '我' : dataPerson.personName}的事项`"
            />
          </div>
          <div v-infinite-scroll="loadMention" v-if="mentionList.length > 0">
            <div
              class="mention-item"
              v-for="item in mentionList"
              :key="item.id"
              @click="handleMention(item)"
            >
              <div class="whitespace-nowrap overflow-hidden text-ellipsis"
                >项目：{{ item.basicsName }}</div
              >
              <div class="whitespace-nowrap overflow-hidden text-ellipsis"
                >{{ getTypeName(item.type) }}：{{ item.itemName }}</div
              >
              <!-- @click="handleMention(item)" -->
              <el-tooltip :content="item.mentionContent">
                <div class="whitespace-nowrap overflow-hidden text-ellipsis">{{
                  item.mentionContent
                }}</div>
              </el-tooltip>
              <div>{{ formatToDateTime(item.createTime) }}</div>
            </div>
          </div>
          <el-empty
            v-else
            :description="`当前无@${dataPerson.type === 'my' ? '我' : dataPerson.personName}的事项`"
          />
        </div>
        <div
          :class="[
            'bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-10px',
            isMobile ? 'w-full' : 'w-[calc(33%-10px)] m-l-10px'
          ]"
        >
          <div :class="isMobile ? 'h-auto mb-10px' : 'h-40px'">
            <CardTitle
              :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的操作记录`"
            />
          </div>
          <div
            :class="['overflow-auto', isMobile ? 'max-h-300px' : 'h-[calc(100%-40px)]']"
            v-infinite-scroll="loadLog"
            v-if="logList.length > 0"
          >
            <el-timeline>
              <el-timeline-item v-for="item in logList" :key="item.id">
                <div class="el-timeline-item__timestamp">
                  {{ formatDate(item.createTime) }}-<span class="el-timeline-item__user">{{
                    item.createName
                  }}</span>
                </div>
                {{ item.content }}
                <div v-if="item.modifyFieldName">
                  <template v-if="shouldShowDictTag(item.modifyField)">
                    字段:{{ item.modifyFieldName }} 从:{{
                      formatDictLabel(item.modifyField, item.beforeValue)
                    }}
                    修改为:{{ formatDictLabel(item.modifyField, item.afterValue) }}
                  </template>
                  <template
                    v-else-if="
                      ['managers', 'userIds', 'director', 'coordinate'].includes(
                        item.modifyField
                      ) ||
                      getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                        (dict) => dict.value === item.modifyField
                      )?.label
                    "
                  >
                    字段:{{ item.modifyFieldName }} 从:{{
                      getUserNickName(userList, item.beforeValue)
                    }}
                    修改为:{{ getUserNickName(userList, item.afterValue) }}
                  </template>
                  <template v-else>
                    字段:{{ item.modifyFieldName }} 从:{{ item.beforeValue }} 修改为:{{
                      item.afterValue
                    }}
                  </template>
                </div>
                <div v-if="item.processInstanceId">
                  <el-link type="primary" @click="toBpm(item.processInstanceId)">跳转流程</el-link>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <el-empty v-else description="当前无操作记录" />
        </div>
      </div>
    </div>

    <!-- 桌面端右侧个人信息区域 -->
    <div :class="dataPerson.type === 'my' ? 'w-20%' : 'w-10%'">
      <ContentWrap>
        <div class="flex items-center">
          <el-avatar :src="avatar" :size="50" class="mr-16px">
            <img src="@/assets/imgs/avatar.gif" alt="" />
          </el-avatar>
          <div>
            <div>{{ username }}</div>
            <div class="text-1rem color-[var(--el-color-info)]"> 欢迎回来！ </div>
          </div>
        </div>
      </ContentWrap>
      <div
        class="h-[calc(100%-78px)] bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-5px w-full"
      >
        <div class="p-10px">
          <CardTitle :title="`${dataPerson.type === 'my' ? '我' : dataPerson.personName}的数据`" />
        </div>
        <div class="bg-white h-[calc(100%-40px)] w-full">
          <div class="p-10px h-20% w-full">
            <div
              v-for="dict in getIntDictOptions('project_activities_status')"
              :key="dict.value"
              :class="['data-item', `type-${dict.colorType || 'default'}`]"
              v-show="dict.label !== '暂停'"
            >
              <div>{{ dict.label }}</div>
              <div>{{ getStatusCount(dict.value) }}</div>
              <div><Icon :icon="getIcon(dict.value)" class="!text-1.8rem" /></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { dateUtil, formatToDateTime } from '@/utils/dateUtil'
import { InstrumentApi } from '@/api/project/instrument'
import { formatDate } from '@/utils/formatTime'
import { getSimpleUserList } from '@/api/system/user'
import { useCache } from '@/hooks/web/useCache'
import { getIntDictOptions } from '@/utils/dict'
import * as TaskApi from '@/api/bpm/task'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { BasicsApi } from '@/api/project/basics'
import { CommentApi } from '@/api/infra/comment'
import { SupportLibraryApi } from '@/api/project/supportlibrary'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const userStore = useUserStore()
const appStore = useAppStore()
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const userList = ref<any[]>([])
const router = useRouter()
const { wsCache } = useCache()

const dataPerson = ref({
  type: 'my',
  personId: useUserStore().getUser.id,
  personName: useUserStore().getUser.nickname
})

const viewUserList = ref<any[]>([])

// 移动端检测
const isMobile = computed(() => appStore.getMobile)

const calendar = ref()
const currentDate = ref<Date>(new Date())
const calendarType = ref('all')
const listType = ref('all')
const calendarTotalList = ref<any[]>([])
const calendarTodoList = ref<any[]>([])
const filterCalendarTodoList = ref<any[]>([])
const statusCountList = ref<any[]>([])
const auditList = ref<any[]>([])
const logList = ref<any[]>([])
const logTotal = ref(0)
const logQueryParams = ref({
  pageNo: 1,
  pageSize: 10,
  creator: userStore.getUser.id
})

const listChange = () => {
  if (listType.value === 'all') {
    filterCalendarTodoList.value = calendarTodoList.value
  } else {
    filterCalendarTodoList.value = calendarTodoList.value.filter(
      (item) => item.type === listType.value
    )
  }
}

const toBpm = (query: any) => {
  wsCache.set('project_page_show_form', query)
  router.push({
    name: 'ProjectCenter'
  })
}

const getIcon = (status: number) => {
  switch (status) {
    case 1:
      return 'fa-solid:running'
    case 2:
      return 'ep:warning-filled'
    case 3:
      return 'ep:success-filled'
    case 4:
      return 'fa:compass'
    case 10:
      return 'fa:pencil-square'
    case 11:
      return 'fa:calendar-times-o'
  }
}

const getLogList = async () => {
  logQueryParams.value.creator = dataPerson.value.personId
  const res = await BasicsApi.getBasicsLogPage(logQueryParams.value)
  logList.value = logList.value.concat(res.list)
  logTotal.value = res.total
}

const loadLog = () => {
  logQueryParams.value.pageNo++
  if (logQueryParams.value.pageNo > Math.ceil(logTotal.value / logQueryParams.value.pageSize))
    return
  getLogList()
}

const mentionQueryParams = ref({
  pageNo: 1,
  pageSize: 10,
  userId: userStore.getUser.id,
  status: 0
})

const mentionList = ref<any[]>([])
const mentionTotal = ref(0)

const getMentionList = async () => {
  mentionQueryParams.value.userId = dataPerson.value.personId
  const res = await CommentApi.getMentionPage(mentionQueryParams.value)
  mentionList.value = mentionList.value.concat(res.list)
  mentionTotal.value = res.total
}

const loadMention = () => {
  mentionQueryParams.value.pageNo++
  if (
    mentionQueryParams.value.pageNo >
    Math.ceil(mentionTotal.value / mentionQueryParams.value.pageSize)
  )
    return
  getMentionList()
}

const getTypeName = (type: string) => {
  switch (type) {
    case 'activities':
      return '活动'
    case 'problem':
      return '问题'
    case 'risk':
      return '风险'
    case 'technical':
      return '技术评审'
    default:
      return ''
  }
}

const handleMention = async (item: any) => {
  const res = await BasicsApi.getSimpleBasics(item.basicsId)
  wsCache.set('project_page_show_form', {
    categoryId: res.categoryIds?.[0],
    basicsId: item.basicsId,
    page: item.type,
    stage: item.stage,
    id: item.itemId
  })
  router.push({
    name: 'ProjectCenter'
  })
}

/** 查询任务列表 */
const getTodoList = async () => {
  const data = await TaskApi.getTaskTodoPage({
    pageNo: 1,
    pageSize: -1,
    userId: dataPerson.value.personId
  })
  auditList.value = data.list
}

/** 获取指定日期的总数 */
const getDateCount = (date: string) => {
  return calendarTotalList.value.find((item) => item.date === date)?.total
}
/** 获取日历待办总数 */
const calcCalendarTotalList = async () => {
  const day = dateUtil(currentDate.value)
  const firstDayOfMonth = day.startOf('month') // 当月第一天
  const lastDayOfMonth = day.endOf('month') // 当月最后一天

  // 1. 起始日：当月第一天所在周的周日
  const start = firstDayOfMonth.clone().subtract(firstDayOfMonth.day(), 'day') // day() 返回 0（周一）到 6（周日），减去对应天数到周日

  // 2. 结束日：当月最后一天所在周的周六
  const endDayOfWeek = lastDayOfMonth.day()
  const daysToAdd = (6 - endDayOfWeek + 7) % 7 // 计算到周六需要加的天数
  const end = lastDayOfMonth.clone().add(daysToAdd, 'day')
  const res = await InstrumentApi.getCalendarCountList(
    start.format('YYYY-MM-DD'),
    end.format('YYYY-MM-DD'),
    dataPerson.value.personId
  )
  calendarTotalList.value = res
}
/** 获取指定日期的待办 */
const onCalendarTodoList = async () => {
  const res = await InstrumentApi.getCalendarTodoList(
    dateUtil(currentDate.value).format('YYYY-MM-DD'),
    calendarType.value,
    dataPerson.value.personId
  )
  calendarTodoList.value = res
  filterCalendarTodoList.value = res
}
/** 获取用户列表 */
const onUserList = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}

const onStatusCountList = async () => {
  const res = await InstrumentApi.getStatusCountList(dataPerson.value.personId)
  statusCountList.value = res
}

const getStatusCount = (dictVal: number) => {
  return statusCountList.value.find((item) => item.status == dictVal)?.total || 0
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}

const viewTypeChange = () => {
  if (dataPerson.value.type !== 'my') {
    return
  }
  dataPerson.value.personId = useUserStore().getUser.id as any
  dataPerson.value.personName = useUserStore().getUser.nickname as any
  wsCache.set('workbench_view_type', dataPerson.value)
  calcCalendarTotalList()
  onCalendarTodoList()
  onStatusCountList()
  getTodoList()
  logList.value = []
  getLogList()
  getMentionList()
}

// 展开人员选择器
const expandPersonSelector = () => {
  dataPerson.value.type = 'other'
  // 如果用户列表为空，先加载用户列表
  if (viewUserList.value.length === 0) {
    getViewUserList()
  }
}

const getViewUserList = async () => {
  const res = await InstrumentApi.getViewUserList()

  if (!res || res?.length === 0) {
    viewUserList.value = userList.value.filter((user) => user.id !== 1)
  } else {
    viewUserList.value = userList.value.filter(
      (user) => res.includes(user.id) || supportlibraryList.value.includes(user.id)
    )
  }
}

const userName = ref('')
const treeRef = ref()

/** 基于名字过滤 */
const filterNode = (name: string, data: any) => {
  if (!name) return true
  return data.nickname.includes(name)
}

/** 监听deptName */
watch(userName, (val) => {
  treeRef.value!.filter(val)
})

const onDataPersonChange = (row: any) => {
  const user = userList.value.find((item) => item.id === row.id)
  dataPerson.value.personId = user?.id
  dataPerson.value.personName = user?.nickname
  wsCache.set('workbench_view_type', dataPerson.value)
  calcCalendarTotalList()
  onCalendarTodoList()
  onStatusCountList()
  getTodoList()
  logList.value = []
  getLogList()
  getMentionList()
}

watch(
  () => currentDate.value,
  (newVal, oldVal) => {
    calendarType.value = 'day'
    onCalendarTodoList()
    // 1. 将值转为 dayjs 对象（根据你的 dateUtil 判断是否需要调整）
    const newDate = dateUtil(newVal)
    const oldDate = dateUtil(oldVal)

    // 2. 判断是否跨月（年份或月份不同）
    if (newDate.year() !== oldDate.year() || newDate.month() !== oldDate.month()) {
      calcCalendarTotalList()
    }
  }
)

function selectedRegion() {
  //通过 key 设置某个节点的当前选中状态，使用此方法必须设置 node-key 属性
  const node = document.getElementById(`user-${dataPerson.value.personId!}`) // 通过Id获取到对应的dom元素
  console.log(node)
  setTimeout(() => {
    if (node) {
      nextTick(() => {
        node.scrollIntoView({ block: 'center' }) // 通过scrollIntoView方法将对应的dom元素定位到可见区域 【block: 'center'】这个属性是在垂直方向居中显示
      })
    }
  }, 100)
}

onMounted(async () => {
  const res = wsCache.get('workbench_view_type')
  if (
    userStore.getRoles.some((item) =>
      ['r&d_manager', 'super_admin', 'porject_manager'].includes(item)
    ) &&
    res
  ) {
    dataPerson.value = res
  }
  await onUserList()
  await getSupportLibraryList()
  await getViewUserList()
  selectedRegion()
  calcCalendarTotalList()
  onCalendarTodoList()

  onStatusCountList()
  getTodoList()
  getLogList()
  getMentionList()
})
</script>

<style lang="scss" scoped>
/* 浮动展开按钮 */
.floating-expand-btn {
  position: absolute;
  top: 50px;
  left: 100px;
  transform: translateY(-50%);
  z-index: 1000;

  .expand-button {
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

/* 人员选择面板 */
.person-selector-panel {
  transition: all 0.3s ease;
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

:deep(.el-card__body),
:deep(.el-calendar__header),
:deep(.el-calendar__body) {
  padding: 5px !important;
}
:deep(.el-calendar-table) {
  .el-calendar-day {
    height: 3.2rem;
    line-height: calc(3.2rem - 10px);
    font-size: 1rem;
    padding: 0;
    text-align: center;
  }
  th {
    font-size: 1rem;
    padding: 5px 0;
  }
}
.calendar-list {
  height: 100%;
  padding: 5px;

  .calendar-todo-list {
    height: calc(100% - 30px);
    padding: 10px 5px;
    overflow: auto;

    & > * + * {
      margin-top: 5px;
    }
    .calendar-todo-item {
      background-color: #fff;
      // border-left: 8px solid;
      //border-bottom: 1px solid #f1efef !important;
      //border-top: 1px solid #f1efef !important;

      border-bottom: 0.3px solid #f1f1f1;
      padding: 5px;

      /* 新增阴影效果 */
      // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      & > * {
        padding: 2px;
      }

      :deep(.el-tag) {
        font-size: 1rem;
        height: 24px;
      }
    }

    .calendar-todo-item:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.data-item {
  width: 100%;
  display: flex; /* 启用Flex布局 */
  flex-direction: column; /* 垂直排列子元素 */
  justify-content: space-evenly; /* 垂直均匀分布 */
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  height: 100%;
  border-radius: 10px;
  padding: 5px 0 0 0;

  & > * + * {
    margin-top: 5px;
  }
}
.data-item + .data-item {
  margin-top: 10px;
}
.type-default,
.type-primary {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
.type-danger {
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}
.type-warning {
  color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}
.type-success {
  color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
}

.todo-item {
  display: flex;
  flex-wrap: wrap;
  font-size: 0.85rem;
  color: var(--secondary-text-color);
  cursor: pointer;
  justify-content: flex-start;
  align-items: center;
  background-color: #f8fcff;
  height: 4rem;
  border-radius: 10px;
  padding: 0px 10px;
}
.todo-item:hover {
  background-color: #f9f8ff;
}
.todo-item + .todo-item {
  margin-top: 5px;
}

:deep(.el-timeline-item__content) {
  white-space: pre-wrap;
  font-size: 0.8rem;
  color: var(--regular-text-color);
  background-color: #f8fcff;
  border-radius: 5px;
  padding: 5px;
}
.el-timeline-item__timestamp {
  padding: 3px;
  font-size: 0.8rem;

  .el-timeline-item__user {
    color: var(--el-color-warning);
    font-size: 0.8rem;
  }
}

:deep(.el-link__inner) {
  width: 100%;
  justify-content: flex-start;
}

// :deep(.el-progress-bar__outer) {
//   background-color: #fff !important;
// }

:deep(.el-progress-bar__inner) {
  background-color: var(--el-color-success) !important;
}

:deep(.el-progress__text) {
  color: #888888 !important;
}

:deep(.el-radio__label) {
  font-size: 1rem !important;
}

.tag {
  border-top-left-radius: 5px;
  border-bottom-right-radius: 5px;
  text-align: center;
}

:deep(.dict-tag),
:deep(.el-tag) {
  border-top-right-radius: 5px;
  border-bottom-left-radius: 5px;
  text-align: center;
  position: absolute;
  top: -3px;
  right: -3px;
  font-size: 0.9rem !important;
  width: 3.5rem;
  height: 1.5rem !important;
}

/* 移动端专用样式 */
@media (max-width: 768px) {
  .calendar-list {
    .calendar-todo-list {
      height: auto !important;
      max-height: 400px;

      .calendar-todo-item {
        padding: 8px;
        margin-bottom: 8px;

        & > div {
          padding: 4px 2px;
        }

        .tag {
          font-size: 0.8rem !important;
          width: 2.5rem;
          height: 1.2rem !important;
        }
      }
    }
  }

  .mobile-todo-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    height: auto !important;
    line-height: 1.4 !important;
    padding: 12px !important;
    gap: 4px;

    & > div {
      margin-left: 0 !important;
    }
  }

  .mobile-data-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 80px;
    border-radius: 8px;
    padding: 8px;

    & > * + * {
      margin-top: 4px;
    }
  }

  :deep(.el-calendar-table) {
    .el-calendar-day {
      height: 3rem !important;
      line-height: calc(3rem - 10px) !important;
      font-size: 0.9rem !important;
    }

    th {
      font-size: 0.9rem !important;
      padding: 3px 0 !important;
    }
  }

  :deep(.el-radio-group) {
    .el-radio {
      margin-right: 8px !important;

      .el-radio__label {
        font-size: 0.9rem !important;
      }

      .el-radio__input {
        .el-radio__inner {
          width: 16px !important;
          height: 16px !important;
        }
      }
    }
  }

  :deep(.el-button) {
    min-height: 44px !important; /* 触摸友好的最小高度 */
  }

  :deep(.el-date-picker) {
    .el-input__inner {
      height: 44px !important;
    }
  }

  :deep(.el-timeline-item__content) {
    font-size: 0.75rem !important;
    padding: 8px !important;
    margin-bottom: 8px;
  }

  .el-timeline-item__timestamp {
    font-size: 0.7rem !important;

    .el-timeline-item__user {
      font-size: 0.7rem !important;
    }
  }
}
.mention-item {
  padding: 5px 10px;
  background-color: #f8fcff;
  font-size: 0.85rem !important;
  border-radius: 5px;
  cursor: pointer;
  color: var(--secondary-text-color);
}

.mention-item + .mention-item {
  margin-top: 10px;
}

:deep(.expand-button > span) {
  text-wrap: wrap;
  display: block !important;
}
</style>
