<template>
  <div class="flex h-full">
    <div class="w-[calc(100%-20%)] p-l-10px p-r-10px h-full">
      <div class="h-50% flex w-100% rounded-5px bg-white p-10px">
        <div class="calendar-list w-60%">
          <div class="flex justify-between items-center">
            <CardTitle title="我的事项" />

            <el-radio-group size="small" v-model="listType" @change="listChange">
              <el-radio class="text-1rem" label="所有" value="all" />
              <el-radio value="activities">
                <div class="flex items-center text-1rem">
                  <div class="w-16px h-16px bg-[var(--el-color-primary)] m-2px"></div>
                  <span>活动</span>
                </div>
              </el-radio>
              <el-radio value="problem">
                <el-tooltip
                  content="A类问题是影乡响产品核心功能、安规或用户体验的严重问题，必须立即解决。
                            B类问题对用户体验有一定影响，但不会导致产品完全无法使用，可以在A类问题解决后逐步处理。
                            C类问题对产品核心功能和用户体验影响较小，通常是优化和改进类问题，可以在项目后期或资源充足时处理。"
                >
                  <div class="flex items-center text-1rem">
                    <div class="w-16px h-16px bg-[var(--el-color-danger)] m-2px"></div>
                    <span>问题</span>
                  </div>
                </el-tooltip>
              </el-radio>
              <el-radio value="technical">
                <div class="flex items-center text-1rem">
                  <div class="w-16px h-16px bg-[var(--el-color-warning)] m-2px"></div>
                  <span>预审记录</span>
                </div>
              </el-radio>
            </el-radio-group>

            <el-radio-group v-model="calendarType" class="m-l-10px" @change="onCalendarTodoList">
              <el-radio label="所有" value="all" />
              <el-radio label="按天" value="day" />
            </el-radio-group>
          </div>
          <div class="calendar-todo-list" v-if="filterCalendarTodoList.length > 0">
            <div
              v-for="item in filterCalendarTodoList"
              :key="item.id"
              :class="['calendar-todo-item', 'text-1rem', 'cursor-pointer']"
              @click="
                toBpm({
                  categoryId: item.categoryIds[0],
                  basicsId: item.basicsId,
                  page: item.type,
                  stage: item.stage,
                  id: item.id
                })
              "
            >
              <div class="flex w-100% position-relative !p-l-30px">
                <div
                  :class="[
                    item.type == 'activities' && '!bg-[var(--el-color-primary)]',
                    item.type == 'problem' && '!bg-[var(--el-color-danger)]',
                    item.type == 'technical' && '!bg-[var(--el-color-warning)]',
                    'position-absolute',
                    'left-[-5px]',
                    'top-[-5px]',
                    'text-.9rem',
                    'text-white',
                    'w-2.5rem',
                    'tag'
                  ]"
                >
                  {{
                    item.type == 'activities' ? '活动' : item.type == 'problem' ? '问题' : '预审'
                  }}
                </div>
                <UserAvatarList
                  v-model="item.director"
                  :size="30"
                  :limit="3"
                  :add="false"
                  :user-list="userList"
                  class="w-120px"
                />
                <div class="w-50% whitespace-nowrap overflow-hidden text-ellipsis">
                  <div class="w-100%">{{ item.name }}</div>
                  <div class="w-100% text-.9rem text-#888888">项目：{{ item.basicsName }}</div>
                </div>
                <div class="flex justify-between items-center w-[calc(50%-120px)]">
                  <div>{{ formatDate(item.endDate, 'YYYY-MM-DD') }}</div>
                </div>
                <DictTag size="small" type="project_activities_status" :value="item.status" />
              </div>
              <el-progress :percentage="item.progress" class="w-100%" />
            </div>
          </div>
          <el-empty v-else description="当前选择日期暂无事项" />
        </div>
        <el-calendar ref="calendar" class="h-100% w-40% rounded-5px" v-model="currentDate">
          <template #header>
            <CardTitle title="日历" />
            <span>
              <el-date-picker v-model="currentDate" type="month" :clearable="false" class="!w-100px" size="small" />
            </span>
          </template>
          <template #date-cell="{ data }">
            <div class="position-relative">
              <el-badge
                :value="getDateCount(data.day)"
                class="w-full"
                :show-zero="false"
                :offset="[-4, 8]"
              >
                <div :class="[data.isSelected && 'is-selected', 'p-t-5px', 'p-b-5px', 'w-36px']">
                  {{ data.day.split('-').slice(2).join('-') }}
                </div>
              </el-badge>
            </div>
          </template>
        </el-calendar>
      </div>
      <div class="h-[calc(50%-10px)] m-t-10px flex">
        <div
          class="bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] w-50% p-10px"
        >
          <div class="h-40px">
            <CardTitle title="我的流程待办" />
          </div>
          <div class="h-[calc(100%-60px)] overflow-auto" v-if="auditList.length > 0">
            <div
              v-for="item in auditList"
              :key="item.id"
              class="todo-item"
              @click="handleAudit(item)"
            >
              <div class="w-10px h-10px rounded-50% bg-[var(--el-color-primary)]"></div>
              <el-tooltip
                :content="item.formVariables?.['approve_title'] || item.processInstance.name"
              >
                <div class="w-40% whitespace-nowrap overflow-hidden text-ellipsis">
                  {{ item.formVariables?.['approve_title'] || item.processInstance.name }}
                </div>
              </el-tooltip>
              <div class="w-30% ml-5px"
                >提交人:{{ item?.processInstance?.startUser?.nickname }}</div
              >
              <div class="w-[calc(30%-10px)] overflow-hidden whitespace-nowrap text-ellipsis">
                {{ formatDate(item.createTime) }}
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无待办" />
        </div>
        <div
          class="bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] w-[calc(50%-10px)] p-10px m-l-10px"
        >
          <div class="h-40px">
            <CardTitle title="我的操作记录" />
          </div>
          <div
            class="h-[calc(100%-40px)] overflow-auto"
            v-infinite-scroll="loadLog"
            v-if="logList.length > 0"
          >
            <el-timeline>
              <el-timeline-item v-for="item in logList" :key="item.id">
                <div class="el-timeline-item__timestamp">
                  {{ formatDate(item.createTime) }}-<span class="el-timeline-item__user">{{
                    item.createName
                  }}</span>
                </div>
                {{ item.content }}
                <div v-if="item.modifyFieldName">
                  <template v-if="shouldShowDictTag(item.modifyField)">
                    字段:{{ item.modifyFieldName }} 从:{{
                      formatDictLabel(item.modifyField, item.beforeValue)
                    }}
                    修改为:{{ formatDictLabel(item.modifyField, item.afterValue) }}
                  </template>
                  <template
                    v-else-if="
                      ['managers', 'userIds', 'director', 'coordinate'].includes(
                        item.modifyField
                      ) ||
                      getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                        (dict) => dict.value === item.modifyField
                      )?.label
                    "
                  >
                    字段:{{ item.modifyFieldName }} 从:{{
                      getUserNickName(userList, item.beforeValue)
                    }}
                    修改为:{{ getUserNickName(userList, item.afterValue) }}
                  </template>
                  <template v-else>
                    字段:{{ item.modifyFieldName }} 从:{{ item.beforeValue }} 修改为:{{
                      item.afterValue
                    }}
                  </template>
                </div>
                <div v-if="item.processInstanceId">
                  <el-link type="primary" @click="toBpm(item.processInstanceId)">跳转流程</el-link>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
          <el-empty v-else description="当前无操作记录" />
        </div>
      </div>
    </div>
    <div class="w-20%">
      <ContentWrap>
        <div class="flex items-center">
          <el-avatar :src="avatar" :size="50" class="mr-16px">
            <img src="@/assets/imgs/avatar.gif" alt="" />
          </el-avatar>
          <div>
            <div>{{ username }}</div>
            <div class="text-1rem color-[var(--el-color-info)]"> 欢迎回来！ </div>
          </div>
        </div>
      </ContentWrap>
      <div
        class="h-[calc(100%-78px)] bg-white rounded-5px border-1px border-solid border-[var(--el-border-color)] p-5px w-full"
      >
        <div class="p-10px">
          <CardTitle title="个人数据" />
        </div>
        <div class="bg-white h-[calc(100%-40px)] w-full">
          <div class="p-10px h-20% w-full">
            <div
              v-for="dict in getIntDictOptions('project_activities_status')"
              :key="dict.value"
              :class="['data-item', `type-${dict.colorType || 'default'}`]"
              v-show="dict.label !== '暂停'"
            >
              <div>{{ dict.label }}</div>
              <div>{{ getStatusCount(dict.value) }}</div>
              <div><Icon :icon="getIcon(dict.value)" class="!text-1.8rem" /></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { dateUtil } from '@/utils/dateUtil'
import { InstrumentApi } from '@/api/project/instrument'
import { formatDate } from '@/utils/formatTime'
import { getSimpleUserList } from '@/api/system/user'
import { useCache } from '@/hooks/web/useCache'
import { getIntDictOptions } from '@/utils/dict'
import * as TaskApi from '@/api/bpm/task'
import {
  shouldShowDictTag,
  getUserNickName,
  formatDictLabel
} from '@/views/project/ProjectCenter/details/components/utils'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { BasicsApi } from '@/api/project/basics'

const userStore = useUserStore()
const avatar = userStore.getUser.avatar
const username = userStore.getUser.nickname
const userList = ref<any[]>([])
const router = useRouter()
const { wsCache } = useCache()

const calendar = ref()
const currentDate = ref<Date>(new Date())
const calendarType = ref('all')
const listType = ref('all')
const calendarTotalList = ref<any[]>([])
const calendarTodoList = ref<any[]>([])
const filterCalendarTodoList = ref<any[]>([])
const statusCountList = ref<any[]>([])
const auditList = ref<any[]>([])
const logList = ref<any[]>([])
const logTotal = ref(0)
const logQueryParams = ref({
  pageNo: 1,
  pageSize: 10,
  creator: userStore.getUser.id
})

const listChange = () => {
  if (listType.value === 'all') {
    filterCalendarTodoList.value = calendarTodoList.value
  } else {
    filterCalendarTodoList.value = calendarTodoList.value.filter(
      (item) => item.type === listType.value
    )
  }
}

const toBpm = (query) => {
  wsCache.set('project_page_show_form', query)
  router.push({
    name: 'ProjectCenter'
  })
}

const getIcon = (status: number) => {
  switch (status) {
    case 1:
      return 'fa-solid:running'
    case 2:
      return 'ep:warning-filled'
    case 3:
      return 'ep:success-filled'
    case 4:
      return 'fa:compass'
    case 10:
      return 'fa:pencil-square'
    case 11:
      return 'fa:calendar-times-o'
  }
}

const getLogList = async () => {
  const res = await BasicsApi.getBasicsLogPage(logQueryParams.value)
  logList.value = logList.value.concat(res.list)
  logTotal.value = res.total
}

const loadLog = () => {
  logQueryParams.value.pageNo++
  if (logQueryParams.value.pageNo > Math.ceil(logTotal.value / logQueryParams.value.pageSize))
    return
  getLogList()
}

/** 查询任务列表 */
const getTodoList = async () => {
  const data = await TaskApi.getTaskTodoPage({ pageNo: 1, pageSize: -1 })
  auditList.value = data.list
}

/** 获取指定日期的总数 */
const getDateCount = (date: string) => {
  return calendarTotalList.value.find((item) => item.date === date)?.total
}
/** 获取日历待办总数 */
const calcCalendarTotalList = async () => {
  const day = dateUtil(currentDate.value)
  const firstDayOfMonth = day.startOf('month') // 当月第一天
  const lastDayOfMonth = day.endOf('month') // 当月最后一天

  // 1. 起始日：当月第一天所在周的周日
  const start = firstDayOfMonth.clone().subtract(firstDayOfMonth.day(), 'day') // day() 返回 0（周一）到 6（周日），减去对应天数到周日

  // 2. 结束日：当月最后一天所在周的周六
  const endDayOfWeek = lastDayOfMonth.day()
  const daysToAdd = (6 - endDayOfWeek + 7) % 7 // 计算到周六需要加的天数
  const end = lastDayOfMonth.clone().add(daysToAdd, 'day')
  const res = await InstrumentApi.getCalendarCountList(
    start.format('YYYY-MM-DD'),
    end.format('YYYY-MM-DD')
  )
  calendarTotalList.value = res
}
/** 获取指定日期的待办 */
const onCalendarTodoList = async () => {
  const res = await InstrumentApi.getCalendarTodoList(
    dateUtil(currentDate.value).format('YYYY-MM-DD'),
    calendarType.value
  )
  calendarTodoList.value = res
  filterCalendarTodoList.value = res
}
/** 获取用户列表 */
const onUserList = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}

const onStatusCountList = async () => {
  const res = await InstrumentApi.getStatusCountList()
  statusCountList.value = res
}

const getStatusCount = (dictVal: number) => {
  return statusCountList.value.find((item) => item.status == dictVal)?.total || 0
}

/** 处理审批按钮 */
const handleAudit = (row: any) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: row.processInstance.id
    }
  })
}

watch(
  () => currentDate.value,
  (newVal, oldVal) => {
    calendarType.value = 'day'
    onCalendarTodoList()
    // 1. 将值转为 dayjs 对象（根据你的 dateUtil 判断是否需要调整）
    const newDate = dateUtil(newVal)
    const oldDate = dateUtil(oldVal)

    // 2. 判断是否跨月（年份或月份不同）
    if (newDate.year() !== oldDate.year() || newDate.month() !== oldDate.month()) {
      calcCalendarTotalList()
    }
  }
)
onMounted(() => {
  calcCalendarTotalList()
  onCalendarTodoList()
  onUserList()
  onStatusCountList()
  getTodoList()
  getLogList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body),
:deep(.el-calendar__header),
:deep(.el-calendar__body) {
  padding: 5px !important;
}
:deep(.el-calendar-table) {
  .el-calendar-day {
    height: 4rem;
    line-height: calc(4rem - 10px);
    font-size: 1rem;
    padding: 0;
    text-align: center;
  }
  th {
    font-size: 1rem;
    padding: 5px 0;
  }
}
.calendar-list {
  height: 100%;
  width: calc(100% - 10px);
  padding: 5px;

  .calendar-todo-list {
    height: calc(100% - 30px);
    padding: 10px 5px;
    overflow: auto;

    & > * + * {
      margin-top: 10px;
    }
    .calendar-todo-item {
      background-color: #fff;
      // border-left: 8px solid;
      //border-bottom: 1px solid #f1efef !important;
      //border-top: 1px solid #f1efef !important;

      border: 0.3px solid #f1f1f1;
      padding: 5px;
      border-radius: 5px;

      /* 新增阴影效果 */
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      & > * {
        padding: 2px;
      }

      :deep(.el-tag) {
        font-size: 1rem;
        height: 24px;
      }
    }

    .calendar-todo-item:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.data-item {
  width: 100%;
  display: flex; /* 启用Flex布局 */
  flex-direction: column; /* 垂直排列子元素 */
  justify-content: space-evenly; /* 垂直均匀分布 */
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  height: 100%;
  border-radius: 10px;
  padding: 5px 0 0 0;

  & > * + * {
    margin-top: 5px;
  }
}
.data-item + .data-item {
  margin-top: 10px;
}
.type-default,
.type-primary {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
.type-danger {
  color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}
.type-warning {
  color: var(--el-color-warning);
  background-color: var(--el-color-warning-light-9);
}
.type-success {
  color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
}

.todo-item {
  display: flex;
  font-size: 1rem;
  color: var(--secondary-text-color);
  cursor: pointer;
  justify-content: space-around;
  align-items: center;
  background-color: #f8fcff;
  height: 4rem;
  line-height: 4rem;
  border-radius: 10px;
}
.todo-item:hover {
  background-color: #f9f8ff;
}
.todo-item + .todo-item {
  margin-top: 5px;
}

:deep(.el-timeline-item__content) {
  white-space: pre-wrap;
  font-size: 0.8rem;
  color: var(--regular-text-color);
  background-color: #f8fcff;
  border-radius: 5px;
  padding: 5px;
}
.el-timeline-item__timestamp {
  padding: 3px;
  font-size: 0.8rem;

  .el-timeline-item__user {
    color: var(--el-color-warning);
    font-size: 0.8rem;
  }
}

:deep(.el-link__inner) {
  width: 100%;
  justify-content: flex-start;
}

// :deep(.el-progress-bar__outer) {
//   background-color: #fff !important;
// }

:deep(.el-progress-bar__inner) {
  background-color: var(--el-color-success) !important;
}

:deep(.el-progress__text) {
  color: #888888 !important;
}

:deep(.el-radio__label) {
  font-size: 1rem !important;
}

.tag {
  border-top-left-radius: 5px;
  border-bottom-right-radius: 5px;
  text-align: center;
}

:deep(.dict-tag),
:deep(.el-tag) {
  border-top-right-radius: 5px;
  border-bottom-left-radius: 5px;
  text-align: center;
  position: absolute;
  top: -3px;
  right: -3px;
  font-size: 0.9rem !important;
  width: 3.5rem;
  height: 1.5rem !important;
}
</style>
