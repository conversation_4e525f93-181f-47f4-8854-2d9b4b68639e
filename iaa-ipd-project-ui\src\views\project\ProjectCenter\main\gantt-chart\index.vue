<template>
  <ActivitiesContainer :basics-info="props.basicsInfo!" :node-list="[]">
    <template #activities-buttons>
      <!-- <el-button>导出到excel</el-button> -->
    </template>
    <template #activities-table>
      <el-empty v-if="activities.length==0" description="当前未分解活动" />
      <div ref="ganttRef" class="gantt-container" v-show="activities.length>0"></div>
      
      <ActivitiesForm
        ref="activitiesFormRef"
        :activities-list="activities"
        :file-template-list="fileTemplateList"
        :user-list="userList"
      />
    </template>
  </ActivitiesContainer>
</template>

<script lang="ts" setup>
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import ActivitiesForm from '../activities/ActivitiesForm.vue'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { BasicsVO } from '@/api/project/basics'
import gantt from './grantt-config'
import { ActivitiesApi } from '@/api/project/activities'
import { dateUtil } from '@/utils/dateUtil'
import { FileTemplateApi } from '@/api/project/file/template'

const userList = ref<UserVO[]>([])

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  }
})
const activitiesFormRef = ref()
const ganttRef = ref()
const loading = ref(false)
const queryParams = ref({
  basicsId: props.basicsInfo?.id
})
const activities = ref<any[]>([])
const fileTemplateList = ref<any[]>([])

const emits = defineEmits(['click:activities'])

// 添加任务单击事件
gantt.attachEvent('onTaskDblClick', function (id, _e) {
  //any custom logic here
  showRow(activities.value.find((item) => item.id === Number(id)))
  return true
})

/** 初始化甘特图 */
const listActivities = async () => {
  loading.value = true
  try {
    queryParams.value.basicsId = props.basicsInfo?.id
    const res = await ActivitiesApi.getActivitiesList(queryParams.value)
    activities.value = res
    gantt.clearAll()
    if(!activities.value||activities.value.length==0){
      return;
    }
    let task = {
      data: [] as any[],
      links: [] as any[]
    }
    for (let item of res) {
      task.data.push({
        id: item.id,
        text: item.name,
        orderNo: item.orderNo,
        start_date: item.startDate,
        startDate: item.startDate,
        endDate: item.endDate,
        duration: dateUtil(item.endDate).diff(dateUtil(item.startDate), 'day') + 1,
        progress: item.progress / 100,
        level: item.level,
        parentId: item.parentId,
        status: item.status
      })
      item.dependencies?.forEach((dependency: any) => {
        task.links.push({
          id: dependency.id,
          source: dependency.id,
          target: item.id,
          type: dependency.dependencyType == 'fs' ? 0 : 2
        })
      })
    }
    
    gantt.init(ganttRef.value) //这里可以通过ref挂载 或者 id选择器挂载
    gantt.parse(task)
    const dateToStr = gantt.date.date_to_str(gantt.config.task_date)
    gantt.addMarker({
      start_date: new Date(),
      css: 'today',
      text: '今天',
      title: dateToStr(new Date())
    })
    gantt.config.show_markers = true
    gantt.showDate(new Date())
    await nextTick()
    resize()
  } finally {
    loading.value = false
  }
}

const listFileTemplate = async () => {
  const res = await FileTemplateApi.getFileTemplatePage({})
  fileTemplateList.value = res
}

// 生成随机颜色的函数
// const getRandomColor = () => {
//   // 生成一个较暗的颜色
//   const randomChannel = () => Math.floor(Math.random() * 128 + 100)
//   const r = randomChannel()
//   const g = randomChannel()
//   const b = randomChannel()
//   const a = 0.5 // 50% 透明度

//   return `rgba(${r}, ${g}, ${b}, ${a})`
// }

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

/** 显示单行详情 */
const showRow = async (row: any) => {
  unref(activitiesFormRef)?.openForm(row)
}

watch(
  () => [props.basicsInfo],
  () => {
    if (props.basicsInfo?.id) {
      listActivities()
    }
  },
  { immediate: true }
)

// 声明全局变量
let containerWidth = 0 // 父容器宽度（初始化时获取）
let isResizing = false
let startX = 0
let initialWidth = 0
let targetElement: HTMLElement | null = null

// 初始化函数
const resize = async () => {
  // 获取布局容器
  const firstElement = document.querySelector('.grid_cell')
  const parentContainer = firstElement!.parentElement!
  containerWidth = parentContainer.offsetWidth // 初始化时获取容器宽度

  // 绑定事件
  const elements = document.querySelectorAll('.grid_cell')
  elements.forEach((element: HTMLElement) => {
    element.style.cursor = 'ew-resize'
    element.addEventListener('mousedown', (e: any) => {
      const rect = element.getBoundingClientRect()
      if (e.pageX - rect.left > rect.width - 5) {
        startResize(e, element)
        setupResize()
      }
    })
  })
}

// 调整开始
const startResize = (e: MouseEvent, element: HTMLElement) => {
  isResizing = true
  targetElement = element
  startX = e.pageX
  initialWidth = element.offsetWidth
}

// 实时调整
const doResize = (e: MouseEvent) => {
  if (!isResizing || !targetElement) return
  const dx = e.pageX - startX
  const newGridWidth = initialWidth + dx

  // 计算对应宽度
  const timelineWidth = containerWidth - newGridWidth

  // 设置当前元素宽度
  targetElement.style.width = `${newGridWidth}px`

  // 更新所有timeline_cell宽度
  const timelineElements = document.querySelectorAll('.timeline_cell')
  timelineElements.forEach((element:HTMLElement) => {
    element.style.width = `${timelineWidth}px`
  })
}

// 其他函数保持不变...

const stopResize = () => {
  isResizing = false
  targetElement = null
  startX = 0
  initialWidth = 0
}
// 修改为单例监听模式（推荐）
let listenersAdded = false;
const setupResize = () => {
  if (!listenersAdded) {
    document.addEventListener('mousemove', doResize);
    document.addEventListener('mouseup', stopResize);
    listenersAdded = true;
  }
};

// 在 onBeforeUnmount 中补充清理逻辑：
onBeforeUnmount(() => {
  if (listenersAdded) {
    document.removeEventListener('mousemove', doResize);
    document.removeEventListener('mouseup', stopResize);
    listenersAdded = false;
  }
});

onMounted(() => {
  getUserList()
  listFileTemplate()
})
</script>

<style lang="scss" scoped>
.gantt-container {
  height: 92%;
}

:deep(.gantt_scale_cell),
:deep(.gantt_grid_scale) {
  background-color: #f9f9f9;
  color: var(--primary-text-color);
  font-size: 1rem;
  border-bottom: 0.3px solid var(--placeholder-text-color);
}

:deep(.gantt_scale_cell) {
  border-right: 0.3px solid var(--placeholder-text-color);
}

:deep(.gantt_grid) {
  border-right: 0.3px solid var(--primary-text-color);
}

:deep(.gantt-name-box),
:deep(.gantt_tree_content) {
  font-size: 1rem;
}

:deep(.tag-primary),
:deep(.tag-danger),
:deep(.tag-warning),
:deep(.tag-success),
:deep(.tag-default),
:deep(.tag-info) {
  background-color: var(--el-color-primary);
  padding: 5px;
  border-radius: 5px;
  color: #fff;
}

:deep(.tag-danger) {
  background-color: var(--el-color-danger) !important;
}

:deep(.tag-warning) {
  background-color: var(--el-color-warning) !important;
}

:deep(.tag-success) {
  background-color: var(--el-color-success) !important;
}

:deep(.tag-info) {
  background-color: var(--el-color-info) !important;
}

:deep(.gantt_marker .gantt_marker_content) {
  position: sticky;
  top: 0;
  width: 30px;
  opacity: 0.6;
}

:deep(.gantt_bar_task) {
  background-color: var(--el-color-primary-light-7);
}

:deep(.gantt_task_progress) {
  background-color: var(--el-color-primary) !important;
  opacity: 1 !important;
}
</style>
