<template>
  <ActivitiesContainer :basicsInfo="props.basicsInfo!" :nodeList="[]">
    <template #activities-table>
      <div class="flex h-full w-full p-t-10px">
        <div class="tr-container">
          <el-badge
            v-for="node in templateList"
            :key="node.id"
            :value="stageCount[node.id] || 0"
            type="primary"
            class="!w-full"
            :offset="[-10, 8]"
          >
            <div
              :class="['tr-item', currentNode === node.id ? 'active' : '']"
              @click="onNodeChange(node.id)"
            >
              <div class="tr-item-title">{{ node.alias }}</div>
              <div class="tr-item-content">
                <template v-if="nodeDateObj[node.id]">
                  计划完成时间：{{ nodeDateObj[node.id] }}
                </template>
              </div>
            </div>
          </el-badge>
        </div>
        <div class="w-87% h-97% bg-#ebeff3 overflow-auto">
          <template v-if="currentNode">
            <div class="header text-center">
              <el-radio-group size="small" v-model="currentPage" @change="onTabChange">
                <el-radio-button label="评审团" value="team" />
                <el-radio-button label="评审材料" value="file" />
                <el-radio-button label="预审记录" value="table" />
                <el-radio-button label="评审报告" value="record" />
              </el-radio-group>
            </div>
            <template v-if="currentPage == 'team' && currentActivities">
              <div class="w-full">
                <el-card shadow="never">
                  <template #header>
                    <CardTitle title="小组长" />
                  </template>
                  <UserAvatarList
                    v-model="currentActivities.director!"
                    :userList="userList"
                    :limit="10"
                    :size="36"
                    :add="false"
                  />
                </el-card>
                <el-card shadow="never" class="m-t-10px">
                  <template #header>
                    <CardTitle title="评审团队" />
                  </template>
                  <UserAvatarList
                    v-model="additional.teamIds!"
                    :userList="userList"
                    :limit="10"
                    :size="36"
                    :add="currentActivities.director?.includes(useUserStore().getUser.id)"
                    @change:msg="additionalChange"
                  />
                </el-card>
                <el-card shadow="never" class="m-t-10px">
                  <template #header>
                    <CardTitle title="外部专家团" />
                  </template>
                  <UserAvatarList
                    v-model="additional.externalTeamIds!"
                    :userList="userList"
                    :limit="10"
                    :size="36"
                    :add="currentActivities.director?.includes(useUserStore().getUser.id)"
                    @change:msg="additionalChange"
                  />
                </el-card>
              </div>
            </template>
            <template v-else-if="currentPage == 'file' && currentActivities">
              <div class="file-list">
                <div
                  class="file-item"
                  v-for="file in attachmentList"
                  :key="file.id"
                  @click="attachmentPreviewRef?.openForm(file.name, file.processInstanceId)"
                >
                  <img :src="getImagePath(file.name, false)" class="w-77px h-77px" />
                  <div class="file-name">
                    {{ file.name }}
                  </div>
                </div>
              </div>
            </template>
            <template
              v-else-if="
                currentPage == 'table' && (currentActivities || stageCount[currentNode] > 0)
              "
            >
              <TechnicalTable
                :userList="userList"
                :basicsId="props.basicsInfo!.id"
                :nodeId="currentNode!"
                :factorList="factorList"
              />
            </template>
            <template v-else-if="currentPage == 'record' && currentActivities">
              <div
                style="
                  height: calc(100% - 1.6vw - 10px);
                  width: 100%;
                  background-color: white;
                  padding: 10px;
                "
              >
                <div class="w-full position-sticky top-0">
                  <FileUpload
                    ref="fileUploadRef"
                    category="additional"
                    :dynamic-id="additional.id!"
                    @success="onListAttachment"
                    v-if="
                      currentActivities.director?.includes(useUserStore().getUser.id) ||
                      additional.teamIds?.includes(useUserStore().getUser.id)
                    "
                    :disabled="
                      !(
                        getRolePermission('pqa', useUserStore().getUser.id) ||
                        useUserStore().getUser.id == 1
                      )
                    "
                  />
                </div>
                <vxe-table
                  class="w-100%"
                  :header-cell-style="{ padding: '5px' }"
                  :cell-style="{ padding: '5px', height: '30px' }"
                  show-overflow
                  :data="attachmentList"
                  align="center"
                  border
                >
                  <vxe-column title="文件名" field="name" min-width="200" align="left">
                    <template #default="{ row }">
                      <el-link
                        type="primary"
                        @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
                      >
                        {{ row.name }}
                      </el-link>
                    </template>
                  </vxe-column>
                  <vxe-column title="版本" field="currentVersion" width="60" />
                  <vxe-column title="审签状态" field="approvalStatus" width="90">
                    <template #default="{ row }">
                      <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
                    </template>
                  </vxe-column>
                  <vxe-column
                    title="审核通过时间"
                    field="approvalTime"
                    :formatter="dateFormatter3"
                    width="120"
                  />
                  <vxe-column title="操作" width="100px" fixed="right" align="center">
                    <template #default="{ row }">
                      <el-button
                        type="primary"
                        link
                        size="small"
                        v-if="![1, 0].includes(row.approvalStatus)"
                        @click="fileUploadAnewRef?.openForm(row)"
                      >
                        重传
                      </el-button>
                      <el-button
                        type="primary"
                        link
                        size="small"
                        v-if="row.approvalStatus != 1"
                        @click="delAttachment(row)"
                      >
                        删除
                      </el-button>
                    </template>
                  </vxe-column>
                </vxe-table>
              </div>
            </template>
            <el-empty
              v-else-if="!currentActivities"
              description="当前项目未分解当前评审活动，无需评审"
            />
          </template>
          <el-empty v-else-if="!currentNode" description="请选择左侧评审节点" class="bg-white" />
        </div>
      </div>
    </template>
  </ActivitiesContainer>
  <AttachmentPreview ref="attachmentPreviewRef" />
  <FileUploadAnew
    category="additional"
    :dynamic-id="additional.id!"
    ref="fileUploadAnewRef"
    @success="onListAttachment"
  />
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { debounce } from 'lodash-es'
import { propTypes } from '@/utils/propTypes'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import { AttachmentApi } from '@/api/project/attachment'
import { getImagePath } from '@/utils/icon'
import AttachmentPreview from '../components/AttachmentPreview.vue'
import TechnicalTable from './TechnicalTable.vue'
import { TechnicalFactorApi, TechnicalFactorVO } from '@/api/project/technicalfactor'
import { TechnicalApi, TechnicalAdditionalVO } from '@/api/project/technical'
import FileUploadAnew from '../components/FileUploadAnew.vue'
import FileUpload from '../components/FileUpload.vue'
import { dateFormatter3 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { useCache } from '@/hooks/web/useCache'
import { useUserStore } from '@/store/modules/user'
import { getRolePermission } from '../util/permission'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  },
  templateCategory: {
    type: String,
    default: '10'
  },
  activities: propTypes.oneOfType<any[]>([]).isRequired
})
const templateList = ref<any[]>([])
const currentNode = ref<number | undefined>(undefined)
const currentPage = ref('table')
const currentActivities = computed(() => {
  return props.activities?.find((item) => item.templateId == currentNode.value)
})
// 在 script setup 中定义转换后的对象
const nodeDateObj = computed(() => {
  return (props.activities as any[])?.reduce(
    (acc, item) => {
      acc[item.templateId] = item.endDate
      return acc
    },
    {} as Record<number, string>
  )
})
const userList = ref<UserVO[]>([])
const attachmentList = ref<any[]>([])
const message = useMessage()
const attachmentPreviewRef = ref()
const factorList = ref<TechnicalFactorVO[]>([])
const additional = ref<TechnicalAdditionalVO>({})
const fileUploadAnewRef = ref()
const { wsCache } = useCache()
const stageCount = ref<any>({})

const getStageCount = async () => {
  const res = await TechnicalApi.getStageCount(props.basicsInfo!.id!)
  stageCount.value = res
}

const onTabChange = async () => {
  await nextTick()
  switch (currentPage.value) {
    case 'team':
      getAdditional()
      break
    case 'file':
      getTechnicalAttachment()
      break
  }
}

const onNodeChange = async (nodeId: number) => {
  currentNode.value = nodeId
  getAdditional()
  const group = templateList.value.findIndex((node) => node.id === currentNode.value)
  const res = await TechnicalFactorApi.getTechnicalFactorListByGroup(group)
  factorList.value = res
}

/** 删除附件 */
const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'additional',
    dynamicId: additional.value.id!
  })
  console.log(res)
  attachmentList.value = res
}

/** 修改评审附加信息 */
const additionalChange = async () => {
  additional.value.basicsId = props.basicsInfo?.id
  additional.value.nodeId = currentNode.value
  await TechnicalApi.updateAdditional(additional.value)
  getAdditional()
  message.success('成功')
}
/** 获取评审附加信息 */
const getAdditional = async () => {
  const res = await TechnicalApi.getAdditional({
    basicsId: props.basicsInfo?.id,
    nodeId: currentNode.value
  })
  additional.value = res
  onListAttachment()
}

/** 获取技术评审的所有文件 */
const getTechnicalAttachment = async () => {
  if (!currentActivities.value) {
    message.warning('当前项目未分解当前评审活动，无需评审')
    return
  }
  const res = await AttachmentApi.getAttachmentByDependencies(currentActivities.value.id)
  attachmentList.value = res
}

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

const getTemplateList = debounce(async () => {
  const res = await ActivitiesTemplateApi.listActivitiesTechnical(
    props.templateCategory,
    props.basicsInfo?.id
  )
  templateList.value = res
  if (templateList.value.length > 0) {
    const form = wsCache.get('project_page_show_form')
    if (form) {
      onNodeChange(Number(form.stage))
    } else {
      onNodeChange(templateList.value[0].id)
    }
  }
}, 100)

watch(
  () => [props.basicsInfo?.id, props.templateCategory],
  () => {
    if (props.basicsInfo?.id && props.templateCategory) {
      currentPage.value = 'table'
      getTemplateList()
      getStageCount()
    }
  },
  { immediate: true }
)

onMounted(() => {
  getUserList()
})
</script>

<style lang="scss" scoped>
.tr-container {
  padding: 5px;
  height: 98%;
  width: 15%;
  border-right: 1px solid var(--el-border-color-light);

  .tr-item {
    height: 70px;
    width: 100%;
    text-align: center;
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    border-bottom: 1px solid var(--el-border-color-light);
    .tr-item-title {
      height: 50px;
      line-height: 50px;
      font-size: 1rem;
      font-weight: bold;
      color: var(--regular-text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .tr-item-content {
      height: 20px;
      line-height: 20px;
      font-size: 0.7vw;
      color: var(--placeholder-text-color);
    }

    &:hover,
    &.active {
      background-color: var(--el-color-primary-light-9) !important;

      & > .tr-item-title {
        color: var(--el-color-primary) !important;
      }
    }
  }
}

.header {
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 2px;
  border-bottom: 1px solid var(--el-border-color-light);
  :deep(.el-radio-button__inner) {
    border: 1px solid #f1f1f1 !important;
    // background-color: #ebeff3;
    font-size: 1rem;
    padding: 5px 5px;

    i {
      font-size: 1rem !important;

      .el-tooltip__trigger {
        font-size: 1rem !important;
      }
    }
  }
}
.file-list {
  padding: 20px;
  padding-bottom: 3px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  overflow-x: hidden;
  align-content: flex-start;
  justify-content: flex-start;
  flex-direction: row;
  background-color: white;
  min-height: 716px;
}

.file-item {
  width: 81px;
  height: 146px;
  text-align: center;
  margin-left: 5px;
  cursor: pointer;

  .file-name {
    font-size: 12px;
    color: #999999;
  }
}

.file-item:hover,
.file-item.is-active {
  background-color: var(--el-color-primary-light-9);
  border-radius: 2px;
}
</style>
