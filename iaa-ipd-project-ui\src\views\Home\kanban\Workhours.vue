<template>
  <div class="p-l-10px h-full overflow-auto" ref="containerRef">
    <!-- ADCP 按期达成 -->
    <el-card shadow="never">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="adcpToolbarRef">
          <template #buttons>
            <CardTitle title="ADCP按期达成情况" />
            <div class="over-tip">
              <span>
                统计公式：ADCP按期达成率=周期内实际按期达成ADCP个数÷周期内计划达成ADCP个数x100%
                备注：年度与月度均体现ADCP按期达成的情况，月度未按期完成的ADCP不会计入下一个月度的数据统计中。
              </span>
            </div>
          </template>
          <template #tools>
            <el-date-picker
              v-model="adcpDate"
              value-format="YYYY"
              type="year"
              @change="getAdcpAchieved"
              :clearable="false"
            />
          </template>
        </vxe-toolbar>
      </template>
      <div v-loading="adcpLoading">
        <vxe-table
          ref="adcpTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem', background: '#eee' }"
          :cell-style="{ padding: '5px', fontSize: '.8rem' }"
          :cell-config="{ height: 24 }"
          :header-cell-config="{ height: 24 }"
          :footer-cell-config="{ height: 24 }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="adcpList"
          :export-config="{ type: 'xlsx', isMerge: true }"
          align="center"
          height="258px"
          :footerMethod="footerMethodSum"
          show-footer
          border
          stripe
        >
          <vxe-column field="manager" title="项目经理" width="120" fixed="left" />
          <vxe-colgroup field="total" title="合计" width="60" fixed="left">
            <vxe-column
              :field="`planCount-annual`"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 'annual', 'planCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'ADCP',
                      dateType: 'month',
                      year: Number(adcpDate),
                      serialNumber: 100,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data['annual']?.planCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              :field="`actualCount-annual`"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 'annual', 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'ADCP',
                      dateType: 'month',
                      year: Number(adcpDate),
                      serialNumber: 100,
                      manager: row.manager,
                      completed: true
                    })
                  "
                >
                  {{ row.data['annual']?.actualCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              :field="`rate-annual`"
              title="达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 'annual', 'rate')"
            >
              <template #default="{ row }">
                <el-progress
                  :text-inside="true"
                  :stroke-width="18"
                  :percentage="row.data['annual']?.rate"
                  status="success"
                  class="no-radius"
                />
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup field="total" title="当期数据" width="60">
            <vxe-column
              :field="`planCount-99`"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, '99', 'planCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'ADCP',
                      dateType: 'month',
                      year: Number(adcpDate),
                      serialNumber: 99,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data['99']?.planCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              :field="`actualCount-99`"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, '99', 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'ADCP',
                      dateType: 'month',
                      year: Number(adcpDate),
                      serialNumber: 99,
                      manager: row.manager,
                      completed: true
                    })
                  "
                >
                  {{ row.data['99']?.actualCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              :field="`rate-99`"
              title="达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, '99', 'rate')"
            >
              <template #default="{ row }">
                <el-progress
                  :text-inside="true"
                  :stroke-width="18"
                  :percentage="row.data['99']?.rate"
                  status="success"
                  class="no-radius"
                />
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup
            v-for="month in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
            :key="month"
            :field="`${month}-data`"
            :title="`${month}月`"
          >
            <vxe-column
              :field="`planCount-${month}`"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'planCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'ADCP',
                      dateType: 'month',
                      year: Number(adcpDate),
                      serialNumber: month,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data[month]?.planCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              :field="`actualCount-${month}`"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'ADCP',
                      dateType: 'month',
                      year: Number(adcpDate),
                      serialNumber: month,
                      manager: row.manager,
                      completed: true
                    })
                  "
                >
                  {{ row.data[month]?.actualCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              :field="`rate-${month}`"
              title="达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'rate')"
            >
              <template #default="{ row }">
                <el-progress
                  :text-inside="true"
                  :stroke-width="18"
                  :percentage="row.data[month]?.rate"
                  status="success"
                  class="no-radius"
                />
              </template>
            </vxe-column>
          </vxe-colgroup>
        </vxe-table>
      </div>
    </el-card>

    <!-- 里程碑达成率 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="cruxToolbarRef">
          <template #buttons>
            <CardTitle title="里程碑按期达成情况" />
            <div class="over-tip">
              <span>
                统计公式：里程碑按期达成率=周期内实际按期达成里程碑个数÷周期内计划达成里程碑个数x100%
                <!-- 备注：月度/周度未完成的里程碑节点会累加至下一个月度/周度的计划中，即本月度/周度计划里程碑个数=计划安排在本月度/周度完成的里程碑数+过去所有未完成里程碑个数 -->
              </span>
            </div>
          </template>
          <template #tools>
            <el-radio-group v-model="cruxType" @change="getCruxList">
              <el-radio-button label="月" value="year" />
              <el-radio-button label="周" value="week" />
            </el-radio-group>
            <el-date-picker
              v-model="cruxDate"
              value-format="YYYY"
              type="year"
              @change="getCruxList"
              :clearable="false"
            />
          </template>
        </vxe-toolbar>
      </template>
      <div v-loading="cruxLoading">
        <vxe-table
          ref="cruxTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem', background: '#eee' }"
          :cell-style="{ padding: '5px', fontSize: '.8rem' }"
          :cell-config="{ height: 24 }"
          :header-cell-config="{ height: 24 }"
          :footer-cell-config="{ height: 24 }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="cruxList"
          :export-config="{ type: 'xlsx', isMerge: true }"
          align="center"
          height="258px"
          show-footer
          :footerMethod="footerMethodSum1"
          border
          stripe
        >
          <vxe-column field="manager" title="项目经理" width="120" fixed="left" />

          <vxe-colgroup
            field="total"
            :title="`${dateUtil(cruxDate).format('YYYY')}年度`"
            width="60"
            fixed="left"
          >
            <vxe-column
              field="planCount-100"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'planCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'other',
                      dateType: 'month',
                      year: Number(cruxDate),
                      serialNumber: 100,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data[100]?.planCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="actualCount-100"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'other',
                      dateType: 'month',
                      year: Number(cruxDate),
                      serialNumber: 100,
                      manager: row.manager,
                      completed: true
                    })
                  "
                >
                  {{ row.data[100]?.actualCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="percent-100"
              title="达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'percent')"
            >
              <template #default="{ row }">
                <el-progress
                  :text-inside="true"
                  :stroke-width="18"
                  :percentage="row.data[100]?.percent || 0"
                  status="success"
                  class="no-radius"
                />
              </template>
            </vxe-column>
          </vxe-colgroup>
          <vxe-colgroup field="currentTotal" title="当期数据">
            <vxe-column
              field="planCount-99"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 99, 'planCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'other',
                      dateType: 'month',
                      year: Number(cruxDate),
                      serialNumber: 99,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data[99]?.planCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="actualCount-99"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 99, 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showCruxNode({
                      type: 'other',
                      dateType: 'month',
                      year: Number(cruxDate),
                      serialNumber: 99,
                      manager: row.manager,
                      completed: true
                    })
                  "
                >
                  {{ row.data[99]?.actualCount }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="percent-99"
              title="当期达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 99, 'percent')"
            >
              <template #default="{ row }">
                <el-progress
                  :text-inside="true"
                  :stroke-width="18"
                  :percentage="row.data[99]?.percent || 0"
                  status="success"
                  class="no-radius"
                />
              </template>
            </vxe-column>
          </vxe-colgroup>
          <template v-if="cruxType === 'year'">
            <vxe-colgroup
              v-for="month in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
              :key="month"
              :field="`month${month}`"
              :title="`${month}月情况`"
            >
              <vxe-column
                :field="`planCount-${month}`"
                title="计划"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'planCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showCruxNode({
                        type: 'other',
                        dateType: 'month',
                        year: Number(cruxDate),
                        serialNumber: month,
                        manager: row.manager
                      })
                    "
                  >
                    {{ row.data[month]?.planCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`actualCount-${month}`"
                title="实际"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'actualCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showCruxNode({
                        type: 'other',
                        dateType: 'month',
                        year: Number(cruxDate),
                        serialNumber: month,
                        manager: row.manager,
                        completed: true
                      })
                    "
                  >
                    {{ row.data[month]?.actualCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`percent-${month}`"
                title="达成率"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'percent')"
              >
                <template #default="{ row }">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="18"
                    :percentage="row.data[month]?.percent || 0"
                    status="success"
                    class="no-radius"
                  />
                </template>
              </vxe-column>
            </vxe-colgroup>
          </template>
          <template v-else>
            <vxe-colgroup
              v-for="month in weekList"
              :key="month"
              :field="`month${month}`"
              :title="`第${month}周情况-${getWeekRange(month)?.label}`"
            >
              <vxe-column :field="`planCount-${month}`" title="计划" width="100">
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showCruxNode({
                        type: 'other',
                        dateType: 'week',
                        year: Number(cruxDate),
                        serialNumber: month,
                        manager: row.manager
                      })
                    "
                  >
                    {{ row.data[month]?.planCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column :field="`actualCount-${month}`" title="实际" width="100">
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showCruxNode({
                        type: 'other',
                        dateType: 'week',
                        year: Number(cruxDate),
                        serialNumber: month,
                        manager: row.manager,
                        completed: true
                      })
                    "
                  >
                    {{ row.data[month]?.actualCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column :field="`percent-${month}`" title="达成率" width="100">
                <template #default="{ row }">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="18"
                    :percentage="row.data[month]?.percent || 0"
                    status="success"
                    class="no-radius"
                  />
                </template>
              </vxe-column>
            </vxe-colgroup>
          </template>
        </vxe-table>
      </div>
    </el-card>

    <!-- 工时情况 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom ref="workingHoursToolbarRef">
          <template #buttons>
            <CardTitle title="工时情况" />
            <div class="over-tip">
              <span>
                统计公式：活动工时达成率=周期内达成的活动的标准工时（即实际工时）÷周期内公司规定出勤工时（即计划工时）x100%
              </span>
            </div>
          </template>
          <template #tools>
            <el-radio-group v-model="workingHoursType" @change="getworkingHoursList">
              <el-radio-button label="月" value="year" />
              <el-radio-button label="周" value="week" />
            </el-radio-group>
            <el-date-picker
              v-model="workingHoursDate"
              value-format="YYYY"
              type="year"
              @change="getworkingHoursList"
              :clearable="false"
            />
            <el-button
              @click="
                exportWorkhoursToExcel(
                  '工时情况',
                  workingHoursList,
                  workingHoursDate,
                  workingHoursType,
                  workingHoursLoading
                )
              "
              circle
            >
              <Icon icon="ep:download" />
            </el-button>
          </template>
        </vxe-toolbar>
      </template>
      <div>
        <vxe-table
          ref="workingHoursTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem', background: '#eee' }"
          :cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-config="{ height: 24 }"
          :header-cell-config="{ height: 24 }"
          :footer-cell-config="{ height: 24 }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="workingHoursList"
          align="center"
          border
          max-height="600px"
          min-height="400px"
          :loading="workingHoursLoading"
          stripe
          :filter-config="{ remote: true }"
          @filter-change="workingHoursFilterChangeEvent"
        >
          <vxe-column
            field="manager"
            title="人员"
            width="120"
            fixed="left"
            :filters="workingHoursUserList"
            :filter-render="FilterValue.userOrDeptFilterRender"
          />

          <vxe-colgroup
            field="total"
            :title="`${dateUtil(workingHoursDate).format('YYYY')}年度`"
            width="60"
            fixed="left"
          >
            <vxe-column
              field="planCount-100"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'planCount')"
            >
              <template #default="{ row }">
                {{ row.data[100]?.planCount.toFixed(0) || 0 }}
              </template>
            </vxe-column>
            <vxe-column
              field="actualCount-100"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showWorkHoursDetail({
                      dateType: 'month',
                      year: Number(workingHoursDate),
                      serialNumber: 100,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data[100]?.actualCount.toFixed(0) || 0 }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="percent-100"
              title="达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'percent')"
            >
              <template #default="{ row }"> {{ row.data[100]?.percent || 0 }}% </template>
            </vxe-column>
          </vxe-colgroup>
          <template v-if="workingHoursType === 'year'">
            <vxe-colgroup
              v-for="month in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
              :key="month"
              :field="`month${month}`"
              :title="`${month}月情况`"
            >
              <vxe-column
                :field="`planCount-${month}`"
                title="计划"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'planCount')"
              >
                <template #default="{ row }">
                  {{ row.data[month]?.planCount.toFixed(0) || 0 }}
                </template>
              </vxe-column>
              <vxe-column
                :field="`actualCount-${month}`"
                title="实际"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'actualCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showWorkHoursDetail({
                        dateType: 'month',
                        year: Number(workingHoursDate),
                        serialNumber: month,
                        manager: row.manager
                      })
                    "
                  >
                    {{ row.data[month]?.actualCount.toFixed(0) || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`percent-${month}`"
                title="达成率"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'percent')"
              >
                <template #default="{ row }"> {{ row.data[month]?.percent || 0 }}% </template>
              </vxe-column>
            </vxe-colgroup>
          </template>
          <template v-else>
            <vxe-colgroup
              v-for="month in weekList1"
              :key="month"
              :field="`month${month}`"
              :title="`第${month}周情况-${getWeekRange(month)?.label}`"
            >
              <vxe-column
                :field="`planCount-${month}`"
                title="计划"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'planCount')"
              >
                <template #default="{ row }">
                  {{ row.data[month]?.planCount || 0 }}
                </template>
              </vxe-column>
              <vxe-column
                :field="`actualCount-${month}`"
                title="实际"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'actualCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showWorkHoursDetail({
                        dateType: 'week',
                        year: Number(workingHoursDate),
                        serialNumber: month,
                        manager: row.manager
                      })
                    "
                  >
                    {{ row.data[month]?.actualCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`percent-${month}`"
                title="达成率"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'percent')"
              >
                <template #default="{ row }"> {{ row.data[month]?.percent || 0 }}% </template>
              </vxe-column>
            </vxe-colgroup>
          </template>
        </vxe-table>
      </div>
    </el-card>

    <!-- 问题关闭情况 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom ref="problemToolbarRef">
          <template #buttons>
            <CardTitle title="问题按期关闭情况" />
            <div class="over-tip">
              <span>
                统计公式：问题按期关闭率=周期内实际按期关闭问题个数÷周期内计划关闭问题个数x100%
                备注：年度/月度/周度均体现问题按期关闭的情况，本周期未按期完成的问题数不会计入下一个周期的数据中。
              </span>
            </div>
          </template>
          <template #tools>
            <el-radio-group v-model="problemType" @change="getProblemList">
              <el-radio-button label="月" value="year" />
              <el-radio-button label="周" value="week" />
            </el-radio-group>
            <el-date-picker
              v-model="problemDate"
              value-format="YYYY"
              type="year"
              @change="getProblemList"
              :clearable="false"
            />
            <el-button
              @click="
                exportWorkhoursToExcel(
                  '问题按期关闭',
                  problemList,
                  problemDate,
                  problemType,
                  problemLoading
                )
              "
              circle
            >
              <Icon icon="ep:download" />
            </el-button>
          </template>
        </vxe-toolbar>
      </template>
      <div>
        <vxe-table
          ref="problemTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem', background: '#eee' }"
          :cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-config="{ height: 24 }"
          :header-cell-config="{ height: 24 }"
          :footer-cell-config="{ height: 24 }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="problemList"
          :virtual-y-config="{ enabled: problemVitual, gt: 0 }"
          :virtual-x-config="{ enabled: problemVitual, gt: 0 }"
          align="center"
          border
          stripe
          max-height="600px"
          min-height="400px"
          :loading="problemLoading"
          :filter-config="{ remote: true }"
          @filter-change="problemFilterChangeEvent"
        >
          <vxe-column
            field="manager"
            title="人员"
            width="120"
            fixed="left"
            :filters="userList"
            :filter-render="FilterValue.userOrDeptFilterRender"
          />

          <vxe-colgroup
            field="total"
            :title="`${dateUtil(problemDate).format('YYYY')}年度`"
            width="60"
            fixed="left"
          >
            <vxe-column
              field="planCount-100"
              title="计划"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'planCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showProblemInfo({
                      dateType: 'month',
                      year: Number(problemDate),
                      serialNumber: 100,
                      manager: row.manager
                    })
                  "
                >
                  {{ row.data[100]?.planCount || 0 }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="actualCount-100"
              title="实际"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'actualCount')"
            >
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="
                    showProblemInfo({
                      dateType: 'month',
                      year: Number(problemDate),
                      serialNumber: 100,
                      manager: row.manager,
                      completed: true
                    })
                  "
                >
                  {{ row.data[100]?.actualCount || 0 }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column
              field="percent-100"
              title="达成率"
              width="100"
              sortable
              :sort-by="(data: any) => sortWorkHoursMethod(data?.row, 100, 'percent')"
            >
              <template #default="{ row }"> {{ row.data[100]?.percent || 0 }}% </template>
            </vxe-column>
          </vxe-colgroup>
          <template v-if="problemType === 'year'">
            <vxe-colgroup
              v-for="month in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
              :key="month"
              :field="`month${month}`"
              :title="`${month}月情况`"
            >
              <vxe-column
                :field="`planCount-${month}`"
                title="计划"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'planCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showProblemInfo({
                        dateType: 'month',
                        year: Number(problemDate),
                        serialNumber: month,
                        manager: row.manager
                      })
                    "
                  >
                    {{ row.data[month]?.planCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`actualCount-${month}`"
                title="实际"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'actualCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showProblemInfo({
                        dateType: 'month',
                        year: Number(problemDate),
                        serialNumber: month,
                        manager: row.manager,
                        completed: true
                      })
                    "
                  >
                    {{ row.data[month]?.actualCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`percent-${month}`"
                title="达成率"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'percent')"
              >
                <template #default="{ row }"> {{ row.data[month]?.percent || 0 }}% </template>
              </vxe-column>
            </vxe-colgroup>
          </template>
          <template v-else>
            <vxe-colgroup
              v-for="month in problemWeekList"
              :key="month"
              :field="`month${month}`"
              :title="`第${month}周情况-${getWeekRange(month)?.label}`"
            >
              <vxe-column
                :field="`planCount-${month}`"
                title="计划"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'planCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showProblemInfo({
                        dateType: 'week',
                        year: Number(problemDate),
                        serialNumber: month,
                        manager: row.manager
                      })
                    "
                  >
                    {{ row.data[month]?.planCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`actualCount-${month}`"
                title="实际"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'actualCount')"
              >
                <template #default="{ row }">
                  <el-link
                    type="primary"
                    @click="
                      showProblemInfo({
                        dateType: 'week',
                        year: Number(problemDate),
                        serialNumber: month,
                        manager: row.manager,
                        completed: true
                      })
                    "
                  >
                    {{ row.data[month]?.actualCount || 0 }}
                  </el-link>
                </template>
              </vxe-column>
              <vxe-column
                :field="`percent-${month}`"
                title="达成率"
                width="100"
                sortable
                :sort-by="(data: any) => sortWorkHoursMethod(data?.row, month, 'percent')"
              >
                <template #default="{ row }"> {{ row.data[month]?.percent || 0 }}% </template>
              </vxe-column>
            </vxe-colgroup>
          </template>
        </vxe-table>
      </div>
    </el-card>

    <!-- 变更次数 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="changeToolbarRef">
          <template #buttons>
            <CardTitle title="项目变更次数" />
            <div class="over-tip">
              <span>
                统计公式：项目变更次数=项目经理提交的关于项目信息、活动信息变更的次数
                备注：因1-4月未在系统上固化项目变更流程，故无取数。5月份数据为新系统上线与模块测试故此数据无参考性，6月之后的数据方可使用进行分析。
              </span>
            </div>
          </template>
          <template #tools>
            <el-date-picker
              v-model="changeDate"
              value-format="YYYY"
              type="year"
              @change="getChangeList"
              :clearable="false"
            />
          </template>
        </vxe-toolbar>
      </template>
      <div v-loading="changeLoading">
        <vxe-table
          ref="changeTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem', background: '#eee' }"
          :cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-config="{ height: 24 }"
          :header-cell-config="{ height: 24 }"
          :footer-cell-config="{ height: 24 }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="changeList"
          :export-config="{ type: 'xlsx', isMerge: true }"
          align="center"
          border
          stripe
        >
          <vxe-column field="manager" title="项目经理" width="120" fixed="left" />
          <!-- 动态生成12个月 -->
          <vxe-column
            v-for="month in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
            :key="month"
            :field="`data.${month}`"
            :title="`${month}月`"
            width="100"
          >
            <template #default="{ row }">
              <span v-if="changeDate == '2025' && ![1, 2, 3, 4].includes(month)">{{
                row.data[month]
              }}</span>
              <span
                v-else-if="
                  (changeDate == '2025' && [1, 2, 3, 4].includes(month)) ||
                  Number(changeDate) < 2025
                "
                >/</span
              >
            </template>
          </vxe-column>
          <!-- 年度总数 -->
          <vxe-column field="annual" title="年度总数" width="100" />
        </vxe-table>
      </div>
    </el-card>
    <Dialog title="关键节点数据切片" v-model="cruxDialogVisible" width="80%">
      <vxe-toolbar ref="cruxNodeToolbarRef" export size="mini" />
      <vxe-table
        ref="cruxNodeTableRef"
        :data="cruxNodeList"
        :export-config="{ type: 'xlsx' }"
        show-overflow
        align="center"
        :cell-config="{ height: '34px' }"
        max-height="600"
      >
        <vxe-column title="序号" type="seq" width="50" />
        <vxe-column title="项目经理" field="manager" width="80" />
        <vxe-column title="项目名称" field="projectName">
          <template #default="{ row }">
            <ToProjectCenter :name="row.projectName" :basics-id="row.projectId" />
          </template>
        </vxe-column>
        <vxe-column title="活动名称" field="activitiesName">
          <template #default="{ row }">
            <ToProjectCenter
              :name="row.activitiesName"
              :basics-id="row.projectId"
              :activities-id="row.activitiesId"
            />
          </template>
        </vxe-column>
        <vxe-column title="计划开始时间" field="startDate" />
        <vxe-column title="计划完成时间" field="endDate" />
        <vxe-column title="实际完成时间" field="completedDate" />
      </vxe-table>
    </Dialog>
    <Dialog title="工时数据切片" v-model="workHoursDetailDialogVisible" width="80%">
      <vxe-toolbar ref="workHoursDetailToolbarRef" export size="mini" />
      <vxe-table
        ref="workHoursDetailTableRef"
        :data="workHoursDetailList"
        :export-config="{ type: 'xlsx' }"
        show-overflow
        align="center"
        :cell-config="{ height: '34px' }"
        max-height="600"
        :footer-cell-style="{ padding: 0 }"
        :footer-method="footerMethodSum2"
        show-footer
      >
        <vxe-column title="序号" type="seq" width="50" />
        <vxe-column title="人员" field="manager" width="80" />
        <vxe-column title="项目名称" field="basicsName">
          <template #default="{ row }">
            <ToProjectCenter :name="row.basicsName" :basics-id="row.projectId" />
          </template>
        </vxe-column>
        <vxe-column title="项目等级" field="basicsLevel" />
        <vxe-column title="活动名称" field="activitiesName">
          <template #default="{ row }">
            <ToProjectCenter
              :name="row.activitiesName"
              :basics-id="row.projectId"
              :activities-id="row.activitiesId"
            />
          </template>
        </vxe-column>
        <vxe-column title="计划开始时间" field="startDate" />
        <vxe-column title="计划完成时间" field="endDate" />
        <vxe-column title="实际完成时间" field="completedDate" />
        <vxe-column title="标准工时" field="hours" width="80" />
        <vxe-column title="负责人数" field="personCount" width="80" />
        <vxe-column title="分摊工时" field="avgHours" width="80" />
      </vxe-table>
    </Dialog>
    <Dialog title="问题数据切片" v-model="problemInfoDialogVisible" width="80%">
      <vxe-toolbar ref="problemInfoToolbarRef" export size="mini" />
      <vxe-table
        ref="problemInfoTableRef"
        :data="problemInfoList"
        :export-config="{ type: 'xlsx' }"
        show-overflow
        align="center"
        :cell-config="{ height: '34px' }"
        max-height="600"
      >
        <vxe-column title="序号" type="seq" width="50" />
        <vxe-column title="人员" field="manager" width="80" />
        <vxe-column title="项目名称" field="projectName">
          <template #default="{ row }">
            <ToProjectCenter :name="row.projectName" :basics-id="row.projectId" />
          </template>
        </vxe-column>
        <vxe-column title="问题内容" field="activitiesName">
          <template #default="{ row }">
            <ToProjectCenter
              :name="row.activitiesName"
              :basics-id="row.projectId"
              :problem-id="row.activitiesId"
            />
          </template>
        </vxe-column>
        <vxe-column title="提出时间" field="startDate" />
        <vxe-column title="计划完成时间" field="endDate" />
        <vxe-column title="实际完成时间" field="completedDate" />
      </vxe-table>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { KanbanApi } from '@/api/project/kanban'
import { dateUtil } from '@/utils/dateUtil'
import moment from 'moment'
import 'moment/locale/zh-cn'
import * as FilterValue from '@/views/project/ProjectCenter/main/components/Filter'
import * as XLSX from 'xlsx'
import ToProjectCenter from './ToProjectCenter.vue'

const userList = ref([{ data: [] }])
const workingHoursUserList = ref([{ data: [] }])

// 设置周规则（周一为起点，1月1日所在周为第1周）
moment.locale('zh-cn', {
  week: {
    dow: 1, // 周一为一周开始
    doy: 1 // 1月1日所在周为第一周
  }
})

moment.locale('zh-cn') // 全局启用中文

// ADCP 按期达成
const adcpDate = ref(dateUtil().format('YYYY'))
const adcpLoading = ref(false)
const adcpList = ref<any[]>([])

const adcpToolbarRef = ref()
const adcpTableRef = ref()

const getAdcpAchieved = async () => {
  adcpLoading.value = true
  try {
    const res = await KanbanApi.getAdcpAchieved(Number(adcpDate.value))
    adcpList.value = res
      .reduce(
        (acc, curr) => {
          // 数据校验
          if (!curr || !curr.manager || !curr.month) {
            console.warn('无效数据项', curr)
            return acc
          }

          const { manager, month, planCount, actualCount, rate } = curr
          const monthKey = month.toString()

          // 查找已有记录
          const existing = acc.find((item) => item.manager === manager)

          if (existing) {
            existing.data[monthKey] = { planCount, actualCount, rate }
          } else {
            acc.push({
              manager,
              data: {
                [monthKey]: { planCount, actualCount, rate }
              }
            })
          }

          return acc
        },
        [] as Array<{
          manager: string
          data: Record<
            string,
            { planCount: number; actualCount: number; rate: number }
          >
        }>
      )
      // 新增：计算当前月份或全年累计数据
      .map((item) => {
        const currentYear = new Date().getFullYear()
        const currentMonth = new Date().getMonth() + 1
        const selectedYear = Number(adcpDate.value)
        const isCurrentYear = selectedYear === currentYear
        const targetMonth = isCurrentYear ? currentMonth : 12

        let totalPlan = 0
        let totalActual = 0

        for (let m = 1; m <= targetMonth; m++) {
          const key = m.toString()
          if (item.data[key]) {
            totalPlan += item.data[key].planCount
            totalActual += item.data[key].actualCount
          }
        }

        item.data[99] = {
          planCount: totalPlan,
          actualCount: totalActual,
          rate: totalPlan
            ? ((totalActual / totalPlan) * 100).toFixed(1)
            : '0.0'
        }
        let annualPlan = 0;
        let annualActual = 0;

        for (let m = 1; m <= 12; m++) {
          const key = m.toString()
          if (item.data[key]) {
            annualPlan += item.data[key].planCount
            annualActual += item.data[key].actualCount
          }
        }


        item.data.annual = {
          planCount: annualPlan,
          actualCount: annualActual,
          rate: annualPlan
            ? ((annualActual / annualPlan) * 100).toFixed(1)
            : '0.0'
        }

        return item
      })
      .sort((a, b) => a.manager.localeCompare(b.manager))
  } finally {
    adcpLoading.value = false
  }
}

/** 表尾合计函数 */
function footerMethodSum({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '项目集'
      }
      if (column.field.includes('rate')) {
        return (
          (
            (sumNum(data, columns[_columnIndex - 1].field) /
              sumNum(data, columns[_columnIndex - 2].field) || 0) * 100
          ).toFixed(1) + '%'
        )
      }
      return sumNum(data, column.field)
    })
  ]
  return footerData
}

function footerMethodSum1({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '项目集'
      }

      if (column.field.includes('percent')) {
        return (
          (
            (sumNum1(data, columns[_columnIndex - 1].field) /
              sumNum1(data, columns[_columnIndex - 2].field) || 0) * 100
          ).toFixed(1) + '%'
        )
      }
      return sumNum1(data, column.field)
    })
  ]
  return footerData
}
function footerMethodSum2({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '合计'
      }
      if (column.field.includes('avgHours')) {
        return sumNum2(data, column.field).toFixed(2)
      }
      return ''
    })
  ]
  return footerData
}
// 进行合计
function sumNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    const types = type.split('-')
    total += costForm[i].data[types[1]]?.[types[0]] || 0
  }
  return total
}
function sumNum1(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    const split = type.split('-')
    total += costForm[i].data[split[1]]?.[split[0]] || 0
  }
  return total
}
function sumNum2(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i]?.[type] || 0
  }
  return total
}

// 里程碑达成
const cruxDate = ref(dateUtil().format('YYYY'))
const cruxLoading = ref(false)
const cruxList = ref<any[]>([])
const cruxToolbarRef = ref()
const cruxTableRef = ref()
const cruxType = ref('year')

const weekList = computed(() => {
  return Array.from(
    { length: moment().year(Number(cruxDate.value)).isoWeeksInYear() + 2 },
    (_, i) => i + 1
  )
})

const weekList1 = computed(() => {
  return Array.from(
    { length: moment().year(Number(workingHoursDate.value)).isoWeeksInYear() + 2 },
    (_, i) => i + 1
  )
})

// 输入年份和周数，返回周起止日期
// 输入年份和周数，返回当年内的周起止日期
const getWeekRange = (week: number) => {
  // 获取该周的周一
  let start = moment().year(Number(cruxDate.value)).week(week).startOf('week')
  // 获取该周的周日
  let end = moment().year(Number(cruxDate.value)).week(week).endOf('week')
  if (start.format('YYYY-MM-DD') != `${Number(cruxDate.value)}-01-01`) {
    start = moment()
      .year(Number(cruxDate.value))
      .week(week - 1)
      .startOf('week')
    end = moment()
      .year(Number(cruxDate.value))
      .week(week - 1)
      .endOf('week')
  }
  if (week == 54 && start.month() == 0) {
    return {
      label: '当前年度无54周'
    }
  }

  // 强制约束起止日期在当年内
  const yearStart = moment(`${Number(cruxDate.value)}-01-01`).startOf('day')
  const yearEnd = moment(`${Number(cruxDate.value)}-12-31`).endOf('day')

  const adjustedStart = start.isBefore(yearStart) ? yearStart : start
  const adjustedEnd = end.isAfter(yearEnd) ? yearEnd : end

  return {
    start: adjustedStart.format('YYYY-MM-DD'),
    end: adjustedEnd.format('YYYY-MM-DD'),
    label: `${adjustedStart.format('M月D日')} - ${adjustedEnd.format('M月D日')}`
  }
}
const getCruxList = async () => {
  cruxLoading.value = true
  try {
    const res = await KanbanApi.getCruxAchieved(Number(cruxDate.value), cruxType.value)
    // Map 转 Array
    cruxList.value = Object.entries(res)
      .map(([key, value]: [string, any[]]) => ({
        manager: key,
        data: transformData(value)
      }))
      .sort((a, b) => {
        const compare = a.manager.localeCompare(b.manager)
        return compare
      })
  } finally {
    cruxLoading.value = false
  }
}

// 变更次数
const changeDate = ref(dateUtil().format('YYYY'))
const changeLoading = ref(false)
const changeList = ref<any[]>([])
const changeToolbarRef = ref()
const changeTableRef = ref()

const getChangeList = async () => {
  changeLoading.value = true
  try {
    const res = await KanbanApi.getChangeProject(Number(changeDate.value))
    // 按项目经理分组，初始化每月数据为0
    const grouped = res.reduce(
      (acc, curr) => {
        const { manager, month, total } = curr

        if (!acc[manager]) {
          acc[manager] = {
            manager,
            data: {
              ...Array.from({ length: 12 }, (_, i) => ({ [i + 1]: 0 })).reduce(
                (o, v) => ({ ...o, ...v }),
                {}
              )
            },
            annual: 0
          }
        }

        // 更新对应月份数据
        acc[manager].data[month] = total
        acc[manager].annual += total

        return acc
      },
      {} as Record<string, { manager: string; data: Record<string, number>; annual: number }>
    )
    // 转换为数组
    changeList.value = Object.values(grouped)
  } finally {
    changeLoading.value = false
  }
}

// 问题关闭情况
const problemType = ref('year')
const problemDate = ref(dateUtil().format('YYYY'))
const problemVitual = ref(true)
const problemLoading = ref(false)
const problemList = ref<any[]>([])
const problemToolbarRef = ref()
const problemTableRef = ref()
const problemWeekList = computed(() => {
  return Array.from(
    { length: moment().year(Number(problemDate.value)).isoWeeksInYear() + 2 },
    (_, i) => i + 1
  )
})

const problemQueryParams = ref({
  year: 0,
  type: '',
  filterType: '',
  filterIds: [] as any[]
})

const problemFilterChangeEvent = ({ filterList }) => {
  const type = filterList[0].datas[0].type
  problemQueryParams.value.filterType = type
  if (type === 'user') {
    problemQueryParams.value.filterIds = filterList[0].datas[0].userList
  } else {
    problemQueryParams.value.filterIds = filterList[0].datas[0].deptList
  }
  getProblemList()
}

const getProblemList = async () => {
  problemLoading.value = true
  try {
    problemQueryParams.value.year = Number(problemDate.value)
    problemQueryParams.value.type = problemType.value
    const res = await KanbanApi.getProblemAchieved(problemQueryParams.value)
    problemList.value = Object.entries(res).map(([key, value]: [string, any[]]) => ({
      manager: key,
      data: transformData(value)
    }))
  } finally {
    problemLoading.value = false
  }
}

// 工时情况
const workingHoursType = ref('year')
const workingHoursDate = ref(dateUtil().format('YYYY'))
const workingHoursLoading = ref(false)
const workingHoursList = ref<any[]>([])
const workingHoursToolbarRef = ref()
const workingHoursTableRef = ref()
const workingHoursQueryParams = ref({
  year: 0,
  type: '',
  filterType: '',
  filterIds: [] as any[]
})

const sortWorkHoursMethod: any = (row: any, index: number | string, field: string) => {
  // 按名称长度进行排序
  return row.data[index]?.[field] || 0
}

const getworkingHoursList = async () => {
  workingHoursLoading.value = true
  try {
    workingHoursQueryParams.value.year = Number(workingHoursDate.value)
    workingHoursQueryParams.value.type = workingHoursType.value
    const res = await KanbanApi.getWorkHoursNew(workingHoursQueryParams.value)
    // Map 转 Array
    workingHoursList.value = Object.entries(res).map(([key, value]: [string, any[]]) => ({
      manager: key,
      data: transformData(value)
    }))
  } finally {
    workingHoursLoading.value = false
  }
}

const workingHoursFilterChangeEvent = ({ filterList }) => {
  const type = filterList[0].datas[0].type
  workingHoursQueryParams.value.filterType = type
  if (type === 'user') {
    workingHoursQueryParams.value.filterIds = filterList[0].datas[0].userList
  } else {
    workingHoursQueryParams.value.filterIds = filterList[0].datas[0].deptList
  }
  getworkingHoursList()
}

// 转换函数
function transformData(data: any[]): any {
  return data.reduce((acc, curr) => {
    // 自动类型推断
    acc[curr.serialNumber] = {
      planCount: curr.planCount, // 保持字段大小写一致性
      actualCount: curr.actualCount, // 建议改为 actualCount 保持驼峰命名
      percent: curr.percent
    }
    return acc
  }, {} as any)
}

/**
 * 导出工时数据到Excel
 * @param workinghoursList 表格数据源
 * @param workinghoursDate 日期参数
 * @param workingHoursType 类型参数（year/week）
 * @param containerRef 容器引用用于加载状态
 */
async function exportWorkhoursToExcel(
  title: string,
  workinghoursList: any[],
  workinghoursDate: string,
  workingHoursType: string,
  tempLoading: boolean
) {
  tempLoading = true
  try {
    // 显示加载状态

    // 构建工作表数据
    const wsData = [] as any[]

    // 添加表头
    const headers = ['人员', `${workinghoursDate}年度计划`, '实际', '达成率']

    // 动态添加月份/周度列
    const months =
      workingHoursType === 'year'
        ? [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
        : Array.from({ length: 54 }, (_, i) => i + 1)

    months.forEach((month) => {
      headers.push(
        `${month}${workingHoursType === 'year' ? '月' : '周'}计划`,
        `${month}${workingHoursType === 'year' ? '月' : '周'}实际`,
        `${month}${workingHoursType === 'year' ? '月' : '周'}达成率`
      )
    })

    wsData.push(headers)

    // 添加数据行
    workinghoursList.forEach((item) => {
      const row = [item.manager]

      // 添加年度数据
      row.push(
        item.data[100]?.planCount || 0,
        item.data[100]?.actualCount || 0,
        `${item.data[100]?.percent || 0}%`
      )

      // 添加动态列数据
      months.forEach((month) => {
        row.push(
          item.data[month]?.planCount || 0,
          item.data[month]?.actualCount || 0,
          `${item.data[month]?.percent || 0}%`
        )
      })

      wsData.push(row)
    })

    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(wsData)

    // 设置列宽
    ws['!cols'] = [
      { wpx: 120 }, // 人员列
      ...Array(3 + months.length * 3).fill({ wpx: 100 }) // 动态列
    ]

    // 创建工作簿并导出
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, '详情')

    // 生成文件名
    const fileName = `${title}_${workinghoursDate}_${Date.now()}.xlsx`

    // 触发下载
    XLSX.writeFile(wb, fileName)
  } catch (error) {
    console.error('导出失败:', error)
    throw error
  } finally {
    tempLoading = false
    console.log(tempLoading)
  }
}

/** 项目关键节点数据切片 */
const cruxDialogVisible = ref(false)
const cruxNodeList = ref<any[]>([])
const cruxNodeTableRef = ref()
const cruxNodeToolbarRef = ref()

const showCruxNode = async (row: any) => {
  const res = await KanbanApi.getCruxNode(row)
  cruxNodeList.value = res
  cruxDialogVisible.value = true
  await nextTick()
  unref(cruxNodeTableRef)?.connect(unref(cruxNodeToolbarRef))
}

/** 项目工时数据切片  */
const workHoursDetailDialogVisible = ref(false)
const workHoursDetailList = ref<any[]>([])
const workHoursDetailTableRef = ref()
const workHoursDetailToolbarRef = ref()

const showWorkHoursDetail = async (row: any) => {
  const res = await KanbanApi.getWorkHoursDetail(row)
  workHoursDetailList.value = res
  workHoursDetailDialogVisible.value = true
  await nextTick()
  unref(workHoursDetailTableRef)?.connect(unref(workHoursDetailToolbarRef))
}

/** 问题数据切片  */
const problemInfoDialogVisible = ref(false)
const problemInfoList = ref<any[]>([])
const problemInfoTableRef = ref()
const problemInfoToolbarRef = ref()

const showProblemInfo = async (row: any) => {
  const res = await KanbanApi.getProblemInfo(row)
  problemInfoList.value = res
  problemInfoDialogVisible.value = true
  await nextTick()
  unref(problemInfoTableRef)?.connect(unref(problemInfoToolbarRef))
}

const setDefaultFilter = (table: any) => {
  const manager = unref(table)
    .getColumns()
    .find((item) => item.field === 'manager')?.filters[0]
  manager.data.type = 'dept'
  manager.data.deptList = [3246, 3468, 3511, 3512, 3513, 3508, 3509, 3510, 3472]
  unref(table).updateFilterOptionStatus(manager, true)
}

onMounted(async () => {
  await nextTick()

  unref(adcpTableRef)?.connect(unref(adcpToolbarRef))
  unref(cruxTableRef)?.connect(unref(cruxToolbarRef))
  unref(changeTableRef)?.connect(unref(changeToolbarRef))
  unref(problemTableRef)?.connect(unref(problemToolbarRef))
  unref(workingHoursTableRef)?.connect(unref(workingHoursToolbarRef))

  requestAnimationFrame(() => {
    setDefaultFilter(workingHoursTableRef)
    workingHoursQueryParams.value.filterType = 'dept'
    workingHoursQueryParams.value.filterIds = [3246, 3468, 3511, 3512, 3513, 3508, 3509, 3510, 3472]

    setDefaultFilter(problemTableRef)
    problemQueryParams.value.filterType = 'dept'
    problemQueryParams.value.filterIds = [3246, 3468, 3511, 3512, 3513, 3508, 3509, 3510, 3472]

    getAdcpAchieved()
    getCruxList()
    getChangeList()
    getProblemList()
    getworkingHoursList()
  })
})
</script>

<style lang="scss" scoped>
:deep(.el-card__header),
:deep(.el-card__body) {
  padding: 5px;
}
:deep(.el-card__header) {
  padding: 10px 10px !important;
}
:deep(.vxe-toolbar) {
  padding: 0;
}
:deep(.el-progress-bar__innerText) {
  color: black;
}

:deep(.vxe-buttons--wrapper) {
  width: 60%;
}
:deep(.vxe-cell) {
  padding: 0px !important;
}
:deep(.vxe-header--column) {
  border-bottom: 0.3px solid #fafafa;
  border-right: 0.3px solid #fafafa;
}
</style>
