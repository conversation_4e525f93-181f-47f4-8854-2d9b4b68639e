<template>
  <ActivitiesContainer
    :basicsInfo="props.basicsInfo!"
    v-model:nodeList="baseNode"
    v-model:currentNode="currentNode"
    :stage-count="stageCount"
    type="activities"
    @add="showDecompositionActivities"
    v-loading="loading"
    title="活动"
  >
    <template #activities-buttons>
      <vxe-toolbar
        size="mini"
        custom
        :export="hasPermission('activities_export')"
        class="w-100%"
        ref="activitiesToolbarRef"
      >
        <template #buttons>
          <el-button
            type="primary"
            plain
            @click="showBatchDecompositionActivities"
            v-if="hasPermission('activities_disassemble', props.basicsInfo?.id)"
          >
            分解模板活动
          </el-button>
          <el-button
            type="warning"
            plain
            v-if="props.custom && hasPermission('activities_disassemble', props.basicsInfo?.id)"
            @click="showDecompositionActivities"
          >
            自定义活动
          </el-button>
          <div class="over-tip !w-60%">
            <span>
              {{ activitieTypeCount }}
            </span>
          </div>
        </template>
        <template #tools>
          <el-tooltip content="时间记录的显示与隐藏">
            <el-switch
              size="small"
              v-model="historyVisible"
              class="mr-10px !h-1.2rem"
              @change="historyDateChange"
            />
          </el-tooltip>
        </template>
      </vxe-toolbar>
    </template>
    <template #activities-table>
      <ActivitiesTable
        ref="activitiesTableRef"
        :list="activitiesList"
        :userList="userList"
        :basicsId="basicsInfo?.id"
        :history-date="historyVisible"
        v-model:toolbar="activitiesToolbarRef"
        @show="showRow"
        @success="
          (item) => {
            listActivities()
            setCurrentRow(item)
          }
        "
        @show-date-log="onListChangeDateLog"
      />
    </template>
  </ActivitiesContainer>
  <ActivitiesForm
    ref="activitiesFormRef"
    :userList="userList"
    :activities-list="activitiesList"
    :file-template-list="fileTemplateList"
    @success="listActivities"
    @refresh="listActivities"
  />
  <ActivitiesBatchForm
    ref="activitiesBatchFormRef"
    :basicsId="basicsInfo?.id!"
    :user-list="userList"
    :template-category="templateCategory"
    @success="listActivities"
  />
  <ActivitiesCustomForm
    ref="activitiesCustomFormRef"
    :user-list="userList"
    :basicsId="props.basicsInfo?.id"
    :stage="currentNode"
  />
  <Dialog title="活动日期变更记录" v-model="changeDateLogVisible">
    <el-form label-width="120">
      <el-form-item label="活动">
        <div class="w-100% bg-#ebeff3 rounded-1 pl-10px text-#111111">
          {{ `${activities?.orderNo} ${activities?.name}` }}
        </div>
      </el-form-item>
      <el-form-item label="初始计划周期">
        <div class="w-100% bg-#ebeff3 rounded-1 pl-10px text-#111111">
          {{ `${activities?.initStartDate} - ${activities?.initEndDate}` }}
        </div>
      </el-form-item>
      <el-form-item label="当前计划周期">
        <div class="w-100% bg-#ebeff3 rounded-1 pl-10px text-#111111">
          {{ `${activities?.startDate} - ${activities?.endDate}` }}
        </div>
      </el-form-item>
      <el-form-item label="变更记录">
        <vxe-table class="w-100%" align="center" border show-overflow :data="changeDateLogList">
          <vxe-column title="变更对象" field="modifyFieldName" width="100px" />
          <vxe-column title="变更前" field="beforeValue" width="100px" />
          <vxe-column title="变更后" field="afterValue" width="100px" />
          <vxe-column title="原因" field="reason" align="left" />
          <vxe-column title="流程" field="processInstanceId" width="100px">
            <template #default="{ row }">
              <el-button size="small" link type="primary" @click="toBpm(row.processInstanceId)">
                查看
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </el-form-item>
    </el-form>
  </Dialog>
</template>

<script lang="ts" setup>
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import ActivitiesTable from './ActivitiesTable.vue'
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import ActivitiesForm from './ActivitiesForm.vue'
import ActivitiesBatchForm from './ActivitiesBatchForm.vue'
import ActivitiesCustomForm from './ActivitiesCustomForm.vue'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ActivitiesApi, ActivitiesVO } from '@/api/project/activities'
import { FileTemplateApi } from '@/api/project/file/template'
import { debounce } from 'min-dash'
import { useCache } from '@/hooks/web/useCache'
import { hasPermission } from '@/views/project/ProjectCenter/main/util/permission'
import { getIntDictOptions } from '@/utils/dict'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  },
  custom: {
    type: Boolean,
    default: false
  },
  templateCategory: {
    type: String,
    default: ''
  }
})
const { wsCache } = useCache()
const baseNode = ref<any[]>([]) //项目基础节点列表
const currentNode = ref<number>(undefined as unknown as number) //当前节点
const activitiesCustomFormRef = ref()
const activitiesBatchFormRef = ref()
const userList = ref<UserVO[]>([])
const activitiesList = ref<ActivitiesVO[]>([])
const fileTemplateList = ref<any[]>([])
const loading = ref(false)
const activitiesTableRef = ref()
const activitiesToolbarRef = ref()
const activitiesFormRef = ref()
const queryParams = ref({
  basicsId: props.basicsInfo?.id,
  stage: currentNode.value
})
const stageCount = ref<any>({})

const activities = ref<any>({})
const changeDateLogVisible = ref(false)
const changeDateLogList = ref<any[]>([])
const router = useRouter()

const historyVisible = ref(true)

const onListChangeDateLog = async (row: any) => {
  changeDateLogVisible.value = true
  activities.value = row
  const res = await ActivitiesApi.getDateChangeLog(row.id)
  const reasonList = res.filter((item) => item.modifyField === 'update')
  changeDateLogList.value = res
    .filter((item) => item.modifyField !== 'update')
    .map((item) => {
      return {
        modifyFieldName: item.modifyFieldName,
        beforeValue: item.beforeValue,
        afterValue: item.afterValue,
        reason: reasonList.find((el) => el.processInstanceId == item.processInstanceId)
          ?.modifyFieldName,
        processInstanceId: item.processInstanceId
      }
    })
}

const activitieTypeCount = computed(() => {
  if (activitiesList.value?.length == 0) return ''
  const dict = getIntDictOptions('project_activities_status')

  const statusCounts = activitiesList.value.reduce(
    (acc, activity) => {
      const status = activity.status || '未知' // 处理未定义状态
      acc[status] = (acc[status] || 0) + 1
      return acc
    },
    {} as Record<string, number>
  )
  console.log(statusCounts)
  let result = ''
  dict.forEach((item) => {
    if (result != '') {
      result += '；'
    }
    result += `${item.label}：${statusCounts[item.value] || 0}条`
  })
  return result
})

/** 获取活动列表 */
const listActivities = async () => {
  loading.value = true
  try {
    queryParams.value.basicsId = props.basicsInfo?.id
    const form = wsCache.get('project_page_show_form')
    if (form && form?.stage) {
      currentNode.value = Number(form.stage)
    }
    queryParams.value.stage = currentNode.value
    const res = await ActivitiesApi.getActivitiesList(queryParams.value)
    activitiesList.value = res
    if (form && form?.id) {
      await nextTick()
      unref(activitiesTableRef).setCurrent(Number(form.id))
    }
    if (form && form?.type) {
      wsCache.delete('project_page_show_form')
    }
  } finally {
    loading.value = false
  }
}

const getStageCount = async () => {
  const res = await ActivitiesApi.getStageCount(props.basicsInfo!.id!)
  stageCount.value = res
}

const listFileTemplate = async () => {
  const res = await FileTemplateApi.getFileTemplatePage({})
  fileTemplateList.value = res
}

/** 获取项目基础节点 */
const onListBaseNode = debounce(async () => {
  baseNode.value = []
  const res = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: props.templateCategory,
    basicsId: props.basicsInfo?.id
  })
  if (res.length > 0) {
    currentNode.value = res[0].id
  }
  for (const item of res) {
    baseNode.value.push({ id: item.id, label: item.name })
  }
}, 100)

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

/** 显示活动分解页面 */
const showDecompositionActivities = async () => {
  const node = baseNode.value.find((item) => item.id === currentNode.value)
  unref(activitiesCustomFormRef)?.openForm(node.label)
}

const showBatchDecompositionActivities = async () => {
  const node = baseNode.value.find((item) => item.id === currentNode.value)
  unref(activitiesBatchFormRef)?.openForm(
    node.label,
    node.id,
    props.basicsInfo?.teams,
    props.basicsInfo?.releaseDate
  )
}

const showRow = async (row: ActivitiesVO) => {
  unref(activitiesFormRef)?.openForm(row, props.basicsInfo)
}

const setCurrentRow = async (id: number) => {
  // await listActivities()
  await nextTick()
  unref(activitiesTableRef)?.setCurrent(id)
}

const historyDateChange = () => {
  wsCache.set('activities_history_date_show', historyVisible.value)
}

/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

watch(
  () => [props.basicsInfo, props.templateCategory],
  async () => {
    if (props.basicsInfo?.id && props.templateCategory) {
      baseNode.value = []
      await onListBaseNode()
    }
  },
  { immediate: true }
)
const debouncedFetch = debounce(() => {
  getStageCount()
  listActivities()
}, 300)

watch(
  () => [props.basicsInfo, currentNode.value],
  async () => {
    if (props.basicsInfo?.id && currentNode.value) {
      await debouncedFetch()
    }
  }
)
onMounted(() => {
  getUserList()
  listFileTemplate()
  const activities_history_date_show = wsCache.get('activities_history_date_show')
  historyVisible.value =
    activities_history_date_show === undefined ||
    activities_history_date_show === null ||
    activities_history_date_show === ''
      ? true
      : activities_history_date_show
})
</script>
