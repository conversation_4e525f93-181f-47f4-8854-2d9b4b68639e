<template>
  <vxe-split-pane width="300px" min-width="300px" name="risk-condition">
    <div class="p-10px">
      <el-form id="problem-condition" class="custom-form" label-width="70px">
        <el-form-item label="项目">
          <el-input v-model="formData.basicsName" />
        </el-form-item>
        <el-form-item label="项目状态">
          <el-select v-model="formData.basicsStatus" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提出日期">
          <el-date-picker
            type="daterange"
            v-model="formData.createTime"
            unlink-panels
            value-format="YYYY-MM-DD"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="formData.status" multiple>
            <el-option
              v-for="dict in getIntDictOptions('project_activities_status')"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="针对要素">
          <el-select v-model="formData.targetFactor" filterable clearable>
            <el-option
              v-for="item in factorList"
              :key="item.id"
              :label="item.factor"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input type="textarea" v-model="formData.description" :rows="6" />
        </el-form-item>
        <el-form-item label="建议">
          <el-input type="textarea" v-model="formData.suggestion" :rows="6" />
        </el-form-item>
        <el-form-item label="负责人">
          <UserAvatarList
            v-model="formData.director!"
            :user-list="userList"
            :size="28"
            :limit="3"
          />
        </el-form-item>
      </el-form>
    </div>
  </vxe-split-pane>
  <vxe-split-pane>
    <div class="p-10px h-full">
      <vxe-toolbar custom size="mini" ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" size="small" @click="handleList"> 查询 </el-button>
          <el-button type="warning" size="small" @click="refresh"> 重置 </el-button>
        </template>
        <template #tools>
          <el-button type="primary" size="small" @click="handleExport"> 导出 </el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        height="86%"
        :header-cell-style="{ padding: '0', height: '34px' }"
        :cell-style="{ padding: '0', height: '34px' }"
        show-overflow
        align="center"
        border
        ref="tableRef"
        :data="technicalList"
        :loading="loading"
        :export-config="{
          remote: true,
          exportMethod: handleExport
        }"
      >
        <vxe-column title="项目名称" field="basicsName" width="200" align="left" />
        <vxe-column title="项目状态" field="basicsStatus" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_status', row.basicsStatus) }}
          </template>
        </vxe-column>
        <vxe-column title="项目等级" field="basicsLevel" width="100">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.basicsLevel) }}
          </template>
        </vxe-column>
        <vxe-column title="评审节点" field="nodeName" width="160" align="left" />
        <vxe-column title="提出人" field="creator" width="80">
          <template #default="{ row }">
            {{ getUserNickName(row.cretor) }}
          </template>
        </vxe-column>
        <vxe-column
          title="提出日期"
          field="createTime"
          :formatter="dateFormatter4"
          width="90"
          align="center"
        />
        <vxe-column title="图片" field="imgIds" width="60" align="center">
          <template #default="{ row }">
            <template v-if="row.imgIds?.length > 0">
              <img :src="row.imgIds?.[0]" style="width: 1.5vw; height: 1.5vw" />
              <div
                style="
                  position: absolute;
                  top: 0;
                  width: 1vw;
                  height: 1vw;
                  background-color: var(--el-color-primary-light-3);
                  border-radius: 1rem;
                  right: 0;
                  color: #fff;
                "
                >{{ row.imgIds?.length }}</div
              >
            </template>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="status" width="95" align="center">
          <template #default="{ row }">
            <DictTag type="project_activities_status" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="进度" field="progress" width="100">
          <template #default="{ row }">
            {{ row.progress }}
          </template>
        </vxe-column>
        <vxe-column title="针对要素" field="targetFactor">
          <template #default="{ row }">
            {{ factorList.find((item) => item.id === row.targetFactor)?.factor }}
          </template>
        </vxe-column>
        <vxe-column title="问题描述" field="description" />
        <vxe-column title="建议" field="suggestion" />
        <vxe-column title="负责人" field="director" width="120">
          <template #default="{ row }">
            {{ getUserNickName(row.director) }}
          </template>
        </vxe-column>
        <vxe-column
          title="预计完成"
          field="endDate"
          :formatter="dateFormatter4"
          align="center"
          width="100"
        />
      </vxe-table>
      <Pagination
        :total="total"
        v-model:page="formData.pageNo"
        v-model:limit="formData.pageSize"
        @pagination="onList"
      />
    </div>
  </vxe-split-pane>
</template>

<script lang="ts" setup>
import { getDictLabel, getIntDictOptions } from '@/utils/dict'
import { dateFormatter4 } from '@/utils/formatTime'
import { UserVO, getAllUser } from '@/api/system/user'
import { TechnicalFactorApi, TechnicalFactorVO } from '@/api/project/technicalfactor'
import { TechnicalApi } from '@/api/project/technical'
import download from '@/utils/download'

const loading = ref(false)
const technicalList = ref<any[]>([])
const total = ref(0)
const userList = ref<UserVO[]>([])
const factorList = ref<TechnicalFactorVO[]>([])

const tableRef = ref()
const toolbarRef = ref()

const message = useMessage()

const formData = ref({
  basicsName: undefined,
  basicsStatus: undefined,
  createTime: undefined,
  status: undefined,
  targetFactor: undefined,
  description: undefined,
  suggestion: undefined,
  director: undefined,
  pageNo: 1,
  pageSize: 20
})

const handleList = () => {
  formData.value.pageNo = 1
  onList()
}

const onList = async () => {
  loading.value = true
  try {
    const res = await TechnicalApi.getTechnicalPage(formData.value)
    technicalList.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    basicsName: undefined,
    basicsStatus: undefined,
    createTime: undefined,
    status: undefined,
    targetFactor: undefined,
    description: undefined,
    suggestion: undefined,
    director: undefined,
    pageNo: 1,
    pageSize: 20
  }
  handleList()
}

const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    loading.value = true
    const data = await TechnicalApi.exportMulti(formData.value)
    download.excel(data, '技术评审信息.xlsx')
  } catch {
  } finally {
    loading.value = false
  }
}

const onListFactor = async () => {
  const res = await TechnicalFactorApi.getSimpleList()
  factorList.value = res
}

const onListUser = async () => {
  userList.value = await getAllUser()
}
/** 获取用户名 */
const getUserNickName = (ids: number[]) => {
  if (!ids || ids.length === 0) return ''
  return userList.value
    .filter((item) => ids.includes(item.id))
    .map((item) => item.nickname + (item.status == 2 ? '(离职)' : ''))
    .join(',')
}

onMounted(() => {
  onListUser()
  onListFactor()
  handleList()

  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>
