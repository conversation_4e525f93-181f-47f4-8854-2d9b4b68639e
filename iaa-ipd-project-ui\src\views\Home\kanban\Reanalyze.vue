<template>
  <div class="h-full p-10px">
    <vxe-table
      v-if="reanalyzeList.length>0"
      border
      :data="reanalyzeList"
      height="100%"
      class="w-100%"
      scrollbar-always-on
      :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
      :cell-style="{ padding: 0, fontSize: '.8rem', height: '2rem' }"
      :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
      :scroll-y="{ enabled: true, gt: 0 }"
      align="center"
      show-overflow
      :loading="loading"
      :edit-config="{ trigger: 'manual', mode: 'row' }"
      ref="tableRef"
    >
      <vxe-colgroup title="项目报警/暂停信息">
        <vxe-column title="项目名称" field="name" />
        <vxe-column title="项目类型" field="mold">
          <template #default="{ row }">
            {{ getDictLabel('project_type', row.mold) }}
          </template>
        </vxe-column>
        <vxe-column title="项目平台" field="platform">
          <template #default="{ row }">
            {{ getDictLabel('project_platform', row.platform) }}
          </template>
        </vxe-column>
        <vxe-column title="项目等级" field="level">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.level) }}
          </template>
        </vxe-column>
        <vxe-column title="产品经理" field="productUser" />
        <vxe-column title="项目经理" field="pmUser" />
        <vxe-column title="SE" field="seUser" />
        <vxe-column title="状态" field="status" />
        <vxe-column title="报警/暂停时间" field="endDate" :formatter="dateFormatter4" />
        <vxe-column title="延迟天数" field="extensionDays" />
      </vxe-colgroup>
      <vxe-colgroup title="报警分析">
        <vxe-column title="原因分析" field="reason" />
        <vxe-column title="采取措施" field="measures" />
        <vxe-column title="后续计划" field="plan" />
        <vxe-column title="操作" width="160">
          <template #default="{ row }">
            <el-button type="primary" size="small" plain @click="openForm(row)">填写</el-button>
          </template>
        </vxe-column>
      </vxe-colgroup>
    </vxe-table>
    <el-empty class="w-full h-full bg-white" description="您负责的项目下暂无需要复盘的关键活动" v-else />
    <Dialog v-model="dialogVisible" title="修改" width="50%">
      <el-form label-width="100px">
        <el-form-item label="原因分析">
          <el-input type="textarea" :rows="4" v-model="formData.reason" />
        </el-form-item>
        <el-form-item label="采取措施">
          <el-input type="textarea" :rows="4" v-model="formData.measures" />
        </el-form-item>
        <el-form-item label="后续计划">
          <el-input type="textarea" :rows="4" v-model="formData.plan" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="onSave">保存</el-button>

      </template>
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
import { getDictLabel } from '@/utils/dict'
import { dateFormatter4 } from '@/utils/formatTime'
import { ReanalyzeApi } from '@/api/project/reanalyze'

const reanalyzeList = ref<any[]>([])

const loading = ref(false)
const tableRef = ref()
const message = useMessage()
const formData = ref({
  id:undefined,
  activitiesId:undefined,
  reason:undefined,
  measures:undefined,
  plan:undefined
})
const dialogVisible = ref(false)

const onList = async () => {
  loading.value = true
  try {
    const res = await ReanalyzeApi.getReanalyze()
    reanalyzeList.value = res
  } finally {
    loading.value = false
  }
}

const openForm =(row:any)=>{
  dialogVisible.value = true
  formData.value ={
    id:row.id,
    activitiesId:row.nodeId,
    reason:row.reason,
    measures:row.measures,
    plan:row.plan
  }
}

const onSave = async () => {
  try {
    await ReanalyzeApi.saveReanalyze(formData.value)
    message.success('保存成功')
    dialogVisible.value = false
    onList()
  }finally{}
}

onMounted(() => {
  onList()
})
</script>
