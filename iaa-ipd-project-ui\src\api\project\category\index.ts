import request from '@/config/axios'

// 项目分类 VO
export interface CategoryVO {
  id: number // 分类ID
  name: string // 分类名称
  templateCategory: string
  sort: number // 分类排序
  customActivity: boolean // 是否允许自定义分解活动
}

// 项目分类 API
export const CategoryApi = {
  // 查询项目分类分页
  getCategoryPage: async (params: any) => {
    return await request.get({ url: `/project/category/page`, params })
  },

  //查询所有项目分类
  getCategoryList: async (params: any) => {
    return await request.get({ url: `/project/category/list`, params })
  },
  //查询所有项目分类
  getCategoryListByUserRange: async () => {
    return await request.get({ url: `/project/category/list-range` })
  },
  // 查询项目分类详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/project/category/get?id=` + id })
  },

  // 新增项目分类
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/project/category/create`, data })
  },

  // 修改项目分类
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/project/category/update`, data })
  },

  // 删除项目分类
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/project/category/delete?id=` + id })
  },

  // 导出项目分类 Excel
  exportCategory: async (params) => {
    return await request.download({ url: `/project/category/export-excel`, params })
  }
}
