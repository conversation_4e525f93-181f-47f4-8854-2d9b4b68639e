<template>
  <ActivitiesContainer
    :basicsInfo="basicsInfo!"
    :nodeList="nodeList"
    v-model:currentNode="currentNode"
    :stage-count="stageCount"
    title="问题"
  >
    <template #activities-buttons>
      <vxe-toolbar
        size="mini"
        :export="hasPermission('problem_export')"
        custom
        class="w-100%"
        ref="toolbarRef"
      >
        <template #buttons>
          <el-button
            size="small"
            type="primary"
            plain
            @click="
              problemFormRef.open('提出:' + nodeList.find((el) => el.id == currentNode).label)
            "
            v-if="![1,4].includes(props.basicsInfo?.status!)&&(getVisiableUserList(props.basicsInfo?.id).includes(getUser.id) || getUser.id == 1)"
          >
            提出问题
          </el-button>
          <div class="over-tip">
            <span
              >A类问题是影乡响产品核心功能、安规或用户体验的严重问题，必须立即解决。B类问题对用户体验有一定影响，但不会导致产品完全无法使用，可以在A类问题解决后逐步处理。C类问题对产品核心功能和用户体验影响较小，通常是优化和改进类问题，可以在项目后期或资源充足时处理。</span
            >
          </div>
        </template>
        <template #tools>
          <el-button
            circle
            class="mr-10px !w-1.5rem !h-1.5rem"
            @click="problemUploadRef?.open()"
            v-if="hasPermission('problem_import')"
          >
            <Icon icon="ep:upload" />
          </el-button>
        </template>
      </vxe-toolbar>
    </template>
    <template #activities-table>
      <problem-table
        ref="tableRef"
        :list="probelmList"
        :template-node="baseNode"
        :user-list="userList"
        :basics-id="props.basicsInfo?.id"
        v-model:toolbar="toolbarRef"
        @update:success="listProblem"
        @show:form="(row: any) => problemFormRef.open('问题详情', row)"
        v-loading="loading"
      />
    </template>
  </ActivitiesContainer>
  <ProblemForm
    :template-node="baseNode"
    :basics-id="props.basicsInfo?.id"
    :user-list="userList"
    :current-node="currentNode"
    ref="problemFormRef"
    @submit:success="listProblem"
  />
  <ProblemUpload
    :basics-id="props.basicsInfo?.id"
    :category="props.templateCategory"
    ref="problemUploadRef"
    @success="listProblem"
  />
</template>

<script lang="ts" setup>
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import ProblemTable from './ProblemTable.vue'
import ProblemForm from './ProblemForm.vue'
import { BasicsVO } from '@/api/project/basics'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { ProblemVO, ProblemApi } from '@/api/project/problem'
import { hasPermission, getVisiableUserList } from '../util/permission'
import { useCache } from '@/hooks/web/useCache'
import { useUserStore } from '@/store/modules/user'
import ProblemUpload from './ProblemUpload.vue'

const problemFormRef = ref()
const nodeList = ref<Array<any>>([])
const currentNode = ref<string>('')
const baseNode = ref<Array<any>>([])
const userList = ref<Array<UserVO>>([])
const probelmList = ref<Array<ProblemVO>>([])
const loading = ref(false)
const { wsCache } = useCache()
const { getUser } = useUserStore()

const toolbarRef = ref<any>()
const tableRef = ref<any>()
const problemUploadRef = ref()

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  },
  templateCategory: {
    type: String,
    default: ''
  }
})

const queryParams = reactive({
  type: '',
  basicsId: undefined
})
const stageCount = ref<any>({})

/** 获取问题列表 */
const listProblem = async () => {
  loading.value = true
  try {
    queryParams.basicsId = props.basicsInfo?.id as any
    const form = wsCache.get('project_page_show_form')
    if (form) {
      currentNode.value = form.stage as string
    }
    queryParams.type = currentNode.value
    probelmList.value = await ProblemApi.getProblemList(queryParams)
    await nextTick()
    if (form) {
      unref(tableRef).scrollToRow(Number(form.id))
    }
  } finally {
    loading.value = false
  }
}

const getStageCount = async () => {
  const res = await ProblemApi.getStageCount(props.basicsInfo!.id!)
  stageCount.value = res
}

/** 问题节点 */
const listNode = async () => {
  nodeList.value = []
  for (let item of await getStrDictOptions(DICT_TYPE.PROJECT_PROBLEM_TYPE)) {
    nodeList.value.push({ id: item.value, label: item.label })
  }
  if (nodeList.value.length > 0) {
    currentNode.value = nodeList.value[0].id
  }
}

/** 获取项目基础节点 */
const onListBaseNode = async () => {
  baseNode.value = []
  const res = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: props.templateCategory
  })
  for (const item of res) {
    baseNode.value.push({ id: item.id, label: item.name })
  }
}

/** 获取用户列表 */
const getUserList = async () => {
  const data = await getSimpleUserList()
  userList.value = data
}

watch(
  () => [props.basicsInfo, currentNode.value],
  () => {
    if (props.basicsInfo?.id && currentNode.value) {
      getStageCount()
      listProblem()
    }
  }
)

watch(
  () => props.templateCategory,
  () => {
    if (props.templateCategory) {
      onListBaseNode()
    }
  },
  { immediate: true }
)

onMounted(() => {
  listNode()
  getUserList()
})
</script>

<style lang="scss" scoped>
:deep(.vxe-buttons--wrapper) {
  width: 60%;
}
</style>
