import request from '@/config/axios'

export const RiskFlowApi = {
  createRisk: async (data: any) => {
    return await request.post({ url: '/bpm/risk/create', data })
  },
  // 更新风险
  updateRisk: async (data: any) => {
    return await request.post({ url: '/bpm/risk/update', data })
  },
  // 删除风险
  deleteRisk: async (data: any) => {
    return await request.post({ url: '/bpm/risk/delete', data })
  },
  // 获取风险
  getRisk: async (id: string) => {
    return await request.get({ url: `/bpm/risk/get/${id}` })
  }
}
