import request from '@/config/axios'

// 项目技术评审 VO
export interface TechnicalVO {
  id?: number // 评审ID
  basicsId?: number // 项目ID
  nodeId?: number // 评审节点ID
  imgIds?: string[] // 问题图片
  status?: number // 评审状态
  progress?: number // 进度
  targetFactor?: number // 针对要素
  description?: string // 问题描述
  suggestion?: string // 建议
  creator?: number[] // 提出人
  director?: number[] // 评审负责人
  endDate?: Date // 预计完成日期
  completeDate?: Date // 实际完成日期
  dateOfApproval?: Date // 审批通过日期
  processInstanceId?: string // 当前流程ID
}

export interface TechnicalAdditionalVO {
  id?: number // id
  basicsId?: number // 项目ID
  nodeId?: number // TR 节点ID
  teamIds?: number[] // 团队ID
  externalTeamIds?: number[] // 外部团队ID
}

// 项目技术评审 API
export const TechnicalApi = {
  createTechnical: async (data: TechnicalVO) => {
    return await request.post({ url: '/project/technical/create', data })
  },
  listTechnical: async (params: any) => {
    return await request.get({ url: `/project/technical/list`, params })
  },
  getTechnical: async (id: number) => {
    return await request.get({ url: `/project/technical/get/${id}` })
  },
  /** 更新技术评审附加信息 */
  updateAdditional: async (data: any) => {
    return await request.post({ url: '/project/technical/update-additional', data })
  },
  /** 获取技术评审附加信息 */
  getAdditional: async (params: any) => {
    return await request.get({ url: `/project/technical/get-additional`, params })
  },
  /** 导出技术评审预审记录 */
  exportTechnical: async (data: any) => {
    return await request.downloadPost({ url: `/project/technical/export`, data })
  },
  /** 获取技术评审预审阶段数量 */
  getStageCount: async (basicsId: number) => {
    return await request.get({ url: `/project/technical/get-stage-count/${basicsId}` })
  },
  getTechnicalPage: (data: any) => {
    return request.post({ url: `/project/technical/page`, data })
  },
  exportMulti: (data: any) => {
    return request.downloadPost({ url: `/project/technical/export-multi`, data })
  }
}
