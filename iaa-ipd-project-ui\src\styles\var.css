:root {
  --login-bg-color: #293146;

  --left-menu-max-width: 200px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: #001529;

  --left-menu-bg-light-color: #0f2438;

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-text-color: #bfcbd9;

  --left-menu-text-active-color: #fff;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-height: 40px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color: '#fff';

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --tags-view-height: 30px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 4.5vw;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 10px;

  --app-content-bg-color: #ebeff3;

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;

  --primary-text-color:#303133;

  --regular-text-color:#606266;

  --secondary-text-color:#909399;

  --placeholder-text-color:#A8ABB2;

  --disabled-text-color:#C0C4CC;
}

.dark {
  --app-content-bg-color: var(--el-bg-color);
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
