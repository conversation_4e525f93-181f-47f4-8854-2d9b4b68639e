// main.js

import { VxeUI } from 'vxe-table'
import TextFilter from '@/components/Filter/TextFilter.vue'
import UserFilter from '@/components/Filter/UserFilter.vue'
import UserOrDeptFilter from '@/components/Filter/UserOrDeptFilter.vue'
import DateRangeFilter from '@/components/Filter/DateRangeFilter.vue'
import { useUserStore } from '@/store/modules/user'
import moment from 'moment'

// 创建一个简单的输入框筛选渲染器
VxeUI.renderer.add('TextFilter', {
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <TextFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = ''
    })
  },
  // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
  tableFilterRecoverMethod ({ option }) {
    option.data = ''
  },
  // 自定义筛选方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const { data } = option
    const cellValue = row[column.field]
    if (cellValue) {
      return cellValue.indexOf(data) > -1
    }
    return false
  }
})


VxeUI.renderer.add('UserFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <UserFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'my', userList: [] }
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const {getUser}= useUserStore()
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { userList, type } = option.data
    if (cellValue) {
      if (type === 'my') {
        return cellValue.includes(getUser.id)
      }
      // 将过滤值转为 Set，提升查找效率
      const filterSet = new Set(userList);
      
      // 判断 cellValue 是否包含 filterSet 中的任意一个元素
      return cellValue.some(val => filterSet.has(val));
    }
    return false
  }
})

VxeUI.renderer.add('UserOrDeptFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <UserOrDeptFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = { type: 'dept', deptList: [] }
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    const cellValue = row[column.field]
    const { userList,deptList, type } = option.data
    if (cellValue) {
      // 将过滤值转为 Set，提升查找效率
      const filterSet = new Set(type=='user'?userList:deptList);
      
      // 判断 cellValue 是否包含 filterSet 中的任意一个元素
      return cellValue.some(val => filterSet.has(val));
    }
    return false
  }
})

const message = useMessage()

VxeUI.renderer.add('DateRangeFilter',{
  // 不显示底部按钮，使用自定义的按钮
  showTableFilterFooter: false,
  // 自定义筛选模板
  renderTableFilter (renderOpts, renderParams) {
    return <DateRangeFilter render-params={ renderParams } />
  },
  // 自定义重置数据方法
  tableFilterResetMethod (params) {
    const { options } = params
    options.forEach((option) => {
      option.data = {type:'currentMonth',startDate:undefined,endDate:undefined,history:false}
    })
  },
  // 自定义筛选数据方法
  tableFilterMethod (params) {
    const { option, row, column } = params
    if(column.field!=='date') return false;
    const { startDate,endDate, type,history } = option.data
    if(!type){
      message.error('请选择筛选类型')
      return false
    }
    const startDateVal = row?.['startDate']
    const endDateVal = row?.['endDate']
    const historyDate = row?.['historyDate']
    // 定义筛选时间范围变量
    let filterStart, filterEnd
    switch(type){
      case 'currentMonth':
        // 计算当前月份首尾日期
        filterStart = moment().startOf('months').startOf('days')
        filterEnd = moment().endOf('months').endOf('days')
        break;
      case 'currentWeek':
        filterStart = moment().startOf('weeks').startOf('days')
        filterEnd = moment().endOf('weeks').endOf('days')
        break;
      case 'range':
        filterStart = moment(startDate)
        filterEnd = moment(endDate)
        break;
    }
    // 将行数据转换为moment对象
    
    if(history){
      return historyDate?.some(item=>{
        const tempStart = moment(item.startDate)
        const tempEnd= moment(item.endDate)
        return (tempStart.isSameOrBefore(filterEnd) && tempEnd.isSameOrAfter(filterStart))
      })
    }else{
      const rowStart = moment(startDateVal)
      const rowEnd = moment(endDateVal)
      // 判断日期是否有交集（经典区间重叠公式）
      return (rowStart.isSameOrBefore(filterEnd) && rowEnd.isSameOrAfter(filterStart))
    }
  }
})