<template>
  <NoModalDrawer v-model="visiable" :title="currentTitle" size="50%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ currentTitle }}</div>
        <div v-if="formData.progress !== 100 && formData.status !== 10" class="flex">
          <el-tooltip
            content="修改问题"
            v-if="
              formData.id && (hasPermission('tr_modify') || formData.creator?.includes(getUser.id))
            "
          >
            <el-button :loading="loading" link @click="onModify">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip
            content="删除问题"
            v-if="
              formData.id && (hasPermission('tr_delete') || formData.creator?.includes(getUser.id))
            "
          >
            <el-button :loading="loading" link @click="onDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <template #default>
      <el-tooltip
        content="任务完成确认"
        v-if="
          !edit &&
          allowToComplete &&
          formData.status !== 10 &&
          formData.progress !== 100 &&
          allowPermission
        "
      >
        <el-button
          type="success"
          class="position-fixed w-20px !h-80px !p-0"
          style="left: 50%; top: calc(50% + 50px)"
          @click="onComplete"
        >
          <Icon icon="ep:check" :size="22" />
        </el-button>
      </el-tooltip>
      <el-alert
        v-if="formData.status === 10"
        type="warning"
        show-icon
        description="当前预审记录有相关流程审批中"
        close-text="查看流程"
        @close="toBpm(formData.processInstanceId!)"
      />
      <el-tabs
        v-model="currentTab"
        class="drawer-tabs position-sticky top-0 z-10 bg-#fff"
        @tab-change="onTabChange"
      >
        <el-tab-pane name="technical" label="预审记录" />
        <el-tab-pane name="target" label="输出物" v-if="!edit" />
        <el-tab-pane name="log" label="日志" v-if="!edit" />
      </el-tabs>
      <template v-if="currentTab === 'technical'">
        <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
          <el-collapse-item title="基础信息" name="1">
            <el-form
              label-width="120px"
              ref="formRef"
              class="custom-form"
              :rules="formRules"
              :model="formData"
            >
              <el-form-item label="问题图片">
                <UploadImgs
                  v-model="formData.imgIds!"
                  height="60px"
                  width="60px"
                  :disabled="!edit"
                />
              </el-form-item>
              <el-form-item label="针对的要素" prop="targetFactor">
                <div
                  class="w-full h-36px leading-[36px] text-1rem p-2px rounded-5px cursor-pointer"
                  :style="{
                    border: edit ? '1px solid var(--el-color-info-light-5)' : undefined,
                    backgroundColor: !edit ? 'var(--el-disabled-bg-color)' : undefined
                  }"
                  @click="edit && technicalFactorTableRef?.openForm()"
                >
                  {{ factorData.label }}
                </div>
              </el-form-item>
              <el-form-item label="预计完成" prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  type="date"
                  placeholder="选择日期"
                  :disabled="!edit"
                  :disabled-date="disabledDate"
                  @change="
                    formDataChangeString(
                      'endDate',
                      '预计完成',
                      formatDate(formData.endDate!),
                      formatDate(tempRow?.endDate),
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-form-item label="问题描述" prop="description">
                <el-input
                  v-model="formData.description"
                  type="textarea"
                  :rows="5"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'description',
                      '问题描述',
                      formData.description!,
                      tempRow?.description,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-form-item label="建议">
                <el-input
                  v-model="formData.suggestion"
                  type="textarea"
                  :rows="5"
                  :disabled="!edit"
                  @change="
                    formDataChangeString(
                      'description',
                      '问题描述',
                      formData.description!,
                      tempRow?.description,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-form-item label="负责人" prop="director">
                <user-avatar-list
                  v-model="formData.director!"
                  :user-list="props.userList"
                  :size="28"
                  :limit="10"
                  :add="edit"
                  :visiable-user-list="getVisiableUserList()"
                  @change:msg="
                    formDataChangeArray(
                      'director',
                      '责任人',
                      formData.director!,
                      tempRow?.director,
                      modifyInfo
                    )
                  "
                />
              </el-form-item>
              <el-form-item label="状态" v-if="formData.id && !edit">
                <DictTag type="project_activities_status" :value="formData.status!" />
              </el-form-item>
              <el-form-item label="进度" v-if="formData.id && !edit">
                <el-progress
                  :percentage="formData.progress"
                  class="w-100% no-radius"
                  :text-inside="true"
                  :stroke-width="20"
                  status="success"
                />
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="跟进记录" name="2" v-show="!edit">
            <Comment
              ref="commentRef"
              category="technical"
              :limit="5"
              bgColor="#fff"
              :user-list="props.userList"
            />
          </el-collapse-item>
        </el-collapse>
      </template>
      <template v-else-if="currentTab === 'target'">
        <vxe-table
          class="w-100%"
          :header-cell-style="{ padding: '5px', backgroundColor: '#fff' }"
          :cell-style="{ padding: '5px', height: '30px' }"
          show-overflow
          :data="attachmentList"
          align="center"
          border
        >
          <vxe-column title="文件名" field="name" min-width="200" align="left">
            <template #default="{ row }">
              <el-link
                type="primary"
                @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
              >
                {{ row.name }}
              </el-link>
            </template>
          </vxe-column>
          <vxe-column title="版本" field="currentVersion" width="60" />
          <vxe-column title="审签状态" field="approvalStatus" width="90">
            <template #default="{ row }">
              <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
            </template>
          </vxe-column>
          <vxe-column
            title="审核通过时间"
            field="approvalTime"
            :formatter="dateFormatter3"
            width="120"
          />
          <vxe-column title="操作" width="100px" fixed="right" align="center">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                v-if="![1, 0].includes(row.approvalStatus) && row.creator === getUser.id"
                @click="fileUploadAnewRef?.openForm(row)"
              >
                重传
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                v-if="row.approvalStatus != 1 && row.creator === getUser.id"
                @click="delAttachment(row)"
              >
                删除
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </template>
      <template v-else-if="currentTab === 'log'">
        <LogForm
          ref="logFormRef"
          category="technical"
          :base-id="formData.id"
          :user-list="props.userList"
        />
      </template>
    </template>
    <template #footer v-if="!formData.id && edit">
      <el-button :loading="loading" type="primary" @click="onSumbit(false)">保存</el-button>
      <el-button :loading="loading" type="primary" @click="onSumbit(true)"
        >保存并继续添加</el-button
      >
    </template>
    <template #footer v-else-if="['technical', 'target'].includes(currentTab) && !edit">
      <CommentInput
        v-model="commentData"
        placeholder="录入您的跟进记录，点击右下角纸飞机图标或回车发送"
        :user-list="
          props.userList.filter((item) =>
            [...getVisiableUserList(), ...supportlibraryList].includes(item.id)
          )
        "
        :sharp="false"
        @send="onSaveComment"
        v-if="currentTab == 'technical'"
        :loading="sendLoding"
      />
      <FileUpload
        v-if="
          currentTab == 'target' &&
          formData.status !== 10 &&
          formData.progress !== 100 &&
          allowPermission
        "
        ref="fileUploadRef"
        category="technical"
        :dynamic-id="formData.id"
        @success="onListAttachment"
      />
    </template>
  </NoModalDrawer>
  <TechnicalFactorTable
    ref="technicalFactorTableRef"
    :list="props.factorList"
    @success="onFactorChange"
  />
  <AttachmentPreview ref="attachmentPreviewRef" />
  <FileUploadAnew
    category="technical"
    :dynamic-id="formData.id!"
    ref="fileUploadAnewRef"
    @success="onListAttachment"
  />
</template>

<script lang="ts" setup>
import { TechnicalVO, TechnicalApi } from '@/api/project/technical'
import { TechnicalFactorVO } from '@/api/project/technicalfactor'
import { UserVO } from '@/api/system/user'
import { dateUtil } from '@/utils/dateUtil'
import { propTypes } from '@/utils/propTypes'
import TechnicalFactorTable from './TechnicalFactorTable.vue'
import { cloneDeep } from 'lodash-es'
import { CommentApi, CommentSaveReqVO } from '@/api/infra/comment'
import { CommentVO } from '@/components/CommentInput/comment'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import { dateFormatter3, formatDate } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import FileUploadAnew from '../components/FileUploadAnew.vue'
import AttachmentPreview from '../components/AttachmentPreview.vue'
import FileUpload from '../components/FileUpload.vue'
import { BasicsModifyInfoVO } from '@/api/bpm/basics'
import { formDataChangeString, formDataChangeArray } from '@/utils/formDataChange'
import { TechnicalFlowApi } from '@/api/bpm/technical'
import LogForm from '../../details/components/LogForm.vue'
import { hasPermission, getVisiableUserList } from '../util/permission'
import { useUserStore } from '@/store/modules/user'
import { BasicsApi } from '@/api/project/basics'
import { SupportLibraryApi } from '@/api/project/supportlibrary'

const supportlibraryList = ref<number[]>([])
const getSupportLibraryList = async () => {
  supportlibraryList.value = await SupportLibraryApi.getSimpleList()
}

const visiable = ref(false)
const currentTitle = ref('')
const edit = ref(true)
const currentTab = ref('technical')
const activeNames = ref(['1', '2'])
const formRef = ref()
const message = useMessage()
const emits = defineEmits(['submit:success'])
const technicalFactorTableRef = ref()
const commentRef = ref()
const factorData = ref({
  label: '',
  id: 0
})
const attachmentList = ref<AttachmentRespVO[]>([])
const attachmentPreviewRef = ref()
const fileUploadAnewRef = ref()
const logFormRef = ref()
const tempRow = ref<any>()
const modifyInfo = ref<BasicsModifyInfoVO[]>([])
const loading = ref(false)

const formData = ref<TechnicalVO>({})
const formRules = reactive({
  targetFactor: [{ required: true, message: '请选择针对要素', trigger: 'change' }],
  description: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
  director: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择预计完成日期', trigger: 'change' }]
})

const commentData = ref<CommentVO>({
  content: '',
  imgs: ''
})

const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired,
  basicsId: propTypes.number.isRequired,
  nodeId: propTypes.number.isRequired,
  factorList: propTypes.oneOfType<TechnicalFactorVO[]>([]).isRequired
})
const router = useRouter()
const allowToComplete = computed(() => {
  // 判断是否存在 approvalStatus === 0 的项
  return !attachmentList.value.some((item) => item.approvalStatus === 0)
})

const { getUser } = useUserStore()
const allowPermission = computed(() => {
  return formData.value.director?.includes(getUser.id)
})

/** 完成 */
const onComplete = async () => {
  loading.value = true
  try {
    if (attachmentList.value?.length === 0) {
      message.alertError('请输出您的问题完成佐证材料。')
      return
    }
    await message.confirm('确认当前问题已解决?')
    await TechnicalFlowApi.completeTechnicalFlow({
      ...formData.value,
      technicalId: formData.value.id
    })
    message.success('成功')
    edit.value = false
    emits('submit:success')
    const res = await TechnicalApi.getTechnical(formData.value.id!)
    formData.value = cloneDeep(res)
    tempRow.value = res
    modifyInfo.value.length = 0
  } finally {
    loading.value = false
  }
}

/** 删除 */
const onDelete = async () => {
  loading.value = true
  try {
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入删除原因', '删除问题', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '删除原因不能为空'
    })
    modifyInfo.value.push({
      modifyField: 'delete',
      modifyFieldName: '预审记录删除审批处理,原因：' + value,
      beforeValue: '',
      afterValue: ''
    })
    await TechnicalFlowApi.deleteTechnicalFlow({
      ...formData.value,
      technicalId: formData.value.id,
      modifyInfo: modifyInfo.value
    })
    message.success('成功')
    edit.value = false
    emits('submit:success')
    visiable.value = false
    refresh()
  } finally {
    loading.value = false
  }
}

/** 跳转流程详情 */
const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}
/** 修改 */
const onModify = async () => {
  loading.value = true
  try {
    if (!edit.value) {
      edit.value = true
      currentTab.value = 'technical'
      return
    }
    // 二次确认
    const { value } = await ElMessageBox.prompt('请输入变更原因', '技术评审', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
      inputErrorMessage: '修改原因不能为空'
    })
    modifyInfo.value.push({
      modifyField: 'update',
      modifyFieldName: '技术评审修改原因：' + value,
      beforeValue: ''
    })
    unref(formRef)?.validate()
    await TechnicalFlowApi.updateTechnicalFlow({
      ...formData.value,
      technicalId: formData.value.id,
      modifyInfo: modifyInfo.value
    })
    message.success('成功')
    edit.value = false
    emits('submit:success')
    const res = await TechnicalApi.getTechnical(formData.value.id!)
    formData.value = cloneDeep(res)
    tempRow.value = res
    modifyInfo.value.length = 0
  } finally {
    loading.value = false
  }
}

/** 删除附件 */
const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

const sendLoding = ref(false)
/** 发送评论 */
const onSaveComment = async () => {
  sendLoding.value = true
  try {
    if (typeof commentData.value.imgs == 'string') {
      if (!commentData.value.imgs) {
        commentData.value.imgs = []
      } else {
        commentData.value.imgs = [commentData.value.imgs]
      }
    }
    const comment: CommentSaveReqVO = {
      moduleId: 'technical' + formData.value.id,
      content: commentData.value.content,
      imgUrls: commentData.value.imgs,
      parentId: -1,
      replyCommentId: -1
    }
    await BasicsApi.sendComment(comment)
    message.success('发送成功')
    commentData.value = {
      content: '',
      imgs: ''
    }
    commentRef.value?.listEvent(formData.value.id)
  } finally {
    sendLoding.value = false
  }
}

/** 获取附件列表 */
const onListAttachment = async () => {
  const res = await AttachmentApi.getAttachmentList({
    category: 'technical',
    dynamicId: formData.value.id
  })
  attachmentList.value = res
}

/** 接受要素参数 */
const onFactorChange = (row: any) => {
  factorData.value.label = row.factor
  factorData.value.id = row.id
  formData.value.targetFactor = row.id
}

const onTabChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'technical':
      commentRef.value?.listEvent(formData.value.id)
      break
    case 'target':
      onListAttachment()
      break
    case 'log':
      logFormRef.value?.refreshLog()
      break
  }
}

const onSumbit = async (goOn: boolean) => {
  loading.value = true
  try {
    await formRef.value?.validate()
    formData.value.basicsId = props.basicsId
    formData.value.nodeId = props.nodeId
    formData.value.targetFactor = factorData.value.id
    await TechnicalApi.createTechnical(formData.value)
    message.success('保存成功')
    refresh()
    if (!goOn) {
      visiable.value = false
    }
    emits('submit:success')
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  formData.value = {
    id: undefined, // 评审ID
    basicsId: undefined, // 项目ID
    nodeId: undefined, // 评审节点ID
    imgIds: undefined, // 问题图片
    status: undefined, // 评审状态
    progress: undefined, // 进度
    targetFactor: undefined, // 针对要素
    description: undefined, // 问题描述
    suggestion: undefined, // 建议
    director: undefined, // 评审负责人
    endDate: undefined, // 预计完成日期
    completeDate: undefined, // 实际完成日期
    dateOfApproval: undefined, // 审批通过日期
    processInstanceId: undefined // 当前流程ID
  }
  factorData.value = {
    label: '',
    id: 0
  }
}

/** 将ga时间之后的时间禁用，同时将同行第一个时间之前的时间禁用 */
const disabledDate = (time: Date) => {
  return dateUtil().subtract(1, 'days').isAfter(time)
}
const openForm = async (title: string, row?: any) => {
  visiable.value = true
  currentTitle.value = title
  getSupportLibraryList()

  if (row) {
    formData.value = cloneDeep(row)
    tempRow.value = row
    factorData.value.id = row.targetFactor
    factorData.value.label =
      props.factorList.find((item) => item.id === row.targetFactor)?.factor || ''
    edit.value = false
    await nextTick()
    commentRef.value?.listEvent(formData.value.id)
    onListAttachment()
    CommentApi.viewMention({
      type: 'technical',
      itemId: row.id
    })
  } else {
    refresh()
    edit.value = true
  }
}

defineExpose({
  openForm
})
</script>
