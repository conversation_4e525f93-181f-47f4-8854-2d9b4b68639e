import { BasicsVO } from '@/api/project/basics'
import { useCache } from '@/hooks/web/useCache'
import { useUserStore } from '@/store/modules/user'
import type { App } from 'vue'

const { wsCache } = useCache()

export const hasPermission = (code: string, id?: number) => {
  const { getUser } = useUserStore()
  const basicsInfo: BasicsVO = wsCache.get('PROJECT_BASICS_INFO')
  // 如果项目已暂停，屏蔽权限
  if ([1,4].includes(basicsInfo.status!)) return false

  // 如果是超级管理员，获得所有的权限
  if (getUser.id === 1) return true

  const codes = basicsInfo.teams
    ?.filter((item) => item.userIds?.includes(getUser.id))
    .map((item) => item.role)
  if (codes?.includes('pm')) {
    return true
  }

  if (basicsInfo.managers?.includes(getUser.id)) {
    return true
  }
  const permissions = wsCache.get('PROJECT_PERMISSION')

  const permission = permissions.find((item) => item.code === code)

  // 3. 判断是否有权限（重点修改部分）
  if (permission?.roleIds && codes) {
    // 使用数组交集判断是否有共同角色
    const hasIntersection = permission.roleIds.some((roleId) => codes.includes(roleId))
    if (hasIntersection) return true
  }
  // 单独的用户权限
  if (permission?.userIds) {
    return permission.userIds.includes(getUser.id)
  }

  return false
}
/** 获取权限 */
export function getPermi(app: App<HTMLElement>) {
  app.directive('getPermi', async (el, binding) => {
    await nextTick()
    if (!hasPermission(binding.value[0])) {
      el.style.display = 'none'
    } else {
      el.style.display = 'block'
    }
  })
}

export function getVisiableUserList(temp?: number): number[] {
  const basicsInfo: BasicsVO = wsCache.get('PROJECT_BASICS_INFO')
  if (!basicsInfo?.teams) return []

  // 提取所有userIds并扁平化
  const allUserIds = basicsInfo.teams
    .map((team) => team.userIds || []) // 处理可能的空数组
    .flat()
  // 去重（利用Set）
  return Array.from(new Set(allUserIds))
}

export function getRolePermission(roleId: string, userId: number): boolean {
  const basicsInfo: BasicsVO = wsCache.get('PROJECT_BASICS_INFO')

  if (basicsInfo.managers?.includes(userId)) {
    return true
  }
  if (!basicsInfo?.teams) return false

  return basicsInfo.teams.find((item) => item.role === roleId)?.userIds?.includes(userId) || false
}
