<template>
  <vxe-toolbar size="mini" custom ref="toolbarRef">
    <template #buttons>
      <el-button type="primary" plain @click="operationFormRef?.openForm(0)">
        <Icon icon="ep:plus" />
        新增授权许可或转让记录
      </el-button>
    </template>
  </vxe-toolbar>
  <!-- 表格容器 -->
  <div class="h-[calc(100vh-280px)]">
    <vxe-table
      ref="tableRef"
      height="100%"
      :header-cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        backgroundColor: '#fafafa',
        color: 'var(--primary-text-color)'
      }"
      :row-style="{
        cursor: 'pointer'
      }"
      :cell-style="{
        padding: '0',
        height: '2.5rem',
        fontSize: '.9rem',
        color: 'var(--primary-text-color)'
      }"
      :header-cell-config="{ height: 40 }"
      :cell-config="{ height: 40 }"
      :data="list"
      border
      align="center"
      @cell-click="(el) => operationFormRef?.openForm(0,el.row.operationId)"
    >
      <vxe-column title="类型" field="recordType" width="120">
        <template #default="{row}">
          <DictTag type="patent_operation_record_type" :value="row.recordType" />
        </template>
      </vxe-column>
      <vxe-column title="原申请人" field="originalApplicant" min-width="120" />
      <vxe-column title="当前权力人" field="ownership" min-width="120" />
      <vxe-column title="申请号" field="applicationNo" min-width="120" />
      <vxe-column title="专利名称" field="patentName" min-width="120" />
      <vxe-column title="保护技术点" field="protectionPoint" min-width="120" />
      <vxe-column title="许可人或转让人" field="licensorPeople" min-width="120" />
      <vxe-column title="许可或转让时间" field="licensorDate" min-width="120" />
      <vxe-column title="费用" field="costs" min-width="120" />
    </vxe-table>
  </div>
  <Pagination
    size="small"
    :total="total"
    v-model:page="queryParams.pageNo"
    v-model:limit="queryParams.pageSize"
    @pagination="getList"
  />
  <OperationForm ref="operationFormRef" @success="getList()" />
</template>

<script lang="ts" setup>
import { OperationApi } from '@/api/patent/operation'
import OperationForm from './OperationForm.vue'

const operationFormRef = ref()

const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  originalType: 0
})

const list = ref<any[]>()
const total = ref(0)
const loading = ref(false)

const getList = async () => {
  loading.value = true
  try {
    const data = await OperationApi.getOperationPage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

onMounted(() => {
  handleList()
})
</script>
