<template>
  <ActivitiesContainer :basics-info="props.basicsInfo!" :node-list="[]">
    <template #activities-table>
      <Knowledge :basicsId="props.basicsInfo?.id" />
    </template>
  </ActivitiesContainer>
  
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics';
import Knowledge from '@/views/project/ArchiveCenter/knowledge/index.vue'
import ActivitiesContainer from '../components/ActivitiesContainer.vue';

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  }
})

</script>


