import request from '@/config/axios'

// 项目决策评审模板 VO
export interface DecisionTemplateVO {
  id: number // 评审模板ID
  category: number // 评审分类
  aspect: string // 项目决策评审方面
  attention: string // 需要关注的内容
  problem: string // 考虑的问题
  director: number[] // 评审负责人
}

// 项目决策评审模板 API
export const DecisionTemplateApi = {
  // 查询项目决策评审模板分页
  getDecisionTemplatePage: async (params: any) => {
    return await request.get({ url: `/project/decision-template/page`, params })
  },

  // 查询项目决策评审模板详情
  getDecisionTemplate: async (id: number) => {
    return await request.get({ url: `/project/decision-template/get?id=` + id })
  },

  // 新增项目决策评审模板
  createDecisionTemplate: async (data: DecisionTemplateVO) => {
    return await request.post({ url: `/project/decision-template/create`, data })
  },

  // 修改项目决策评审模板
  updateDecisionTemplate: async (data: DecisionTemplateVO) => {
    return await request.put({ url: `/project/decision-template/update`, data })
  },

  // 删除项目决策评审模板
  deleteDecisionTemplate: async (id: number) => {
    return await request.delete({ url: `/project/decision-template/delete?id=` + id })
  },

  // 导出项目决策评审模板 Excel
  exportDecisionTemplate: async (params) => {
    return await request.download({ url: `/project/decision-template/export-excel`, params })
  }
}
