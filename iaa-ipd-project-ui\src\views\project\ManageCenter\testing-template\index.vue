<template>
  <el-row :gutter="10" class="wh-full">
    <el-col :xs="24" :sm="24" :md="24" :lg="4" :xl="4" class="h-full">
      <ContentWrap class="h-full">
        <div class="overflow-auto h-[calc(100vh-130px)]">
          <el-tree
            class="w-full overflow-hidden"
            ref="treeRef"
            :data="getStrDictOptions('project_testing_type')"
            :expand-on-click-node="false"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template #default="{ node }">
              <span>
                <Icon icon="ep:bicycle" />
              </span>
              <span class="ml-8px">{{ node.label }}</span>
            </template>
          </el-tree>
        </div>
      </ContentWrap>
    </el-col>
    <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="20">
      <ContentWrap v-if="queryParams.type">
        <div class="flex">
          <el-button
            type="primary"
            plain
            size="small"
            @click="testingTemplateFormRef?.openForm(queryParams.type)"
          >
            添加记录
          </el-button>
        </div>
        <div class="h-[calc(100vh-205px)]">
          <vxe-table
            height="100%"
            :header-cell-config="{ height: 40 }"
            :cell-config="{ height: 30 }"
            border
            align="center"
            :loading="loading"
            :data="list"
            show-overflow
          >
            <vxe-column title="二级分类" width="150" field="secondaryType">
              <template #default="{ row }">
                <DictTag
                  type="project_testing_secondary_type"
                  :value="row.secondaryType"
                  v-if="row.secondaryType"
                />
                <el-tag type="info" v-else>无</el-tag>
              </template>
            </vxe-column>
            <vxe-column title="序号" width="80" field="sort" />
            <vxe-column title="测试项" width="200" field="name" />
            <vxe-column title="测试要求" width="200" field="demand" />
            <vxe-column title="测试说明" field="accountFor" />
            <vxe-column title="工时(h)" width="120" field="workHours" />
            <vxe-column title="操作" width="150" field="opertion">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="testingTemplateFormRef?.openForm(row.type, row)"
                >
                  修改
                </el-button>
                <el-button type="danger" size="small" link @click="handleDelete(row.id)">
                  删除
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
      <el-empty v-else description="请选择左侧分类" />
    </el-col>
  </el-row>
  <TestingTemplateForm ref="testingTemplateFormRef" @success="handleList" />
</template>

<script lang="ts" setup>
import { getStrDictOptions } from '@/utils/dict'
import { TestingTemplateApi } from '@/api/project/testingtemplate'
import TestingTemplateForm from './TestingTemplateForm.vue'

const treeRef = ref()
const testingTemplateFormRef = ref()
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  type: undefined
})
const list = ref<any[]>([])
const total = ref(0)
const loading = ref(false)
const message = useMessage()

const getList = async () => {
  loading.value = true
  try {
    const res = await TestingTemplateApi.getTestingTemplatePage(queryParams.value)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

/** 触发点击事件 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  queryParams.value.type = row.value
  getList()
}

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

const handleDelete = async (id: number) => {
  loading.value = true
  try {
    await message.confirm('确定删除?')
    await TestingTemplateApi.deleteTestingTemplate(id)
    message.success('删除成功')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>
