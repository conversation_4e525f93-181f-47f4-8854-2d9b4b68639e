<template>
  <InfoDetails formType="approve" />
  <ContentWrap title="状态分布">
    <div class="flex flex-wrap">
      <div id="activities-status" class="pie-item h-150px"></div>
      <div id="problem-status" class="pie-item h-150px"></div>
      <div id="risk-status" class="pie-item h-150px"></div>
      <div id="technical-status" class="pie-item h-150px"></div>
    </div>
  </ContentWrap>

  <ContentWrap title="问题分布">
    <div class="flex flex-wrap">
      <div id="problem-distribution-category" class="pie-item h-150px"></div>
      <div id="problem-distribution-module" class="pie-item h-150px"></div>
      <div id="problem-distribution-level" class="pie-item h-150px"></div>
      <div id="problem-distribution-dept" class="pie-item h-150px"></div>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import InfoDetails from '../details/index.vue'
import { BasicsFlowApi, BasicsFlowVO } from '@/api/bpm/basics'
import { KanbanApi } from '@/api/project/kanban'
import * as PieChart from '@/views/Home/kanban/pie'
import * as echarts from 'echarts'
import { getDictLabel } from '@/utils/dict'

const { query } = useRoute()
const formData = ref<BasicsFlowVO>()

const initProblemDistribution = async () => {
  const res = await KanbanApi.getProblemDistribution({ basicsId: formData.value?.basicsId })
  await nextTick()
  showPieChart(
    '分类统计',
    'problem-distribution-category',
    'category',
    res.category,
    'project_problem_category'
  )
  showPieChart(
    '责任模块',
    'problem-distribution-module',
    'module',
    res.module,
    'project_problem_module'
  )
  showPieChart(
    '等级分布',
    'problem-distribution-level',
    'level',
    res.level,
    'project_problem_level'
  )
  showPieChart(
    '参与人员',
    'problem-distribution-dept',
    'department',
    res.department,
    'project_problem_proposing_department'
  )
  showPieChart(
    '活动',
    'activities-status',
    'status',
    res.activitiesStatus,
    'project_activities_status'
  )
  showPieChart('问题', 'problem-status', 'status', res.problemStatus, 'project_activities_status')
  showPieChart('风险', 'risk-status', 'status', res.riskStatus, 'project_risk_status')
  showPieChart(
    '技术评审',
    'technical-status',
    'status',
    res.technicalStatus,
    'project_activities_status'
  )
}

const showPieChart = async (
  title: string,
  id: string,
  type: string,
  data: any[],
  dictType: string
) => {
  PieChart.refreshChart(
    data.map((item) => {
      return {
        name: getDictLabel(dictType, item[type]),
        value: item.count
      }
    }),
    echarts.init(document.getElementById(id)!),
    title
  )
}

onMounted(async () => {
  if (query.id) {
    const res = await BasicsFlowApi.getBasicsFlowByProcessInstanceId(query.id as string)
    formData.value = res
    initProblemDistribution()
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-card__header),
:deep(.el-card__body) {
  padding: 10px;
}

.pie-item{
  width: 25%;
}

@media screen and (max-width: 768px) {
  .pie-item{
    width: 100% !important;
    height: 200px !important;
  }
}
</style>
