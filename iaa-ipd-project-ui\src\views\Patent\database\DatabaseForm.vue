<template>
  <!-- 对话框(添加 / 修改) -->
  <NoModalDrawer :title="dialogTitle" v-model="visible" size="60%">
    <template #header>
      <div class="flex justify-between">
        <div class="text-white font-bold text-17px">{{ dialogTitle }}</div>
        <div class="flex" v-if="formType === 'edit'">
          <el-tooltip content="修改">
            <el-button link :loading="loading" @click="onModify()">
              <Icon :icon="!edit ? 'ep:edit' : 'ep:check'" :size="22" />
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除">
            <el-button link :loading="loading" @click="handleDelete">
              <Icon icon="ep:delete" :size="22" />
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </template>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="100px"
      class="mt-10px custom-form"
      size="small"
      :disabled="!edit"
    >
      <el-row>
        <el-col :span="24">
          <CardTitle title="查询词" />
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="专利搜索词" prop="word">
            <el-input v-model="formData.word" placeholder="请输入专利搜索词" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="formData.startDate"
              value-format="YYYY-MM-DD"
              placeholder="开始时间"
              class="!w-100%"
              @change="changeDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="formData.endDate"
              value-format="YYYY-MM-DD"
              placeholder="结束时间"
              class="!w-100%"
              :disabled-date="disabledDate"
              @change="changeDate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <CardTitle title="著录项" />
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="专利类型" prop="patentType">
            <el-select
              v-model="formData.patentType"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16" :xs="24" :sm="12" :md="12">
          <el-form-item label="专利名称" prop="patentName">
            <el-input v-model="formData.patentName" placeholder="请输入专利名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="专利号" prop="patentNo">
            <el-input v-model="formData.patentNo" placeholder="请输入专利号" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="公开号" prop="publicNo">
            <el-input v-model="formData.publicNo" placeholder="请输入公开号" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="申请日期" prop="applicationDate">
            <el-date-picker
              v-model="formData.applicationDate"
              value-format="YYYY-MM-DD"
              placeholder="申请日期"
              class="!w-100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="授权日期" prop="authorizationDate">
            <el-date-picker
              v-model="formData.authorizationDate"
              value-format="YYYY-MM-DD"
              placeholder="授权日期"
              class="!w-100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="权利人" prop="ownership">
            <el-input v-model="formData.ownership" placeholder="请输入权利人" />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="发明人" prop="inventor">
            <el-input v-model="formData.inventor" placeholder="请输入发明人" />
          </el-form-item>
        </el-col>

        <el-col :span="8" :xs="24" :sm="12" :md="12">
          <el-form-item label="法律状态" prop="legalStatus">
            <el-select
              v-model="formData.legalStatus"
              placeholder="请选择法律状态"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <CardTitle title="摘要" />
        </el-col>
        <el-col :span="24">
          <el-form-item label="摘要" prop="abstracts">
            <el-input
              v-model="formData.abstracts"
              type="textarea"
              :rows="5"
              placeholder="请输入摘要"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form label-width="100px" class="mt-10px custom-form" size="small">
      <el-tabs v-model="currentTab" @tab-change="onTableChange">
        <el-tab-pane label="功能问题" name="function">
          <el-form-item label="功能点" prop="functionPoint">
            <tags-input v-model="formData.functionPoint" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="技术点" prop="technologyPoint">
            <tags-input v-model="formData.technologyPoint" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="解决的问题" prop="solvedProblem">
            <el-input
              v-model="formData.solvedProblem"
              type="textarea"
              :rows="5"
              placeholder="请输入解决的问题"
              :disabled="!edit"
            />
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="附图" name="imgs">
          <UploadImgs v-model="formData.imgs!" height="80px" width="80px" :disabled="!edit" />
        </el-tab-pane>
        <el-tab-pane label="保护范围" name="scope">
          <el-form-item label="整机" prop="machineType">
            <tags-input v-model="formData.machineType" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="组件" prop="component">
            <tags-input v-model="formData.component" :disabled="!edit" />
          </el-form-item>
          <el-form-item label="零件" prop="part">
            <tags-input v-model="formData.part" :disabled="!edit" />
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="同族专利" name="same">
          <GroupInfo
            ref="groupRef"
            :table-type="1"
            :form-type="formType"
            :belongs-id="formData.id"
          />
        </el-tab-pane>
        <el-tab-pane label="附件" name="attachment">
          <AttachmentInfo
            ref="attachmentRef"
            :table-type="1"
            :form-type="formType"
            :belongs-id="formData.id"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer v-if="formType === 'create'">
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="loading">确 定</el-button>
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </template>
  </NoModalDrawer>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { DatabaseApi } from '@/api/patent/database'
import TagsInput from '../components/TagsInput.vue'
import AttachmentInfo from '../components/AttachmentInfo.vue'
import GroupInfo from '../components/GroupInfo.vue'
import moment from 'moment'

const visible = ref(false)
const edit = ref(false)
const currentTab = ref('function')

const formData = ref({
  id: undefined,
  word: undefined,
  startDate: undefined,
  endDate: undefined,
  patentNo: undefined,
  publicNo: undefined,
  patentName: undefined,
  patentType: undefined,
  ownership: undefined,
  inventor: undefined,
  applicationDate: undefined,
  authorizationDate: undefined,
  families: undefined,
  legalStatus: 0,
  abstracts: undefined,
  attachmentIds: [] as string[],
  machineType: [] as string[],
  component: undefined,
  part: undefined,
  technologyPoint: undefined,
  solvedProblem: undefined,
  functionPoint: undefined,
  imgs: undefined
})
const formRules = reactive({
  publicNo: [{ required: true, message: '请输入公开号', trigger: 'blur' }],
  patentName: [{ required: true, message: '请输入专利名称', trigger: 'blur' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()
const attachmentRef = ref()
const groupRef = ref()

const openForm = async (dataId?: number) => {
  visible.value = true
  if (dataId) {
    formType.value = 'edit'
    dialogTitle.value = '修改专利信息'
    const data = await DatabaseApi.getDatabase(dataId)
    // 时间字段转换：时间戳 -> YYYY-MM-DD 字符串
    formData.value = data
    edit.value = false
    onTableChange()
  } else {
    formType.value = 'create'
    dialogTitle.value = '添加专利信息'
    edit.value = true
    refresh()
  }
}

const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()
  loading.value = true
  try {
    const attachments = await unref(attachmentRef)?.getData()
    const groups = await unref(groupRef)?.getData()
    await DatabaseApi.createDatabase({
      ...formData.value,
      attachments,
      groups
    })
    message.success('创建成功')

    visible.value = false
    edit.value = false
    currentTab.value = 'functions'
    emit('success')
  } finally {
    loading.value = false
  }
}

const onModify = async () => {
  if (!edit.value) {
    edit.value = true
    return
  } else {
    await DatabaseApi.updateDatabase(formData.value)
    message.success('修改成功')
    edit.value = false
    emit('success')
  }
}

/** 删除按钮操作 */
const handleDelete = async () => {
  // 删除的二次确认
  await message.delConfirm()
  // 发起删除
  await DatabaseApi.deleteDatabase(formData.value.id!)
  message.success('删除成功')
  // 刷新列表并保持滚动位置
  emit('success')
}

/** 页签变化 */
const onTableChange = async () => {
  await nextTick()
  switch (currentTab.value) {
    case 'attachment':
      attachmentRef.value?.onList()
      break
    case 'same':
      groupRef.value?.onList()
  }
}

const disabledDate = (current: any) => {
  return moment(current).isBefore(moment(formData.value?.startDate))
}

const changeDate = () => {
  const { startDate, endDate } = formData.value

  if (startDate && endDate && moment(endDate).isBefore(moment(startDate))) {
    formData.value.endDate = undefined
    message.error('结束时间不能早于开始时间')
  }
}

const refresh = () => {
  formData.value = {
    id: undefined,
    word: undefined,
    startDate: undefined,
    endDate: undefined,
    patentNo: undefined,
    publicNo: undefined,
    patentName: undefined,
    patentType: undefined,
    ownership: undefined,
    inventor: undefined,
    applicationDate: undefined,
    authorizationDate: undefined,
    families: undefined,
    legalStatus: 0,
    abstracts: undefined,
    attachmentIds: [] as string[],
    machineType: [] as string[],
    component: undefined,
    part: undefined,
    technologyPoint: undefined,
    solvedProblem: undefined,
    functionPoint: undefined,
    imgs: undefined
  }
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header.is-top) {
  background-color: #f1f1f1;
}
:deep(.card-title) {
  padding: 5px;
  display: block;
}
</style>
