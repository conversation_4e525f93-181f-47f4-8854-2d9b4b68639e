<template>
  <Dialog
    v-if="hasDialog"
    :title="fileName"
    v-model="visible"
    width="80%"
    class="file-viewer-dialog"
  >
    <ContentWrapper v-if="config.documentType" />
  </Dialog>
  <ContentWrapper v-else-if="!hasDialog && config.documentType" />
</template>

<script setup lang="tsx">
import { useUserStore } from '@/store/modules/user'
import { DocumentEditor } from '@onlyoffice/document-editor-vue'
import { ElWatermark } from 'element-plus'
import * as FileApi from '@/api/infra/file'
import { propTypes } from '@/utils/propTypes'

const { getUser } = useUserStore()

const props = defineProps({
  download: propTypes.bool.def(false),
  hasDialog: propTypes.bool.def(true)
})

const type = ref('')
const otherPath = ref('')
const previewUrl = import.meta.env.VITE_ONLY_OFFICE_URL // 修正拼写错误

const file = ref<any>()
const fileName = ref<string>('')

const visible = ref(false)
const config = ref({
  document: {
    fileType: '',
    key: '',
    title: '',
    url: '',
    permissions: {
      chat: false,
      comment: false,
      copy: false,
      print: false,
      download: props.download
    }
  },
  documentType: '',
  editorConfig: {
    callbackUrl: `${window.location.origin}/callback`, // 设置回调 URL
    lang: 'zh',
    mode: 'view',
    user: {
      group: getUser.deptId,
      id: getUser.id,
      image: getUser.avatar,
      name: getUser.nickname
    }
  }
})

const font = reactive({
  color: 'rgba(0, 0, 0, .15)'
})

const open = async (infraFileId: number, tempFileName: string) => {
  try {
    file.value = await FileApi.get(infraFileId)
    if (!file.value || !file.value.url) {
      console.error('文件信息获取失败或文件 URL 为空')
      return
    }
    visible.value = true
    fileName.value = tempFileName
    show()
  } catch (error) {
    console.error('打开文件时出错:', error)
  }
}

const show = () => {
  const url = file.value.url
  const fileType = url.split('.').pop() || ''
  if (['docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'pdf'].includes(fileType.toLowerCase())) {
    config.value.document = {
      fileType,
      key: `file-${file.value.id}`,
      title: fileName.value,
      url,
      permissions: {
        chat: false,
        comment: false,
        copy: false,
        print: false,
        download: props.download
      }
    }
    config.value.editorConfig.callbackUrl = `${window.location.origin}/callback` // 确保每次更新
    type.value = 'office'
  } else {
    // todo 先这么解决
    otherPath.value = 'http://oa.iaa360.cn:13135/onlinePreview?url=' + encodeURIComponent(btoa(url))
  }
  config.value.documentType = getDocumentType(fileType)
}

const getDocumentType = (fileType: string): string => {
  switch (fileType.toLowerCase()) {
    case 'docx':
    case 'doc':
      return 'word'
    case 'xlsx':
    case 'xls':
      return 'cell'
    case 'pptx':
    case 'ppt':
      return 'slide'
    case 'pdf':
      return 'pdf'
    default:
      return 'word' // 默认类型
  }
}

const ContentWrapper = () => (
  <ElWatermark font={font} content={`IAA-国际香氛-${getUser.nickname}`}>
    <div class="file-viewer-container">
      {type.value === 'office' ? (
        <DocumentEditor
          id="docEditor"
          documentServerUrl={previewUrl}
          config={config.value}
          onDocumentReady={onDocumentReady}
        />
      ) : (
        <iframe id="myIframe" src={otherPath.value} class="h-full w-full" />
      )}
    </div>
  </ElWatermark>
)

defineExpose({ open })

const onDocumentReady = () => {
  console.log('Document is ready')
}
</script>
