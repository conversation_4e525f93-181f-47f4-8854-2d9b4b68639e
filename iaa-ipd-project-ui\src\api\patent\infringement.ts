import request from '@/config/axios'

export interface infringementVO {
  id: any // 主键
  submittedPeople: any
  customers: any
  productModel: any
  attachmentIds: any
  targetPatent: any
  targetPatentInformation: any
  targetPatentPoints: any
  targetPatentDrawings: any
  analysisReport: any
  analysisReportAttachment: any
  avoidanceScheme: any
  isContinue: any
  avoidanceRemark: any
  crossLicensing: any
  followPerson: any
  costsCycles: any
  patentApplication: any
}

export const infringementApi = {
  /** 分页获取产品专利侵权分析 */
  getProductInfringementPage: (data: any) => {
    return request.post({ url: '/patent/product-infringement/page', data })
  },
  /** 创建产品专利侵权分析 */
  createProductInfringement: (data: any) => {
    return request.post({ url: '/patent/product-infringement/create', data })
  },
  /** 更新产品专利侵权分析 */
  updateProductInfringement: (data: infringementVO) => {
    return request.post({ url: '/patent/product-infringement/update', data })
  },
  /** 删除产品专利侵权分析 */
  deleteProductInfringement: (id: number) => {
    return request.get({ url: `/patent/product-infringement/delete/${id}`})
  },
  /** 查询知识库详情 */
  getProductInfringement: async (id: any) => {
    return await request.get({ url: `/patent/product-infringement/get?id=` + id })
  },
}