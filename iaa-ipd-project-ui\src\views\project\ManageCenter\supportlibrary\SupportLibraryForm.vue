<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="角色" prop="role">
        <el-select v-model="formData.role">
          <el-option
            v-for="item in getStrDictOptions('project_team_role')"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人员" prop="userIds">
        <UserAvatarList
          v-model="formData.userIds!"
          :user-list="props.userList"
          :size="28"
          :limit="5"
          :add="true"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { SupportLibraryApi, SupportLibraryVO } from '@/api/project/supportlibrary'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user'
import { getStrDictOptions } from '@/utils/dict'

/** 项目支持人员库 表单 */
defineOptions({ name: 'SupportLibraryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  role: undefined,
  userIds: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

const props = defineProps({
  userList: propTypes.oneOfType<UserVO[]>([]).isRequired
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SupportLibraryApi.getSupportLibrary(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SupportLibraryVO
    if (formType.value === 'create') {
      await SupportLibraryApi.createSupportLibrary(data)
      message.success(t('common.createSuccess'))
    } else {
      await SupportLibraryApi.updateSupportLibrary(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    role: undefined,
    userIds: undefined
  }
  formRef.value?.resetFields()
}
</script>
