import request from '@/config/axios'

export const CostApi = {
  /** 获取成本列表 */
  getCostList: (basicsId: number) => {
    return request.get({ url: `project/cost/get/${basicsId}` })
  },
  /** 保存成本 */
  saveCost: (data: any) => {
    return request.post({ url: `project/cost/save`, data })
  },
  /** 获取成本费率 */
  getCostRateList: () => {
    return request.get({ url: `project/cost/get-rate` })
  },
  /** 保存成本费率 */
  saveCostRate: (data: any) => {
    return request.post({ url: `project/cost/save-rate`, data })
  },
  /** 查询项目人工成本 */
  getArtificialCost: (basicsId: number) => {
    return request.get({ url: `project/cost/get-artificial-cost/${basicsId}` })
  }
}
