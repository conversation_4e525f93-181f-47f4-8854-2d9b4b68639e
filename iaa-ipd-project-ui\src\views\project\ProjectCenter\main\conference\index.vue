<template>
  <ActivitiesContainer
    :basics-info="props.basicsInfo!"
    :node-list="baseNode"
    v-model:current-node="currentNode"
  >
    <template #activities-table>
      <div class="h-full">
        <div class="p-t-10px p-b-10px" style="height: calc(100% - 180px)">
          <vxe-table
            height="100%"
            class="w-100%"
            :header-cell-style="{
              padding: '0',
              height: '2vw',
              fontSize: '1rem',
              backgroundColor: '#fafafa',
              color: 'var(--primary-text-color)'
            }"
            :row-style="{
              cursor: 'pointer'
            }"
            :cell-style="{
              padding: '0',
              height: '2vw',
              fontSize: '1rem',
              color: 'var(--primary-text-color)'
            }"
            round
            auto-resize
            show-overflow
            :data="attachmentList"
            align="center"
            border
            :loading="loading"
          >
            <vxe-column title="文件名" field="name" min-width="200" align="left">
              <template #default="{ row }">
                <el-link
                  type="primary"
                  @click="attachmentPreviewRef?.openForm(row.name, row.processInstanceId)"
                >
                  {{ row.name }}
                </el-link>
              </template>
            </vxe-column>
            <vxe-column title="版本" field="currentVersion" width="60" />
            <vxe-column title="审签状态" field="approvalStatus" width="90">
              <template #default="{ row }">
                <DictTag type="project_target_approval_status" :value="row.approvalStatus" />
              </template>
            </vxe-column>
            <vxe-column
              title="审核通过时间"
              field="approvalTime"
              :formatter="dateFormatter3"
              width="120"
            />
            <vxe-column title="操作" width="100px" fixed="right" align="center">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  size="small"
                  v-if="![1, 0].includes(row.approvalStatus) && row.creator === getUser.id"
                  @click="fileUploadAnewRef?.openForm(row)"
                >
                  重传
                </el-button>
                <el-button
                  type="primary"
                  link
                  size="small"
                  v-if="row.approvalStatus != 1 && row.creator === getUser.id"
                  @click="delAttachment(row)"
                >
                  删除
                </el-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <div class="h-160px">
          <FileUpload
            ref="fileUploadRef"
            :category="`conference-${props.basicsInfo?.id}-`"
            :dynamic-id="currentNode"
            @success="onListAttachment"
            :disabled="
              !(
                getRolePermission('pm', getUser.id) ||
                getRolePermission('pqa', getUser.id) ||
                getUser.id == 1
              )
            "
          />
        </div>
      </div>
    </template>
  </ActivitiesContainer>
  <AttachmentPreview ref="attachmentPreviewRef" />
  <FileUploadAnew
    :category="`conference-${props.basicsInfo?.id}-`"
    :dynamic-id="currentNode"
    ref="fileUploadAnewRef"
    @success="onListAttachment"
  />
</template>

<script lang="ts" setup>
import { BasicsVO } from '@/api/project/basics'
import ActivitiesContainer from '../components/ActivitiesContainer.vue'
import { AttachmentApi, AttachmentRespVO } from '@/api/project/attachment'
import FileUpload from '../components/FileUpload.vue'
import AttachmentPreview from '../components/AttachmentPreview.vue'
import FileUploadAnew from '../components/FileUploadAnew.vue'
import { dateFormatter3 } from '@/utils/formatTime'
import { ElMessageBox } from 'element-plus'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'
import { ActivitiesTemplateApi } from '@/api/project/activitiestemplate'
import { useUserStore } from '@/store/modules/user'
import { getRolePermission } from '../util/permission'

const props = defineProps({
  basicsInfo: {
    type: Object as PropType<BasicsVO>
  },
  templateCategory: {
    type: String,
    default: ''
  }
})
const baseNode = ref<any[]>([])
const currentNode = ref<number>(undefined as unknown as number) //当前节点
const attachmentList = ref<AttachmentRespVO[]>([])
const attachmentPreviewRef = ref()
const fileUploadAnewRef = ref()
const message = useMessage()
const loading = ref(false)
const { getUser } = useUserStore()

/** 获取附件列表 */
const onListAttachment = async () => {
  loading.value = true
  try {
    const res = await AttachmentApi.getAttachmentList({
      category: `conference-${props.basicsInfo?.id}-`,
      dynamicId: currentNode.value
    })
    attachmentList.value = res
  } finally {
    loading.value = false
  }
}

/** 删除附件 */
const delAttachment = async (row: any) => {
  // 二次确认
  const { value } = await ElMessageBox.prompt('请输入删除原因', '删除输出物', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
    inputErrorMessage: '删除原因不能为空'
  })
  if (row.approvalStatus == 2) {
    await AttachmentApi.delAttachment({
      id: row.id,
      value
    })
  } else {
    await ProcessInstanceApi.cancelProcessInstanceByStartUser(row.processInstanceId, value)
  }
  message.success('删除成功')
  onListAttachment()
}

/** 获取项目基础节点 */
const onListBaseNode = async () => {
  baseNode.value = []
  const res = await ActivitiesTemplateApi.listActivitiesTemplate({
    parentId: 0,
    categoryId: props.templateCategory
  })
  if (res.length > 0) {
    currentNode.value = res[0].id
  }
  for (const item of res) {
    baseNode.value.push({ id: item.id, label: item.name })
  }
}

watch(
  () => props.templateCategory,
  () => {
    if (props.templateCategory) {
      onListBaseNode()
    }
  },
  { immediate: true }
)

watch(
  () => [currentNode.value, props.basicsInfo?.id],
  () => {
    if (currentNode.value && props.basicsInfo?.id) {
      onListAttachment()
    }
  },
  { immediate: true }
)
</script>
