<!-- DatabaseDialog.vue -->
<template>
  <Dialog v-model="visible" title="选择数据库专利信息" width="60%">
    <div class="h-[calc(100vh-250px)]">
      <div class="h-[calc(100%-50px)]">
        <vxe-table
          ref="tableRef"
          height="100%"
          @cell-dblclick="cellClickEvent"
          :header-cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            backgroundColor: '#fafafa',
            color: 'var(--primary-text-color)'
          }"
          :row-style="{
            cursor: 'pointer'
          }"
          :cell-style="{
            padding: '0',
            height: '2.5rem',
            fontSize: '.9rem',
            color: 'var(--primary-text-color)',
            cursor: 'pointer'
          }"
          :data="list"
          border
          align="center"
          show-overflow
        >
          <vxe-column title="知识产权搜索词" align="left" field="word" min-width="240">
            <template #header>
              <div>知识产权搜索词</div>
              <el-input
                v-model="queryParams.word"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="开始时间" width="120" :formatter="dateFormatter4" field="startDate" />
          <vxe-column title="结束时间" width="120" :formatter="dateFormatter4" field="endDate" />
          <vxe-column title="专利号" width="120" field="patentNo" />
          <vxe-column title="公开号" width="120" field="publicNo" />
          <vxe-column title="专利名称" width="120" field="patentName" />
          <vxe-column title="专利类型" align="left" field="patentType" width="90">
            <template #header>
              <div>专利类型</div>
              <el-select
                v-model="queryParams.patentType"
                @change="handleList"
                placeholder="选择专利类型"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.PATENT_TYPE" :value="row.patentType" />
            </template>
          </vxe-column>
          <vxe-column title="权力人" width="120" field="ownership" />
          <vxe-column title="发明人" width="120" field="inventor" />
          <vxe-column
            title="申请日期"
            width="120"
            :formatter="dateFormatter4"
            field="applicationDate"
          />
          <vxe-column
            title="授权日期"
            width="120"
            :formatter="dateFormatter4"
            field="authorizationDate"
          />
          <vxe-column title="同族专利" width="120" field="families" />
          <vxe-column title="法律状态" align="left" field="legalStatus" width="90">
            <template #header>
              <div>法律状态</div>
              <el-select
                v-model="queryParams.legalStatus"
                @change="handleList"
                placeholder="选择法律状态"
                style="width: 100%"
                size="small"
                clearable
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.LEGAL_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
            <template #default="{ row }">
              <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="row.legalStatus" />
            </template>
          </vxe-column>
          <vxe-column title="摘要" width="120" field="abstracts" />
          <vxe-column title="保护的整机类型" width="120" field="machineType" />
          <vxe-column title="保护的组件类型" width="120" field="component" />
          <vxe-column title="保护的零件类型" width="120" field="part" />
          <vxe-column title="附件ID" width="120" field="attachmentIds" />
          <vxe-column title="技术点" width="120" field="technologyPoint" />
          <vxe-column title="解决的问题" width="120" field="solvedProblem" />
          <vxe-column title="功能点" width="120" field="functionPoint" />
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DatabaseApi, DatabaseVO } from '@/api/patent/database'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter4 } from '@/utils/formatTime'

const visible = ref(false)
const tableRef = ref()
const total = ref(0)
const list = ref<DatabaseVO[]>([]) // 列表的数据
const loading = ref(true) // 列表的加载中
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  patentType: undefined,
  legalStatus: undefined,
  word: undefined
})
// 筛选处理
const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}
const getList = async () => {
  loading.value = true
  try {
    const data = await DatabaseApi.getDatabasePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

//双击编辑
const cellClickEvent: any = ({ row, column }) => {
  //触发父组件事件
  emit('success', row)
  //关闭弹窗
  visible.value = false
}

// 控制弹窗显示/隐藏
//打开弹窗
const openDialog = async () => {
  visible.value = true
  getList()
}

// 定义对外暴露的方法和事件
defineExpose({ openDialog })
const emit = defineEmits(['success'])
</script>
