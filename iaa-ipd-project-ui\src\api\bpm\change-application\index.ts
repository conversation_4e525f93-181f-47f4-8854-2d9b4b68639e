import request from '@/config/axios'

export const ChangeApplicationApi = {
  /** 创建变更申请流程 */
  createFlow: (data: any) => {
    return request.post({ url: '/bpm/change-application/create', data })
  },
  /** 根据流程ID获取申请单信息 */
  getFlow: (processInstanceId: string) => {
    return request.post({ url: '/bpm/change-application/get/' + processInstanceId })
  },
  /** 分页获取申请单信息 */
  pageFlow: (data: any) => {
    return request.post({ url: '/bpm/change-application/page', data })
  }
}
