<template>
  <div class="p-l-10px h-full overflow-auto" ref="containerRef">
    <!-- 项目分布与占比 -->
    <el-card shadow="never">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="distributionToolbarRef">
          <template #buttons>
            <CardTitle title="项目分布与占比" />
          </template>
        </vxe-toolbar>
      </template>
      <div class="flex w-full justify-between" v-loading="distributionLoading">
        <vxe-table
          ref="distributionTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-style="{ padding: 0, fontSize: '.8rem' }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="distributionTable"
          :export-config="{ type: 'xlsx', isMerge: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          class="w-40%"
          align="center"
          height="220px"
          :footerMethod="footerMethodSum"
          show-footer
        >
          <vxe-column field="level" title="等级" width="80" />
          <vxe-column
            v-for="category in categoryList.filter((item) => item.templateCategory)"
            :key="category.id"
            :field="category.id"
            :title="category.name.replace('项目', '')"
          />
          <vxe-column field="total" title="合计" width="60" />
        </vxe-table>
        <div id="distribution-type" class="w-30% h-220px"></div>
        <div id="distribution-level" class="w-30% h-220px"></div>
      </div>
    </el-card>

    <!-- 项目整体进度 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="progressToolbarRef">
          <template #buttons>
            <CardTitle title="项目整体进度" />
          </template>
          <template #tools>
            <div class="flex items-center">
              <el-radio-group
                size="small"
                v-model="progressDeviationType"
                @change="onListProgressDeviationList"
              >
                <el-radio-button label="月" value="month" />
                <el-radio-button label="年" value="year" />
              </el-radio-group>
              <el-date-picker
                v-model="progressDeviationDate"
                :type="progressDeviationType"
                placeholder="选择月"
                size="small"
                :clearable="false"
                @change="onListProgressDeviationList"
              />
            </div>
          </template>
        </vxe-toolbar>
      </template>

      <div class="flex" v-loading="progressDeviationLoading">
        <vxe-table
          ref="progressTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-style="{ padding: 0, fontSize: '.8rem' }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="progressDeviationTable"
          :export-config="{ type: 'xlsx', isMerge: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          class="w-50%"
          align="center"
          height="220px"
          show-footer
          :footer-method="footerMethodAvg"
        >
          <vxe-column field="level" title="等级" width="60" />
          <vxe-column
            v-for="category in categoryList.filter((item) => item.templateCategory)"
            :key="category.id"
            :field="category.id"
            :title="category.name.replace('项目', '')"
          />
          <vxe-column field="total" title="均值" width="60" />
        </vxe-table>
        <div class="flex h-220px w-50% text-.8rem color-[var(--secondary-text-color)]">
          <div class="w-120px text-center bg-#f8f8f9" style="height: 220px; line-height: 220px"
            >公式说明</div
          >
          <div class="h-100%">
            <div class="h-33% border-1px border-solid border-#f1f1f1 p-10px">
              ADCP偏差天数=ADCP实际完成时间-ADCP计划完成时间。未完结则和当前日期计算
            </div>
            <div class="h-33% border-1px border-solid border-#f1f1f1 p-10px">
              自研类A1等级项目ADCP偏差天数=（自研类A1等级项目1ADCP偏差天数+自研类A1等级项目2ADCP偏差天数+...）/自研类A1等级项目总数其他同样类型以此类推
            </div>
            <div class="h-33% border-1px border-solid border-#f1f1f1 p-10px">
              自研类项目类型ADCP平均偏差天数=自研类项目ADCP偏差天数总和/自研类项目总数项目等级ADCP平均偏差天数以此类推
            </div>
          </div>
        </div>
      </div>
      <div class="flex h-200px w-100%" v-loading="progressDeviationLoading">
        <div id="deviation-type" class="w-50% h-200px"></div>
        <div id="deviation-level" class="w-50% h-200px"></div>
      </div>
    </el-card>

    <!-- 项目质量 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="problemToolbarRef">
          <template #buttons>
            <CardTitle title="项目质量" />
          </template>
          <template #tools>
            <div class="flex items-center">
              <el-radio-group
                size="small"
                v-model="problemCountType"
                @change="onListProblemCountList"
              >
                <el-radio-button label="月" value="month" />
                <el-radio-button label="年" value="year" />
              </el-radio-group>
              <el-date-picker
                v-model="problemCountDate"
                :type="problemCountType"
                placeholder="选择月"
                size="small"
                :clearable="false"
                @change="onListProblemCountList"
              />
            </div>
          </template>
        </vxe-toolbar>
      </template>

      <div class="flex" v-loading="problemCountLoading">
        <vxe-table
          ref="problemTableRef"
          :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-style="{ padding: 0, fontSize: '.8rem' }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :data="problemCountTable"
          :export-config="{ type: 'xlsx', isMerge: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          class="w-50%"
          align="center"
          height="220px"
          show-footer
          :footer-method="footerMethodAvg"
        >
          <vxe-column field="level" title="等级" width="60" />
          <vxe-column
            v-for="category in categoryList.filter((item) => item.templateCategory)"
            :key="category.id"
            :field="category.id"
            :title="category.name.replace('项目', '')"
          />
          <vxe-column field="total" title="均值" width="60" />
        </vxe-table>
        <div class="flex h-220px w-50% text-.8rem color-[var(--secondary-text-color)]">
          <div class="w-120px text-center bg-#f8f8f9" style="height: 220px; line-height: 220px">
            公式说明
          </div>
          <div class="h-100%">
            <div class="h-33% border-1px border-solid border-#f1f1f1 p-10px">
              单个项目问题数量=其他问题列表问题数量+小试问题列表问题数量+中试问题列表问题数量+TR评审问题数量
            </div>
            <div class="h-33% border-1px border-solid border-#f1f1f1 p-10px">
              自研类A1等级项目平均问题数=（自研类A1等级项目1问题数+自研类A1等级项目2平均问题数+...）/自研类A1等级项目总数其他同样类型以此类推
            </div>
            <div class="h-33% border-1px border-solid border-#f1f1f1 p-10px">
              自研类项目类型平均问题数=自研类项目问题数总和/自研类项目总数项目等级问题数，项目等级以此类推
            </div>
          </div>
        </div>
      </div>
      <div class="flex h-200px w-100%" v-loading="problemCountLoading">
        <div id="problem-type" class="w-50% h-200px"></div>
        <div id="problem-level" class="w-50% h-200px"></div>
      </div>
    </el-card>

    <!-- 职级分布 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar custom export size="mini" ref="officeToolbarRef">
          <template #buttons>
            <CardTitle title="项目开发人员结构" />
          </template>
        </vxe-toolbar>
      </template>
      <vxe-table
        ref="officeTableRef"
        :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :cell-style="{ padding: 0, fontSize: '.8rem' }"
        :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :data="officeLevelList"
        :export-config="{ type: 'xlsx', isMerge: true }"
        :merge-cells="getMergeCell(officeLevelList, 2)"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        class="w-100%"
        align="center"
        height="400px"
        border
        :loading="officeLoading"
      >
        <vxe-column field="level" title="项目等级" :filters="levelFilter" />
        <vxe-column field="projectCount" title="项目数量" />
        <vxe-column field="role" title="涉及角色" :filters="roleFilter">
          <template #default="{ row }">
            {{ getDictLabel('project_team_role', row.role) }}
          </template>
        </vxe-column>
        <vxe-column field="userCount" title="人员数量">
          <template #default="{ row }">
            {{ row.userIds?.length || 0 }}
          </template>
        </vxe-column>
        <vxe-column
          v-for="level in levelUserList"
          :key="level.id"
          :title="getDictLabel('rank_of_officer', level.level)"
        >
          <template #default="{ row }">
            {{ countCommonUsers(level.userIds || [], row.userIds || []) }}
          </template>
        </vxe-column>
      </vxe-table>
    </el-card>

    <!-- 项目交付 -->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="mini" custom export ref="deliveryToolbarRef">
          <template #buttons>
            <CardTitle title="项目交付" />
          </template>
          <template #tools>
            <div class="flex items-center">
              <el-radio-group size="small" v-model="deliveryType" @change="onListDelivery">
                <el-radio-button label="月" value="month" />
                <el-radio-button label="年" value="year" />
              </el-radio-group>
              <el-date-picker
                v-model="deliveryDate"
                :type="deliveryType"
                placeholder="选择月"
                size="small"
                :clearable="false"
                @change="onListDelivery"
              />
            </div>
          </template>
        </vxe-toolbar>
      </template>
      <div class="flex flex-wrap" v-loading="deliveryLoading">
        <vxe-table
          ref="deliveryTableRef"
          border
          :data="scheduleList"
          height="220px"
          class="w-50%"
          scrollbar-always-on
          :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :cell-style="{ padding: 0, fontSize: '.8rem', height: '2rem' }"
          :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
          :export-config="{ type: 'xlsx', isMerge: true }"
          :virtual-y-config="{ enabled: true, gt: 0 }"
          align="center"
          show-overflow
          show-footer
          :footer-method="footerMethodSum"
        >
          <vxe-column title="完成情况" width="120" field="title" />
          <!-- 动态生成的项目类型列 -->
          <vxe-column
            v-for="category in categoryList.filter((item) => item.templateCategory)"
            :key="category.id"
            :field="category.id"
            :title="category.name.replace('项目', '')"
          />
          <vxe-column title="合计" field="total" />
        </vxe-table>
        <div
          v-for="category in categoryList.filter((item) => item.templateCategory)"
          :key="category.id"
          :id="`delivery${category.id}`"
          class="w-25% h-220px"
        ></div>
      </div>
    </el-card>
    <!--项目复盘-->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar custom export size="small" ref="reanalyzeToolbarRef">
          <template #buttons>
            <CardTitle title="项目复盘" />
          </template>
        </vxe-toolbar>
      </template>
      <vxe-table
        ref="reanalyzeTableRef"
        border
        :data="reanalyzeList"
        height="400px"
        class="w-100%"
        scrollbar-always-on
        :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :cell-style="{ padding: 0, fontSize: '.8rem', height: '2rem' }"
        :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :export-config="{ type: 'xlsx', isMerge: true, isColgroup: true }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        align="center"
        show-overflow
        :loading="reanalyzeLoading"
      >
        <vxe-colgroup title="项目报警/暂停信息" filed="group1">
          <vxe-column
            title="项目名称"
            field="name"
            :filters="fillterOptions"
            :filter-method="(el: any) => fillterMethod(el, 'name')"
          >
            <template #filter="{ $panel, column }">
              <input
                class="my-input"
                type="type"
                v-for="(option, index) in column.filters"
                :key="index"
                v-model="option.data"
                @input="$panel.changeOption($event, !!option.data, option)"
                @keyup.enter="$panel.confirmFilter()"
                placeholder="按回车确认筛选"
              />
            </template>
          </vxe-column>
          <vxe-column title="项目类型" field="mold">
            <template #default="{ row }">
              {{ getDictLabel('project_type', row.mold) }}
            </template>
          </vxe-column>
          <vxe-column title="项目平台" field="platform">
            <template #default="{ row }">
              {{ getDictLabel('project_platform', row.platform) }}
            </template>
          </vxe-column>
          <vxe-column title="项目等级" field="level" :filters="levelFilter1">
            <template #default="{ row }">
              {{ getDictLabel('project_level', row.level) }}
            </template>
          </vxe-column>
          <vxe-column
            title="产品经理"
            field="productUser"
            :filters="fillterOptions"
            :filter-method="(el: any) => fillterMethod(el, 'productUser')"
          >
            <template #filter="{ $panel, column }">
              <input
                class="my-input"
                type="type"
                v-for="(option, index) in column.filters"
                :key="index"
                v-model="option.data"
                @input="$panel.changeOption($event, !!option.data, option)"
                @keyup.enter="$panel.confirmFilter()"
                placeholder="按回车确认筛选"
              />
            </template>
          </vxe-column>
          <vxe-column
            title="项目经理"
            field="pmUser"
            :filters="fillterOptions"
            :filter-method="(el: any) => fillterMethod(el, 'pmUser')"
          >
            <template #filter="{ $panel, column }">
              <input
                class="my-input"
                type="type"
                v-for="(option, index) in column.filters"
                :key="index"
                v-model="option.data"
                @input="$panel.changeOption($event, !!option.data, option)"
                @keyup.enter="$panel.confirmFilter()"
                placeholder="按回车确认筛选"
              />
            </template>
          </vxe-column>
          <vxe-column
            title="SE"
            field="seUser"
            :filters="fillterOptions"
            :filter-method="(el: any) => fillterMethod(el, 'seUser')"
          >
            <template #filter="{ $panel, column }">
              <input
                class="my-input"
                type="type"
                v-for="(option, index) in column.filters"
                :key="index"
                v-model="option.data"
                @input="$panel.changeOption($event, !!option.data, option)"
                @keyup.enter="$panel.confirmFilter()"
                placeholder="按回车确认筛选"
              />
            </template>
          </vxe-column>
          <vxe-column title="状态" field="status" />
          <vxe-column title="报警/暂停时间" field="endDate" :formatter="dateFormatter4" />
          <vxe-column title="延迟天数" field="extensionDays" />
        </vxe-colgroup>
        <vxe-colgroup title="报警分析" filed="group2">
          <vxe-column title="原因分析" field="reason" />
          <vxe-column title="采取措施" field="measures" />
          <vxe-column title="后续计划" field="plan" />
        </vxe-colgroup>
      </vxe-table>
    </el-card>
    <!--项目成本-->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar custom export size="small" ref="costToolbarRef">
          <template #buttons>
            <CardTitle title="项目成本" />
          </template>
          <template #tools>
            <el-date-picker
              type="month"
              v-model="costDate"
              @change="onListCost"
              :clearable="false"
            />
          </template>
        </vxe-toolbar>
      </template>
      <vxe-table
        ref="costTableRef"
        border
        :data="costList"
        height="400px"
        class="w-100%"
        scrollbar-always-on
        :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :cell-style="{ padding: 0, fontSize: '.8rem', height: '2rem' }"
        :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :export-config="{ type: 'xlsx', isMerge: true, isColgroup: true }"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        align="center"
        show-overflow
        :loading="costLoading"
      >
        <vxe-column title="项目名称" field="name" />
        <vxe-column title="项目开始时间" field="startDate" :formatter="dateFormatter4" />
        <vxe-column title="ADCP时间" field="adcpDate" :formatter="dateFormatter4" />
        <vxe-column title="计划成本" field="planCost" />
        <vxe-column title="实际成本" field="actualCost" />
        <vxe-column title="计划费用" field="planExpense" />
        <vxe-column title="实际费用" field="actualExpense" />
      </vxe-table>
    </el-card>
    <!--个人提升-->
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="small" ref="ascensionToolbarRef" custom export>
          <template #buttons>
            <CardTitle title="工程师个人效能" />
          </template>
        </vxe-toolbar>
      </template>
      <vxe-table
        ref="ascensionTableRef"
        border
        :data="accensionList"
        height="400px"
        class="w-100%"
        scrollbar-always-on
        :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :cell-style="{ padding: 0, fontSize: '.8rem', height: '1rem' }"
        :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :export-config="{ type: 'xlsx', isMerge: true }"
        :merge-cells="getMergeCell(accensionList, 3)"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        align="center"
        show-overflow
        :loading="ascensionLoading"
      >
        <vxe-column
          title="姓名"
          field="nickname"
          :filters="fillterOptions"
          :filter-method="(el: any) => fillterMethod(el, 'nickname')"
        >
          <template #filter="{ $panel, column }">
            <input
              class="my-input"
              type="type"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @input="$panel.changeOption($event, !!option.data, option)"
              @keyup.enter="$panel.confirmFilter()"
              placeholder="按回车确认筛选"
            />
          </template>
        </vxe-column>
        <vxe-column title="职级" field="officeLevel" />
        <vxe-column title="项目数量" field="projectCount" />
        <vxe-column title="项目等级" field="level" :filters="levelFilter1">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.level) }}
          </template>
        </vxe-column>
        <vxe-column
          title="项目名称"
          field="name"
          :filters="fillterOptions"
          :filter-method="(el: any) => fillterMethod(el, 'name')"
        >
          <template #filter="{ $panel, column }">
            <input
              class="my-input"
              type="type"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @input="$panel.changeOption($event, !!option.data, option)"
              @keyup.enter="$panel.confirmFilter()"
              placeholder="按回车确认筛选"
            />
          </template>
        </vxe-column>
        <vxe-column title="项目开始时间" field="startDate" :formatter="dateFormatter4" />
        <vxe-column title="项目预计结束时间" field="endDate" :formatter="dateFormatter4" />
        <vxe-column title="计划项目周期天数" field="planDays" />
        <vxe-column title="项目偏差天数" field="deviationDays" />
        <vxe-column title="项目角色" field="role" :filters="roleFilter">
          <template #default="{ row }">
            {{ getDictLabel('project_team_role', row.role) }}
          </template>
        </vxe-column>
        <vxe-column title="实际工时(天)" field="sumDays" />
        <vxe-column title="活动平均偏差天数" field="avgDays" />
        <vxe-column title="问题数量" field="problemTotal" />
      </vxe-table>
    </el-card>
    <el-card shadow="never" class="m-t-10px">
      <template #header>
        <vxe-toolbar size="small" custom export ref="ascensionSeToolbarRef">
          <template #buttons>
            <CardTitle title="SE个人效能" />
          </template>
        </vxe-toolbar>
      </template>
      <vxe-table
        ref="ascensionSeTableRef"
        border
        :data="accensionSeList"
        height="400px"
        class="w-100%"
        scrollbar-always-on
        :header-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :cell-style="{ padding: 0, fontSize: '.8rem', height: '2rem' }"
        :footer-cell-style="{ padding: 0, fontSize: '.8rem' }"
        :export-config="{ type: 'xlsx', isMerge: true }"
        :merge-cells="getMergeCell(accensionSeList, 3)"
        :virtual-y-config="{ enabled: true, gt: 0 }"
        align="center"
        show-overflow
        :loading="ascensionLoading"
      >
        <vxe-column
          title="姓名"
          field="nickname"
          :filters="fillterOptions"
          :filter-method="(el: any) => fillterMethod(el, 'nickname')"
        >
          <template #filter="{ $panel, column }">
            <input
              class="my-input"
              type="type"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @input="$panel.changeOption($event, !!option.data, option)"
              @keyup.enter="$panel.confirmFilter()"
              placeholder="按回车确认筛选"
            />
          </template>
        </vxe-column>
        <vxe-column title="职级" field="officeLevel" />
        <vxe-column title="项目数量" field="projectCount" />
        <vxe-column title="项目等级" field="level" :filters="levelFilter1">
          <template #default="{ row }">
            {{ getDictLabel('project_level', row.level) }}
          </template>
        </vxe-column>
        <vxe-column
          title="项目名称"
          field="name"
          :filters="fillterOptions"
          :filter-method="(el: any) => fillterMethod(el, 'name')"
        >
          <template #filter="{ $panel, column }">
            <input
              class="my-input"
              type="type"
              v-for="(option, index) in column.filters"
              :key="index"
              v-model="option.data"
              @input="$panel.changeOption($event, !!option.data, option)"
              @keyup.enter="$panel.confirmFilter()"
              placeholder="按回车确认筛选"
            />
          </template>
        </vxe-column>
        <vxe-column title="项目开始时间" field="startDate" :formatter="dateFormatter4" />
        <vxe-column title="项目预计结束时间" field="endDate" :formatter="dateFormatter4" />
        <vxe-column title="计划项目周期天数" field="planDays" />
        <vxe-column title="项目偏差天数" field="deviationDays" />
        <vxe-column title="项目角色" field="role" :filters="roleFilter">
          <template #default="{ row }">
            {{ getDictLabel('project_team_role', row.role) }}
          </template>
        </vxe-column>
        <vxe-column title="实际工时(天)" field="sumDays" />
        <vxe-column title="项目里程碑平均偏差天数" field="avgDays" />
        <vxe-column title="问题数量" field="problemTotal" />
      </vxe-table>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { KanbanApi } from '@/api/project/kanban'
import { CategoryApi, CategoryVO } from '@/api/project/category'
import { getDictLabel, getStrDictOptions } from '@/utils/dict'
import * as PieChart from './pie'
import * as LineChart from './line'
import * as echarts from 'echarts'
import { dateUtil } from '@/utils/dateUtil'
import { OfficeLevelApi } from '@/api/project/officelevel'
import { dateFormatter4 } from '@/utils/formatTime'
import { ExpenseApi } from '@/api/docking/expense'

//#region 筛选
const roleFilter = ref<any[]>([])
const onListFilter = async () => {
  roleFilter.value = getStrDictOptions('project_team_role').map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
}

const levelFilter = ref<any[]>([])
const levelFilter1 = ref<any[]>([])
const onLevelFilter = async () => {
  levelFilter.value = getStrDictOptions('project_level').map((dict) => {
    return {
      label: dict.label,
      value: dict.label
    }
  })
  levelFilter1.value = getStrDictOptions('project_level').map((dict) => {
    return {
      label: dict.label,
      value: dict.value
    }
  })
}

const fillterMethod: any = ({ option, row }: any, field: string) => {
  return row[field].toLowerCase().includes(option.data.toLowerCase())
}

const fillterOptions = ref<any>([{ data: '' }])

//#endregion

// 获取项目分类信息
const categoryList = ref<CategoryVO[]>([])
const onCategoryList = async () => {
  const res = await CategoryApi.getCategoryList({})
  categoryList.value = res
}
//#region 项目分布
// 获取项目分布信息
interface Distribution {
  level: string
  category: number
  total: number
}
const distributionLoading = ref(false)
const distributionList = ref<Distribution[]>([])
const distributionTable = ref<any[]>([])
const distributionToolbarRef = ref()
const distributionTableRef = ref()

const onListDistributionList = async () => {
  distributionLoading.value = true
  try {
    const res = await KanbanApi.getBasicsDistribution()
    distributionList.value = res
    initDistribution()
  } finally {
    distributionLoading.value = false
  }
}
// 初始化项目分布信息
const initDistribution = async () => {
  const dictList = await getStrDictOptions('project_level')
  distributionTable.value = []
  dictList.forEach((dict) => {
    distributionTable.value.push({
      level: dict.label,
      value: dict.value
    })
  })
  distributionTable.value.forEach((item) => {
    let count = 0
    categoryList.value.forEach((category) => {
      if (category.templateCategory) {
        const row = distributionList.value.find(
          (distribution) =>
            distribution.category == category.id && distribution.level === item.value
        )
        item[category.id] = row?.total || 0
        count += row?.total || 0
      }
    })
    item['total'] = count
  })

  await nextTick()
  // 在initDistribution中调用
  const categoryTotalMap = calculateTotal(distributionList.value, (item) => {
    const category = categoryList.value.find((c) => c.id === item.category)
    return category?.name.replace('项目', '') || ''
  })

  const levelTotalMap = calculateTotal(distributionList.value, (item) => {
    return dictList.find((d) => d.value === item.level)?.label || ''
  })
  // 转换为图表格式
  const categoryTotals = Object.entries(categoryTotalMap).map(([name, total]) => ({
    name,
    value: total
  }))
  const levelTotals = Object.entries(levelTotalMap).map(([level, total]) => ({
    name: level,
    value: total
  }))
  // 使用示例：
  // categoryTotalMap将得到类似 {1: 150, 2: 200} 这样的结构
  var distributiontype = document.getElementById('distribution-type')
  PieChart.refreshChart(categoryTotals, echarts.init(distributiontype), '项目类型占比')

  var distributionLevel = document.getElementById('distribution-level')
  PieChart.refreshChart(levelTotals, echarts.init(distributionLevel), '项目等级占比')
}

//#endregion 项目分布

//#region 项目整体进度
// 获取项目整体进度信息
const progressDeviationLoading = ref(false)
const progressDeviationType = ref<any>('year')
const progressDeviationDate = ref(new Date())
const progressDeviationList = ref<Distribution[]>([])
const progressDeviationTable = ref<any[]>([])
const progressToolbarRef = ref()
const progressTableRef = ref()

const onListProgressDeviationList = async () => {
  progressDeviationLoading.value = true
  try {
    const startDate = dateUtil(progressDeviationDate.value)
      .startOf(progressDeviationType.value)
      .format('YYYY-MM-DD')
    const endDate = dateUtil(progressDeviationDate.value)
      .endOf(progressDeviationType.value)
      .format('YYYY-MM-DD')

    const res = await KanbanApi.getBasicsProgressDeviation({ startDate, endDate })
    progressDeviationList.value = res
    initProgressDeviation()
  } finally {
    progressDeviationLoading.value = false
  }
}
// 初始化项目整体进度信息
const initProgressDeviation = async () => {
  const dictList = await getStrDictOptions('project_level')
  progressDeviationTable.value = []
  dictList.forEach((dict) => {
    progressDeviationTable.value.push({
      level: dict.label,
      value: dict.value
    })
  })
  progressDeviationTable.value.forEach((item) => {
    let count = 0
    let size = 0
    categoryList.value.forEach((category) => {
      if (category.templateCategory) {
        const row = progressDeviationList.value.find(
          (distribution) =>
            distribution.category == category.id && distribution.level === item.value
        )
        item[category.id] = row?.total || 0
        count += row?.total || 0
        size++
      }
    })
    item['total'] = Number((count / size).toFixed(2))
  })
  await nextTick()
  // 在initDistribution中调用
  const categoryTotalMap = calculateAverage(progressDeviationList.value, (item) => {
    const category = categoryList.value.find((c) => c.id === item.category)
    return category?.name.replace('项目', '') || ''
  })

  const levelTotalMap = calculateAverage(progressDeviationList.value, (item) => {
    return dictList.find((d) => d.value === item.level)?.label || ''
  })
  // 使用示例：
  // categoryTotalMap将得到类似 {1: 150, 2: 200} 这样的结构
  var distributiontype = document.getElementById('deviation-type')
  LineChart.refreshChart(
    '项目等级ADCP平均偏差天数',
    Object.entries(levelTotalMap).map(([key]) => key),
    Object.entries(levelTotalMap).map(([, value]) => value),
    echarts.init(distributiontype)
  )

  var distributionLevel = document.getElementById('deviation-level')
  LineChart.refreshChart(
    '项目类型ADCP平均偏差天数',
    Object.entries(categoryTotalMap).map(([key]) => key),
    Object.entries(categoryTotalMap).map(([, value]) => value),
    echarts.init(distributionLevel)
  )
}
//#endregion

//#region 项目质量
const problemCountLoading = ref(false)
const problemCountDate = ref(new Date())
const problemCountType = ref<any>('year')
const problemCountList = ref<Distribution[]>([])
const problemCountTable = ref<any[]>([])
const problemToolbarRef = ref()
const problemTableRef = ref()

const onListProblemCountList = async () => {
  problemCountLoading.value = true
  try {
    const startDate = dateUtil(problemCountDate.value)
      .startOf(problemCountType.value)
      .format('YYYY-MM-DD')
    const endDate = dateUtil(problemCountDate.value)
      .endOf(problemCountType.value)
      .format('YYYY-MM-DD')
    const res = await KanbanApi.getBasicsProblemCount({ startDate, endDate })
    problemCountList.value = res
    initProblemCount()
  } finally {
    problemCountLoading.value = false
  }
}

const initProblemCount = async () => {
  const dictList = await getStrDictOptions('project_level')
  problemCountTable.value = []
  dictList.forEach((dict) => {
    problemCountTable.value.push({
      level: dict.label,
      value: dict.value
    })
  })
  problemCountTable.value.forEach((item) => {
    let count = 0
    let size = 0
    categoryList.value.forEach((category) => {
      if (category.templateCategory) {
        const row = problemCountList.value.find(
          (distribution) =>
            distribution.category == category.id && distribution.level === item.value
        )
        item[category.id] = row?.total || 0
        count += row?.total || 0
        size++
      }
    })
    item['total'] = Number((count / size).toFixed(2))
  })
  await nextTick()
  // 在initDistribution中调用
  const categoryTotalMap = calculateAverage(problemCountList.value, (item) => {
    const category = categoryList.value.find((c) => c.id === item.category)
    return category?.name.replace('项目', '') || ''
  })

  const levelTotalMap = calculateAverage(problemCountList.value, (item) => {
    return dictList.find((d) => d.value === item.level)?.label || ''
  })
  // 使用示例：
  // categoryTotalMap将得到类似 {1: 150, 2: 200} 这样的结构
  var distributiontype = document.getElementById('problem-type')
  LineChart.refreshChart(
    '项目等级平均问题数量',
    Object.entries(levelTotalMap).map(([key]) => key),
    Object.entries(levelTotalMap).map(([, value]) => value),
    echarts.init(distributiontype)
  )

  var distributionLevel = document.getElementById('problem-level')
  LineChart.refreshChart(
    '项目类型类型平均问题数量',
    Object.entries(categoryTotalMap).map(([key]) => key),
    Object.entries(categoryTotalMap).map(([, value]) => value),
    echarts.init(distributionLevel)
  )
}
//#endregion

//#region 项目职级分布
const officeLoading = ref(false)
const officeLevelList = ref<any[]>([])
const levelUserList = ref<any[]>([])
const officeToolbarRef = ref()
const officeTableRef = ref()
const onOfficeLevelList = async () => {
  officeLoading.value = true
  try {
    const res = await KanbanApi.getBasicsOfficeLevel()
    officeLevelList.value = res
    levelUserList.value = await OfficeLevelApi.getOfficeLevelList()
  } finally {
    officeLoading.value = false
  }
}
// 计算两个数组的交集数量
const countCommonUsers = (levelUserIds: number[], rowUserIds: number[]): number | string => {
  // 使用 Set 加速查找（时间复杂度 O(n)）
  const rowSet = new Set(rowUserIds)
  const length = levelUserIds.filter((id) => rowSet.has(id)).length
  return length == 0 ? '' : length
}
//#endregion

//#region 项目交付
const deliveryLoading = ref(false)
const deliveryList = ref<any[]>([])
const deliveryDate = ref(new Date())
const deliveryType = ref<any>('year')
const scheduleList = ref<any[]>([])
const deliveryToolbarRef = ref()
const deliveryTableRef = ref()
const onListDelivery = async () => {
  deliveryLoading.value = true
  try {
    const startDate = dateUtil(deliveryDate.value).startOf(deliveryType.value).format('YYYY-MM-DD')
    const endDate = dateUtil(deliveryDate.value).endOf(deliveryType.value).format('YYYY-MM-DD')
    const res = await KanbanApi.getBasicsDelivery({ startDate, endDate })
    deliveryList.value = res
    scheduleList.value = transformDeliveryToSchedule(deliveryList.value)
    await nextTick()
    categoryList.value
      .filter((item) => item.templateCategory)
      .forEach((element) => {
        const tempData = deliveryList.value.find((el) => element.id == el.categoryId)
        let data: any = []
        for (let key in tempData) {
          switch (key) {
            case 'alarm':
              data.push({ name: '报警', value: tempData[key] })
              break
            case 'normal':
              data.push({ name: '正常', value: tempData[key] })
              break
            case 'overdue':
              data.push({ name: '延期', value: tempData[key] })
              break
            case 'suspended':
              data.push({ name: '暂停', value: tempData[key] })
              break
          }
        }
        var distributiontype = document.getElementById('delivery' + element.id)
        PieChart.refreshChart(
          data,
          echarts.init(distributiontype),
          element.name.replace('项目', '')
        )
      })
  } finally {
    deliveryLoading.value = false
  }
}
// 在onListDelivery方法后添加转换逻辑
const transformDeliveryToSchedule = (deliveryData: any[]) => {
  const statusTypes = ['alarm', 'normal', 'overdue', 'suspended']
  return statusTypes.map((status) => {
    let title = ''
    switch (status) {
      case 'alarm':
        title = '里程碑进度报警（超期且未完成）'
        break
      case 'normal':
        title = '里程碑进度正常（期限内完成）'
        break
      case 'overdue':
        title = '里程碑进度延期（超期但完成）'
        break
      case 'suspended':
        title = '里程碑进度暂停'
        break
    }
    const row: any = { title: title }
    let total = 0

    // 遍历所有项目类型（categoryList）
    categoryList.value.forEach((category) => {
      if (category.templateCategory) {
        // 根据categoryId找到对应状态的数值
        const value = deliveryData.find((item) => item.categoryId === category.id)?.[status] || 0
        row[category.id] = value // 直接使用categoryId作为键
        total += value
      }
    })

    row.total = total
    return row
  })
}

//#endregion

//#region 项目复盘
const reanalyzeLoading = ref(false)
const reanalyzeList = ref<any[]>([])
const reanalyzeToolbarRef = ref()
const reanalyzeTableRef = ref()
const onListReanalyze = async () => {
  reanalyzeLoading.value = true
  try {
    const res = await KanbanApi.getReanalyzeList()
    reanalyzeList.value = res
  } finally {
    reanalyzeLoading.value = false
  }
}

//#endregion

//#region 项目成本
const costLoading = ref(false)
const costList = ref<any[]>([])
const costToolbarRef = ref()
const costTableRef = ref()
const costDate = ref(new Date())
const onListCost = async () => {
  costLoading.value = true
  try {
    const res = await ExpenseApi.getExpenseList({
      endDate: dateUtil(costDate.value).format('YYYY-MM-DD')
    })
    costList.value = res
  } finally {
    costLoading.value = false
  }
}
//#endregion

//#region 个人效能
const ascensionLoading = ref(false)
const accensionList = ref<any[]>([])
const accensionSeList = ref<any[]>([])
const ascensionSeToolbarRef = ref()
const ascensionSeTableRef = ref()
const ascensionToolbarRef = ref()
const ascensionTableRef = ref()

const onListAscension = async () => {
  ascensionLoading.value = true
  try {
    const res = await KanbanApi.getPersonalAscension({
      roles: 'mould,attestation,iot,packing,testing,electronic,structure,pm',
      isCrux: false
    })
    accensionList.value = res
  } finally {
    ascensionLoading.value = false
  }
}
const onListSeAscension = async () => {
  ascensionLoading.value = true
  try {
    const res = await KanbanApi.getPersonalAscension({ roles: 'se', isCrux: true })
    accensionSeList.value = res
  } finally {
    ascensionLoading.value = false
  }
}

//#endregion
// 封装的统计函数
function calculateTotal(data: Distribution[], groupKey: (item: Distribution) => string | number) {
  return data.reduce(
    (acc, item) => {
      const key = groupKey(item)
      acc[key] = (acc[key] || 0) + item.total
      return acc
    },
    {} as Record<string | number, number>
  )
}
// 改装后的求均值函数
function calculateAverage(data: Distribution[], groupKey: (item: Distribution) => string | number) {
  const stats: Record<string | number, { total: number; count: number }> = {}
  data.forEach((item) => {
    const key = groupKey(item)
    if (!stats[key]) {
      stats[key] = { total: 0, count: 0 }
    }
    stats[key].total += item.total
    stats[key].count += 1
  })
  return Object.fromEntries(
    Object.entries(stats).map(([key, { total, count }]) => [
      key,
      count > 0 ? Number((total / count).toFixed(2)) : 0
    ])
  )
}

/** 表尾合计函数 */
function footerMethodSum({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '合计'
      }
      return sumNum(data, column.field)
    })
  ]
  return footerData
}

function footerMethodAvg({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '均值'
      }
      return avgNum(data, column.field).toFixed(2)
    })
  ]
  return footerData
}
// 进行合计
function sumNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i][type]
  }
  return total
}
function avgNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i][type]
  }
  return total / costForm.length
}

/** 获取合并单元格 */
const getMergeCell = (data: any[], colSize: number): any[] => {
  if (!data || data.length == 0) {
    return []
  }
  let preValue = ''
  let row = 0
  let rowspan = 0

  let result = [] as any[]
  for (let i = 0; i < data.length; i++) {
    for (let column in data[i]) {
      if (data[i][column] != preValue) {
        if (rowspan > 0) {
          result.push({ row, col: 0, rowspan: rowspan + 1, colspan: 0 })
        }
        row = i
        rowspan = 0
      } else {
        rowspan++
      }

      preValue = data[i][column]
      break
    }
  }
  if (rowspan > 0) {
    // 处理最后一段合并
    result.push({
      row,
      col: 0,
      rowspan,
      colSpan: 0
    })
  }

  let currentResult = [] as any[]
  for (let row of result) {
    currentResult.push(row)
    for (let i = 1; i < colSize; i++) {
      let newRow = { ...row }
      newRow.col = i
      currentResult.push(newRow)
    }
  }
  return currentResult
}

// 修改 onMounted 中的逻辑
onMounted(async () => {
  // 第一阶段：优先加载基础数据
  await onCategoryList()
  await onOfficeLevelList()
  await onListFilter()
  await onLevelFilter()

  // 第二阶段：延迟加载非关键模块
  setTimeout(async () => {
    const primaryModules = [
      onListDistributionList(),
      onListProgressDeviationList(),
      onListProblemCountList()
    ]
    await Promise.all(primaryModules)
    await nextTick()

    // 第三阶段：最后加载复杂图表模块
    setTimeout(() => {
      Promise.all([
        onOfficeLevelList(),
        onListDelivery(),
        onListReanalyze(),
        onListAscension(),
        onListSeAscension(),
        onListCost()
      ])

      // 延迟初始化复杂图表
      setTimeout(() => {
        // 图表连接逻辑
        Promise.all([
          unref(ascensionSeTableRef)?.connect(unref(ascensionSeToolbarRef)),
          unref(ascensionTableRef)?.connect(unref(ascensionToolbarRef)),
          unref(reanalyzeTableRef)?.connect(unref(reanalyzeToolbarRef)),
          unref(deliveryTableRef)?.connect(unref(deliveryToolbarRef)),
          unref(officeTableRef)?.connect(unref(officeToolbarRef)),
          unref(problemTableRef)?.connect(unref(problemToolbarRef)),
          unref(progressTableRef)?.connect(unref(progressToolbarRef)),
          unref(distributionTableRef)?.connect(unref(distributionToolbarRef)),
          unref(costTableRef)?.connect(unref(costToolbarRef))
        ])
      }, 500)
    }, 1000)
  }, 0)
})
</script>

<style lang="scss" scoped>
:deep(.el-card__header),
:deep(.el-card__body) {
  padding: 5px;
}
:deep(.el-card__header) {
  padding: 10px 10px !important;
}
</style>

<style lang="scss" scoped>
:deep(.vxe-toolbar) {
  padding: 0;
}
</style>
