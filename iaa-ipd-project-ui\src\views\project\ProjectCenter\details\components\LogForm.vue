<template>
  <div v-infinite-scroll="load" style="overflow: auto; height: 59vh">
    <el-timeline>
      <el-timeline-item v-for="item in list" :key="item.id">
        <div class="el-timeline-item__timestamp">
          {{ formatDate(item.createTime) }}-<span class="el-timeline-item__user">{{
            item.createName
          }}</span>
        </div>
        {{ item.content }}
        <div v-if="item.modifyFieldName">
          <template v-if="shouldShowDictTag(item.modifyField)">
            字段:{{ item.modifyFieldName }} 从:{{
              formatDictLabel(item.modifyField, item.beforeValue)
            }}
            修改为:{{ formatDictLabel(item.modifyField, item.afterValue) }}
          </template>
          <template
            v-else-if="
              ['managers', 'userIds', 'director', 'coordinate'].includes(item.modifyField) ||
              getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE).find(
                (dict) => dict.value === item.modifyField
              )?.label
            "
          >
            字段:{{ item.modifyFieldName }} 从:{{
              getUserNickName(props.userList, item.beforeValue)
            }}
            修改为:{{ getUserNickName(props.userList, item.afterValue) }}
          </template>
          <template v-else>
            字段:{{ item.modifyFieldName }} 从:{{ item.beforeValue }} 修改为:{{ item.afterValue }}
          </template>
        </div>
        <div v-if="item.processInstanceId">
          <el-link type="primary" @click="toBpm(item.processInstanceId)">跳转流程</el-link>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script lang="ts" setup>
import { formatDate } from '@/utils/formatTime'
import { BasicsApi } from '@/api/project/basics'
import { shouldShowDictTag, getUserNickName, formatDictLabel } from './utils'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { UserVO } from '@/api/system/user/index'

const props = defineProps({
  category: propTypes.string.isRequired,
  baseId: propTypes.number.isRequired,
  userList: propTypes.oneOfType([Array<UserVO>]).isRequired
})

const router = useRouter()

const loading = ref(true) // 列表的加载中
const list = ref<any[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  moduleId: undefined as unknown as string
})
/** 查询列表 */
const getList = async () => {
  if (!props.baseId) return
  loading.value = true
  try {
    queryParams.moduleId = props.category + props.baseId
    const data = await BasicsApi.getBasicsLogPage(queryParams)
    list.value = list.value.concat(data.list)
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const load = () => {
  queryParams.pageNo++
  if (queryParams.pageNo > Math.ceil(total.value / queryParams.pageSize)) return
  getList()
}

const refreshLog = () => {
  list.value = []
  queryParams.pageNo = 1
  getList()
}

watch(
  () => props.baseId,
  () => {
    if (props.baseId) {
      refreshLog()
    }
  },
  { immediate: true }
)

const toBpm = (id: string) => {
  router.push({
    name: 'BpmProcessInstanceDetail',
    query: {
      id: id
    }
  })
}

defineExpose({ refreshLog })
</script>
<style lang="scss" scoped>
:deep(.el-timeline-item__content) {
  white-space: pre-wrap;
  font-size: 1rem;
  color: var(--regular-text-color);
  background-color: #f8fcff;
  border-radius: 5px;
  padding: 5px;
}
.el-timeline-item__timestamp {
  padding: 3px;
  font-size: 1rem;

  .el-timeline-item__user {
    color: var(--el-color-warning);
    font-size: 1rem;
  }
}
</style>
