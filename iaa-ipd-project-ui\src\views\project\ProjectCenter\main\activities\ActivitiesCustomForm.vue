<template>
  <el-drawer v-model="visiable" :title="formTitle" size="50%">
    <el-collapse v-model="activeNames" ref="collapseRef" class="custom-collapse">
      <el-collapse-item title="基础信息" name="1">
        <el-form
          label-width="80px"
          class="custom-form"
          size="small"
          :model="formData"
          :rules="formRule"
          ref="formRef"
        >
          <el-form-item label="活动名称" prop="name">
            <el-input v-model="formData.name" />
          </el-form-item>
          <el-form-item label="活动内容">
            <el-input v-model="formData.content" type="textarea" :rows="3" />
          </el-form-item>
          <el-form-item label="活动描述">
            <el-input v-model="formData.description" type="textarea" :rows="6" />
          </el-form-item>
          <el-form-item label="负责人" prop="director">
            <user-avatar-list
              v-model="formData.director!"
              :user-list="props.userList"
              :size="28"
              :limit="3"
              :visiable-user-list="getVisiableUserList()"
            />
          </el-form-item>
          <el-form-item label="执行人">
            <user-avatar-list
              v-model="formData.coordinate!"
              :user-list="props.userList"
              :size="28"
              :limit="3"
              :visiable-user-list="getVisiableUserList()"
            />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startDate">
                <el-date-picker
                  v-model="formData.startDate"
                  value-format="YYYY-MM-DD"
                  class="!w-100%"
                  :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间" prop="endDate">
                <el-date-picker
                  v-model="formData.endDate"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  class="!w-100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="输出物审批流程" name="2">
        <div class="w-full">
          <div class="flex w-full bg-#faf5f3 text-.9rem">
            <div class="w-33% td">流程节点</div>
            <div class="w-33% td">流程审批人类型</div>
            <div class="w-33% td">审批人</div>
          </div>
          <div class="flex" v-for="node in activitiesApproveNode" :key="node.taskId">
            <div class="w-33% td">{{ node.taskName }}</div>
            <div class="w-33% td">
              <el-select v-model="node.userType" @change="node.userIds = undefined">
                <el-option label="项目角色" :value="0" />
                <el-option label="流程用户分组" :value="1" />
                <el-option label="负责人" :value="2" />
              </el-select>
            </div>
            <div class="w-33% td">
              <el-select v-model="node.userIds" multiple collapse-tags collapse-tags-tooltip>
                <template v-if="node.userType === 0">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.PROJECT_TEAM_ROLE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </template>
                <template v-if="node.userType === 1">
                  <el-option
                    v-for="item in userGroupList"
                    :key="item.id"
                    :label="item.name"
                    :value="String(item.id)"
                  />
                </template>
              </el-select>
            </div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="submit(false)">保存</el-button>
      <el-button type="primary" :loading="loading" @click="submit(true)">保存并继续添加</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { UserVO } from '@/api/system/user'
import { ActivitiesVO, ActivitiesApi } from '@/api/project/activities'
import { propTypes } from '@/utils/propTypes'
import { getVisiableUserList } from '../util/permission'
import { dateUtil } from '@/utils/dateUtil'
import * as UserGroupApi from '@/api/bpm/userGroup'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import * as DefinitionApi from '@/api/bpm/definition'
import { useCache } from '@/hooks/web/useCache'
import { BasicsVO } from '@/api/project/basics'
import * as ConfigApi from '@/api/infra/config'

const visiable = ref(false)
const formTitle = ref('')
const formData = ref<ActivitiesVO>({})
const activitiesApproveNode = ref<any[]>([])
const userGroupList = ref<any[]>([])
const formRule = reactive({
  name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  startDate: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
    {
      validator: (_rule, value, callback) => {
        const endDate = formData.value.endDate
        validateDateRange(value, endDate!, callback)
      },
      trigger: 'change'
    }
  ],
  endDate: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (_rule, value, callback) => {
        const endDate = formData.value.endDate
        validateDateRange(value, endDate!, callback)
      },
      trigger: 'change'
    }
  ],
  director: [{ required: true, message: '请选择负责人', trigger: 'change' }]
})
const formRef = ref()
const message = useMessage()
const loading = ref(false)
const activeNames = ref(['1', '2'])
const basicsInfo = ref<any>()
const { wsCache } = useCache()

const props = defineProps({
  userList: propTypes.oneOf<UserVO[]>([]).isRequired,
  basicsId: propTypes.number.isRequired,
  stage: propTypes.number.isRequired
})

const iotDate = ref(0)
const getIotDateConfig = async () => {
  const res = await ConfigApi.getConfigKey('iot.date')
  iotDate.value = res
}

const approvalTemplate = ref([
  { id: 'Activity_0wvczia', userType: 0, userIds: ['pqa', 'se'] },
  { id: 'Activity_1i92mwx', userType: 0, userIds: ['lpdt'] },
  { id: 'Activity_06biffq', userType: 0, userIds: [] }
])
/** 打开表单 */
const openForm = async (title: string) => {
  formTitle.value = title
  visiable.value = true
  await nextTick()
  approvalTemplate.value.forEach((el) => {
    const node = activitiesApproveNode.value.find((item) => el.id === item.taskId)
    node.userType = el.userType
    node.userIds = el.userIds
  })
}

const emits = defineEmits(['success', 'refresh'])

const validateDateRange = (startVal: string, endVal: string, callback: Function) => {
  if (!startVal || !endVal) return callback()

  const start = new Date(startVal)
  const end = new Date(endVal)
  if (end < start) {
    callback(new Error('结束时间不能早于开始时间'))
  } else {
    callback()
  }
}
/** 将ga时间之后的时间禁用，同时将同行第一个时间之前的时间禁用 */
const disabledDate = (time: Date) => {
  if (basicsInfo.value?.categoryIds?.includes(8) && iotDate.value == 1) {
    return false
  }
  return dateUtil().subtract(1, 'days').isAfter(time)
}
/** 加载所有的流程用户分组 */
const onListUserGroup = async () => {
  userGroupList.value = await UserGroupApi.getUserGroupSimpleList()
}

/** 切换流程 */
const onApproveChange = async () => {
  const userTask = await DefinitionApi.getProcessDefinition(undefined, 'activities-target')
  activitiesApproveNode.value = userTask?.startUserSelectTasks?.map((el) => {
    return {
      taskId: el.id,
      taskName: el.name,
      userType: undefined,
      userIds: undefined
    }
  })
}

const submit = async (allow: boolean) => {
  loading.value = true
  try {
    await unref(formRef).validate()
    await ActivitiesApi.createActivities({
      basicsId: props.basicsId,
      stage: props.stage,
      name: formData.value.name,
      content: formData.value.content,
      description: formData.value.description,
      director: formData.value.director,
      coordinate: formData.value.coordinate,
      startDate: formData.value.startDate,
      endDate: formData.value.endDate,
      mold: 1,
      targetApproveId: 'activities-target',
      approveNodes: activitiesApproveNode.value
    })
    message.success('保存成功')
    refresh()
    emits('success')
    if (!allow) {
      visiable.value = false
    }
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  approvalTemplate.value.forEach((el) => {
    const node = activitiesApproveNode.value.find((item) => el.id === item.taskId)
    node.userType = el.userType
    node.userIds = el.userIds
  })
  formData.value = {}
}

defineExpose({
  openForm
})
onMounted(() => {
  onListUserGroup()
  onApproveChange()
  basicsInfo.value = wsCache.get('PROJECT_BASICS_INFO') as BasicsVO
  getIotDateConfig()
})
</script>

<style lang="scss" scoped>
.el-tabs {
  display: block;
  height: 43px !important;
  --el-tabs-header-height: 43px !important;
  margin-bottom: 10px;
}
:deep(.el-tabs__content) {
  padding: 0;
}

.td {
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}
</style>
