<template>
  <ContentWrap>
    <el-button type="primary" @click="costCoefficientRef?.openModal()">设置工时成本费率</el-button>
  </ContentWrap>
  <el-row :gutter="10" class="h-[calc(100vh-160px)]" :loading="loading">
    <el-col :xs="24" :sm="24" :md="24" :lg="4" :xl="4" class="h-full">
      <ContentWrap class="h-full">
        <div class="h-52px">
          <el-input v-model="basicsName" class="mb-20px" clearable placeholder="请输入项目名称">
            <template #prefix>
              <Icon icon="ep:search" />
            </template>
          </el-input>
        </div>
        <div class="overflow-auto h-[calc(100vh-182px)]">
          <el-tree
            class="w-full overflow-hidden"
            ref="treeRef"
            :data="basicsList"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :props="defaultProps"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
          >
            <template #default="{ node }">
              <span>
                <Icon icon="ep:bicycle" />
              </span>
              <span class="ml-8px">{{ node.label }}</span>
            </template>
          </el-tree>
        </div>
      </ContentWrap>
    </el-col>
    <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="20" class="h-full">
      <ContentWrap class="h-full info-body">
        <template v-if="currentBasics?.id">
          <CardTitle title="项目基本信息" />
          <el-form label-width="100px" class="custom-form">
            <el-form-item label="项目名称"> {{ currentBasics.name }} </el-form-item>
            <el-form-item label="项目经理">
              <UserAvatarList
                v-model="currentBasics.managers!"
                :user-list="userList"
                :size="28"
                :add="false"
              />
            </el-form-item>
            <el-form-item label="所属分类">
              <el-select
                v-model="currentBasics.categoryIds"
                multiple
                placeholder="请选择项目组"
                :disabled="true"
              >
                <el-option
                  v-for="item in categoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  {{ item.name }}
                </el-option>
              </el-select>
            </el-form-item>
            <el-row>
              <el-col :span="8">
                <el-form-item label="项目等级">
                  <DictTag type="project_level" :value="currentBasics.level!" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目平台">
                  <DictTag type="project_platform" :value="currentBasics.platform!" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目类型">
                  <DictTag type="project_type" :value="currentBasics.mold!" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="发布时间">
              {{ currentBasics.releaseDate! }}
            </el-form-item>
            <el-form-item label="目标费用">
              <el-input-number
                v-model="currentBasics.planExpense!"
                :min="0"
                @change="onPlanExpenseChange"
              />
            </el-form-item>
            <el-form-item label="ERP 项目编码">
              <el-select
                v-model="currentBasics.erpCode!"
                clearable
                filterable
                @change="onErpCodeChange"
              >
                <el-option
                  v-for="item in erpCodeList"
                  :key="item.code"
                  :label="`${item.name}(${item.code})`"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="实际下单时间">
              <el-date-picker
                v-model="currentBasics.actualOrderDate!"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="选择日期"
                @change="onActualDateChange"
              />
            </el-form-item>
          </el-form>
          <div class="mb-20px"></div>
          <CardTitle title="成本费用信息" />
          <el-tabs>
            <el-tab-pane label="成本设置">
              <vxe-table
                ref="tableRef"
                :header-cell-style="{
                  padding: '0',
                  height: '2rem',
                  fontSize: '.9rem',
                  backgroundColor: '#fafafa',
                  color: 'var(--primary-text-color)'
                }"
                :cell-style="{
                  padding: '0',
                  height: '2.5rem',
                  fontSize: '.9rem',
                  color: 'var(--primary-text-color)'
                }"
                :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
                round
                border
                auto-resize
                :row-config="{ isCurrent: true, isHover: true }"
                :column-config="{ resizable: true, isHover: true }"
                :data="list"
                show-overflow
                :loading="loading"
                align="center"
              >
                <vxe-column title="阶段" width="100px" field="stageName" fixed="left" />
                <vxe-colgroup title="销售目标" field="plan">
                  <vxe-column
                    title="销量(万台)"
                    width="100px"
                    field="salesVolume"
                    :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                  />
                  <vxe-column
                    title="售价(人民币元，汇率7)"
                    width="100px"
                    field="price"
                    :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                  />
                </vxe-colgroup>
                <vxe-colgroup title="成本目标" field="costPlan">
                  <vxe-colgroup title="材料" field="material">
                    <vxe-column
                      title="基准BOM"
                      width="100px"
                      field="baseBom"
                      :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                    />
                    <vxe-column
                      title="包材"
                      width="100px"
                      field="packing"
                      :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                    />
                    <vxe-column title="合计" width="100px" field="materialTotal" />
                  </vxe-colgroup>
                  <vxe-colgroup title="人工" field="labor">
                    <vxe-column title="成本" width="100px" field="laborCost" />
                    <vxe-column
                      title="折算工时"
                      width="100px"
                      field="convertedHours"
                      :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                    />
                  </vxe-colgroup>
                  <vxe-column title="固定制费" width="100px" field="fixedFee" />
                  <vxe-column title="合计" width="100px" field="costTotal" />
                  <vxe-column
                    title="产品毛利"
                    width="100px"
                    field="maori"
                    :formatter="formatRate"
                  />
                </vxe-colgroup>
                <vxe-colgroup title="成本执行" field="actual">
                  <vxe-column
                    title="销量(万台)"
                    width="100px"
                    field="actualSalesVolume"
                    :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                  />
                  <vxe-column
                    title="售价(人民币元，汇率7)"
                    width="100px"
                    field="actualPrice"
                    :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                  />
                  <vxe-colgroup title="材料" field="actualMaterial">
                    <vxe-column
                      title="基准BOM"
                      width="100px"
                      field="actualBaseBom"
                      :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                    />
                    <vxe-column
                      title="包材"
                      width="100px"
                      field="actualPacking"
                      :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                    />
                    <vxe-column title="合计" width="100px" field="actualMaterialTotal" />
                  </vxe-colgroup>
                  <vxe-colgroup title="人工" field="actualLabor">
                    <vxe-column title="成本" width="100px" field="actualLaborCost" />
                    <vxe-column
                      title="折算工时"
                      width="100px"
                      field="actualConvertedHours"
                      :edit-render="{ name: 'VxeInput', props: { type: 'number', digits: 2 } }"
                    />
                  </vxe-colgroup>
                  <vxe-column title="固定制费" width="100px" field="actualFixedFee" />
                  <vxe-column title="合计" width="100px" field="actualCostTotal" />
                  <vxe-column
                    title="产品毛利"
                    width="100px"
                    field="actualMaori"
                    :formatter="formatRate"
                  />
                </vxe-colgroup>
                <vxe-column
                  title="产品成本达成率"
                  width="100px"
                  field="maoriComplete"
                  :formatter="formatRate"
                />
                <vxe-column title="操作" width="140px" fixed="right">
                  <template #default="{ row }">
                    <template v-if="hasEditStatus(row)">
                      <vxe-button @click="saveRowEvent(row)" size="mini" status="success"
                        >保存</vxe-button
                      >
                      <vxe-button @click="cancelRowEvent()" size="mini">取消</vxe-button>
                    </template>
                    <template v-else>
                      <vxe-button @click="editRowEvent(row)" size="mini" status="warning"
                        >编辑</vxe-button
                      >
                    </template>
                  </template>
                </vxe-column>
              </vxe-table>
            </el-tab-pane>
            <el-tab-pane label="物料费用" v-if="currentBasics?.erpCode">
              <vxe-table
                height="350px"
                :header-cell-style="{
                  padding: '0',
                  height: '2rem',
                  fontSize: '.9rem',
                  backgroundColor: '#fafafa',
                  color: 'var(--primary-text-color)'
                }"
                :cell-style="{
                  padding: '0',
                  height: '2.5rem',
                  fontSize: '.9rem',
                  color: 'var(--primary-text-color)'
                }"
                :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
                round
                border
                auto-resize
                :row-config="{ isCurrent: true, isHover: true }"
                :column-config="{ resizable: true, isHover: true }"
                :data="erpExpenseList"
                show-overflow
                :loading="loading"
                align="center"
                show-footer
                :footer-method="footerMethodSum"
              >
                <vxe-column title="项目编码" field="code" />
                <vxe-column title="项目名称" field="name" />
                <vxe-column title="单号" field="docNo" />
                <vxe-column title="单据类型" field="docType" />
                <vxe-column title="料号" field="itemCode" />
                <vxe-column title="物料名称" field="itemName" />
                <vxe-column title="数量" field="qty" />
                <vxe-column title="单位" field="unit" />
                <vxe-column title="金额" field="costMny" />
              </vxe-table>
            </el-tab-pane>
            <el-tab-pane label="报销费用" v-if="currentBasics?.erpCode">
              <vxe-table
                height="350px"
                :header-cell-style="{
                  padding: '0',
                  height: '2rem',
                  fontSize: '.9rem',
                  backgroundColor: '#fafafa',
                  color: 'var(--primary-text-color)'
                }"
                :cell-style="{
                  padding: '0',
                  height: '2.5rem',
                  fontSize: '.9rem',
                  color: 'var(--primary-text-color)'
                }"
                :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
                round
                border
                auto-resize
                :row-config="{ isCurrent: true, isHover: true }"
                :column-config="{ resizable: true, isHover: true }"
                :data="ekuaibaoExpenseList"
                show-overflow
                :loading="loading"
                align="center"
                show-footer
                :footer-method="footerMethodSum"
              >
                <vxe-column title="项目编码" field="code" />
                <vxe-column title="项目名称" field="name" />
                <vxe-column title="一级费用类型" field="expenseType1" />
                <vxe-column title="二级费用类型" field="expenseType2" />
                <vxe-column title="分摊金额" field="amount" />
                <vxe-column title="提交日期" field="submitDate" />
              </vxe-table>
            </el-tab-pane>

            <el-tab-pane label="项目人力投入成本">
              <vxe-table
                height="350px"
                :header-cell-style="{
                  padding: '0',
                  height: '2rem',
                  fontSize: '.9rem',
                  backgroundColor: '#fafafa',
                  color: 'var(--primary-text-color)'
                }"
                :cell-style="{
                  padding: '0',
                  height: '2.5rem',
                  fontSize: '.9rem',
                  color: 'var(--primary-text-color)'
                }"
                :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
                round
                border
                auto-resize
                :row-config="{ isCurrent: true, isHover: true }"
                :column-config="{ resizable: true, isHover: true }"
                :data="artificialCostList"
                show-overflow
                :loading="loading"
                align="center"
                show-footer
                :footer-method="footerMethodSum"
              >
                <vxe-column title="人员" field="manager" />
                <vxe-column title="角色" field="role" />
                <vxe-column title="阶段全称" field="stageName" />
                <vxe-column title="成本费率" field="costRate" />
                <vxe-column title="实际工时" field="actualHours" />
                <vxe-column title="实际成本" field="actualCost" />
              </vxe-table>
            </el-tab-pane>
          </el-tabs>
        </template>
        <el-empty v-else description="请选择项目" />
      </ContentWrap>
    </el-col>
  </el-row>
  <CostCoefficient ref="costCoefficientRef" />
</template>

<script lang="ts" setup>
import { BasicsApi, BasicsVO } from '@/api/project/basics'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import { CategoryApi, CategoryVO } from '@/api/project/category'
import { defaultProps } from '@/utils/tree'
import { CostApi } from '@/api/project/cost'
import { CodeApi } from '@/api/docking/code'
import { ExpenseApi } from '@/api/docking/expense'
import CostCoefficient from './CostCoefficient.vue'

const loading = ref(false)
const basicsList = ref<BasicsVO[]>([])
const basicsName = ref('')
const treeRef = ref()
const currentBasics = ref<BasicsVO>({})
const userList = ref<UserVO[]>([])
const categoryList = ref<CategoryVO[]>([])
const tableRef = ref()
const message = useMessage()
const erpCodeList = ref<any[]>([])
const erpExpenseList = ref<any[]>([])
const ekuaibaoExpenseList = ref<any[]>([])
const costCoefficientRef = ref()
const artificialCostList = ref<any[]>([])

const list = ref<any[]>([])
/** 获取项目列表 */
const onBasicsList = async () => {
  loading.value = true
  try {
    const res = await BasicsApi.getSimpleList()
    basicsList.value = res
  } finally {
    loading.value = false
  }
}

const onErpCodeList = async () => {
  const res = await CodeApi.getCodeList()
  erpCodeList.value = res
}

function formatRate({ cellValue }) {
  if (!cellValue) return ''
  return `${(cellValue*100).toFixed(2)}%`
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 触发点击事件 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  currentBasics.value = row
  getCostList()
  getArtificialCostList()
  if (row?.erpCode) {
    getExpenseList()
  }
}

/** 获取用户列表 */
const getUserList = async () => {
  const res = await getSimpleUserList()
  userList.value = res
}
/** 获取分类列表 */
const getCategoryList = async () => {
  const res = await CategoryApi.getCategoryList({})
  categoryList.value = res
}
/** 获取成本列表 */
const getCostList = async () => {
  loading.value = true
  try {
    const res = await CostApi.getCostList(currentBasics.value.id!)
    list.value = res
  } finally {
    loading.value = false
  }
}

const getArtificialCostList = async () => {
  loading.value = true
  try {
    const res = await CostApi.getArtificialCost(currentBasics.value.id!)
    artificialCostList.value = res
  } finally {
    loading.value = false
  }
}

const getExpenseList = async () => {
  erpExpenseList.value = await ExpenseApi.getErpList({ code: currentBasics.value.erpCode })
  ekuaibaoExpenseList.value = await ExpenseApi.getEkuaibaoList({
    code: currentBasics.value.erpCode
  })
}

const hasEditStatus = (row: any) => {
  const $table = tableRef.value
  if ($table) {
    return $table.isEditByRow(row)
  }
}

const editRowEvent = (row: any) => {
  const $table = tableRef.value
  if ($table) {
    $table.setEditRow(row)
  }
}

const saveRowEvent = async (row: any) => {
  const $table = tableRef.value
  if ($table) {
    await $table.clearEdit()
    await CostApi.saveCost(row)
    message.success('保存成功')
    getCostList()
  }
}

const cancelRowEvent = () => {
  const $table = tableRef.value
  if ($table) {
    $table.clearEdit()
  }
}

const onErpCodeChange = async () => {
  await BasicsApi.updateErpCode({
    id: currentBasics.value.id,
    erpCode: currentBasics.value.erpCode
  })
  if (currentBasics.value?.erpCode) {
    getExpenseList()
  }
  message.success('保存成功')
}

const onPlanExpenseChange = async () => {
  await BasicsApi.updatePlanExpense({
    id: currentBasics.value.id,
    planExpense: currentBasics.value.planExpense
  })
  message.success('保存成功')
}

const onActualDateChange = async () => {
  await BasicsApi.updateActualOrderDate({
    id: currentBasics.value.id,
    actualOrderDate: currentBasics.value.actualOrderDate
  })
  message.success('保存成功')
}

/** 表尾合计函数 */
function footerMethodSum({ columns, data }) {
  const footerData = [
    columns.map((column, _columnIndex) => {
      if (_columnIndex === 0) {
        return '合计'
      }
      if (['amount', 'costMny', 'actualHours', 'actualCost'].includes(column.field)) {
        return sumNum(data, column.field).toFixed(2)
      }
      return ''
    })
  ]
  return footerData
}

// 进行合计
function sumNum(costForm, type) {
  let total = 0
  for (let i = 0; i < costForm.length; i++) {
    total += costForm[i][type]
  }
  return total
}

/** 监听deptName */
watch(basicsName, (val) => {
  treeRef.value!.filter(val)
})

onMounted(() => {
  onBasicsList()
  getUserList()
  getCategoryList()
  onErpCodeList()
})
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 5px !important;
  height: 100%;
}

.info-body :deep(.el-card__body) {
  padding: 10px !important;
  height: 100%;
  overflow: auto;
}
</style>
