import request from '@/config/axios'

export const EncoderApi = {
  /** 生成编码 */
  generateEncode: (params: any) => {
    return request.get({ url: '/infra/encoder/generate', params })
  },
  /** 保存编码器 */
  saveEncoder: (data: any) => {
    return request.post({ url: '/infra/encoder/save', data })
  },
  /** 分页查询编码器 */
  page: (params: any) => {
    return request.get({ url: '/infra/encoder/page', params })
  },
  /** 删除编码器 */
  deleteEncoder: (id: number) => {
    return request.delete({ url: `/infra/encoder/${id}` })
  }
}
