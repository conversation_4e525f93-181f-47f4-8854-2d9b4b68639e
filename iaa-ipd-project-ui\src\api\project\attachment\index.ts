import request from '@/config/axios'

export type AttachmentRespVO = {
  id?: number // 输出物ID
  moduleId?: string // 模块ID
  name?: string // 文件ID
  currentVersion?: number //当前版本
  templateId?: number // 模板ID
  processInstanceId?: string // 流程实例ID
  approvalStatus?: number //审批状态
  infraFileId?: number // 文件ID
  targetType?: number // 输出物类型
  targetDockingId?: number // 对接ID
  dockingNo?: string // 对接编号
  creator?: number // 创建人
}

export const AttachmentApi = {
  /** 获取输出物列表 */
  getAttachmentList: (params: any) => {
    return request.get({ url: '/project/attachment/list', params })
  },
  /** 删除输出物 */
  delAttachment: (data: any) => {
    return request.post({ url: `/project/attachment/delAttachment`, data })
  },
  /** 获取依赖活动的输出物列表 */
  getAttachmentByDependencies: (id: number) => {
    return request.get({ url: `/project/attachment/list-dependencies/${id}` })
  },
  /** 根据项目ID获取输出物列表 */
  getAttachmentByBasicsId: (id: number) => {
    return request.get({ url: `/project/attachment/list-basics-id?basicsId=${id}` })
  }
}
