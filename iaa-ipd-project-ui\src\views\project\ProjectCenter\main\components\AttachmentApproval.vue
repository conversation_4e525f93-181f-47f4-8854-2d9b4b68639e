<template>
  <div class="flex">
    <div class="w-20% text-1rem whitespace-pre-line leading-15">{{ formData?.title }}</div>
    <div :class="['w-full', isMaximized ? 'maximized' : '', 'position-relative']">
      <el-tabs
        v-model="currentTab"
        class="!w-full header-tabs-container"
        type="border-card"
        @tab-change="onTabChange"
      >
        <el-tab-pane label="输出物" name="target" />
        <el-tab-pane label="输出参考" name="template" v-if="formData?.templateId" />
      </el-tabs>
      <el-button
        type="primary"
        class="position-absolute right-5px top-5px !text-1rem"
        plain
        @click="toggleMaximize"
        size="small"
        link
      >
        {{ isMaximized ? '还原' : '最大化' }}
      </el-button>
      <OfficeEditor
        :class="[isMaximized ? 'h-80vh' : '']"
        ref="officeEditorRef"
        :has-dialog="false"
        v-if="loading"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { AttachmentFlowApi } from '@/api/bpm/attachment'
import { FileTemplateApi } from '@/api/project/file/template'
import { propTypes } from '@/utils/propTypes'
const officeEditorRef = ref()
const { query } = useRoute()
const loading = ref(true)
const currentTab = ref('target')

const props = defineProps({
  id: propTypes.string.def('')
})

const formData = ref<any>()
const fileTemplateData = ref<any>()

const isMaximized = ref(false)

const toggleMaximize = () => {
  isMaximized.value = !isMaximized.value
}

const onInit = async () => {
  loading.value = false
  const id = props.id || (query.id as string)
  const res = await AttachmentFlowApi.getAttachmentFlow(id)
  formData.value = res
  loading.value = true
  requestAnimationFrame(() => {
    officeEditorRef.value.open(formData.value.infraFileId, formData.value.name)
  })
  if (formData.value.templateId) {
    fileTemplateData.value = await FileTemplateApi.getFileTemplate(formData.value.templateId)
  }
}

const onTabChange = async () => {
  if (currentTab.value === 'target') {
    officeEditorRef.value.open(formData.value.infraFileId, formData.value.name)
  } else {
    officeEditorRef.value.open(fileTemplateData.value.infraFileId, formData.value.name + '_参考')
  }
}

watch(
  () => [props.id, query.id],
  () => {
    const id = props.id || (query.id as string)
    if (id) {
      onInit()
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
:deep(.el-tabs__header) {
  margin-bottom: 3px !important;
}

:deep(.el-form-item__content) {
  color: var(--primary-text-color) !important;
  padding: 2px;
}

/** header tabs 样式 */
:deep(.header-tabs-container) {
  border: none;
  border-radius: 2px;
  background-color: #ebeff3;

  & > .el-tabs__header {
    background-color: #fff !important;
    border-bottom: 0.3px solid #d8d8d8;

    .el-tabs__nav {
      padding: 0 0px;
    }
    .el-tabs__item {
      height: 1.8rem;
      font-size: 1rem;
      color: var(--primary-text-color);
      &.is-active {
        border-top: 3px solid var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }

  & > .el-tabs__content {
    padding: 0;
  }
}

.maximized {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background-color: white;
  padding: 20px;
  box-sizing: border-box;
}
</style>
