<template>
  <!-- 对话框(添加 / 修改) -->
  <el-drawer :title="dialogTitle" v-model="visible" :size="'50%'">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="loading"
      label-width="120px"
      style="margin-top: 20px"
      class="custom-form"
      size="small"
    >
      <!-- 专利基本信息（只读） -->
      <el-divider content-position="left">专利基本信息</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item label="申请号">
            <el-input v-model="formData.applicationNo" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="公开号">
            <el-input v-model="formData.publicNo" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专利名称">
            <el-input v-model="formData.patentName" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="保护技术点">
            <el-input v-model="formData.protectionPoint" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请日期">
            <el-input v-model="formData.applicationDate" value-format="YYYY-MM-DD" readonly  />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="授权日期">
            <el-input v-model="formData.authorizationDate" value-format="YYYY-MM-DD" readonly  />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="有效期(年数)">
            <el-input v-model="formData.validityPeriod" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="法律状态">
            <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="formData.legalStatus" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="保护的产品型号">
            <el-input v-model="formData.models" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 提醒规则配置（可编辑） -->
      <el-divider content-position="left">提醒规则配置</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form-item label="提醒人员" prop="reminderUsers">
            <div class="user-selection-container">
              <user-avatar-list
                v-model="formData.reminderUsers"
                :user-list="userList"
                :size="32"
                :limit="10"
                :add="true"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提前天数" prop="remindDays">
            <el-input-number
              v-model="formData.remindDays"
              :min="1"
              :max="365"
              type="number"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提醒类型" prop="remindType">
            <el-select
              v-model="formData.remindType"
              placeholder="请选择提醒类型"
              style="width: 100%"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.PATENT_REMINDER_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="formData.status"
              :active-value="1"
              :inactive-value="0"
              active-text="启用"
              inactive-text="停用"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { MaintenanceReminderApi, MaintenanceReminderVO } from '@/api/patent/maintenance-reminder'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { UserVO, getSimpleUserList } from '@/api/system/user'
import UserAvatarList from '@/components/UserAvatarList/index.vue'

const visible = ref(false)
const formData = ref({
  id: undefined as number | undefined,
  maintenanceId: undefined as number | undefined,
  applicationNo: '' as string,
  publicNo: '' as string,
  patentName: '' as string,
  protectionPoint: '' as string,
  models: '' as string,
  applicationDate: '' as string,
  authorizationDate: '' as string,
  validityPeriod: undefined as number | undefined,
  legalStatus: 0 as number,
  reminderUsers: [] as number[],
  remindDays: 30,
  remindType: 0,
  status: 1
})

// 表单验证规则
const formRules = ref({
  reminderUsers: [{ required: true, message: '请选择提醒人员', trigger: 'change' }],
  remindDays: [{ required: true, message: '请输入提前天数', trigger: 'blur' }],
  remindType: [{ required: true, message: '请选择提醒类型', trigger: 'change' }]
})

// 抽屉的标题
const dialogTitle = ref('')
const formType = ref<'create' | 'edit'>('create')
const formRef = ref()
const loading = ref(false)
const message = useMessage()
const userList = ref<UserVO[]>([])

// 获取用户列表
const getUserList = async () => {
  try {
    userList.value = await getSimpleUserList()
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

/** 打开弹窗 */
const openForm = async (type: 'create' | 'edit', id?: number, maintenanceData?: any) => {
  visible.value = true
  formType.value = type
  resetForm()

  // 获取用户列表
  await getUserList()

  if (type === 'create' && maintenanceData) {
    // 新增模式，从专利维保数据中填充只读字段
    dialogTitle.value = '新增提醒规则'
    formData.value.maintenanceId = maintenanceData.maintenanceId
    formData.value.applicationNo = maintenanceData.applicationNo
    formData.value.publicNo = maintenanceData.publicNo
    formData.value.patentName = maintenanceData.patentName
    formData.value.protectionPoint = maintenanceData.protectionPoint
    formData.value.models = Array.isArray(maintenanceData.models) ? maintenanceData.models.join(', ') : maintenanceData.models
    formData.value.applicationDate = maintenanceData.applicationDate
    formData.value.authorizationDate = maintenanceData.authorizationDate
    formData.value.validityPeriod = maintenanceData.validityPeriod
    formData.value.legalStatus = maintenanceData.legalStatus
  } else if (type === 'edit' && id) {
    // 编辑模式，获取提醒规则数据
    dialogTitle.value = '编辑提醒规则'
    loading.value = true
    try {
      const data = await MaintenanceReminderApi.getMaintenanceReminder(id)
      formData.value = {
        ...data,
        models: Array.isArray(data.models) ? data.models.join(', ') : data.models,
        reminderUsers: Array.isArray(data.reminderUsers) ? data.reminderUsers : []
      }
    } finally {
      loading.value = false
    }
  }
}

//重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    maintenanceId: undefined,
    applicationNo: '',
    publicNo: '',
    patentName: '',
    protectionPoint: '',
    models: '',
    applicationDate: '',
    authorizationDate: '',
    validityPeriod: undefined,
    legalStatus: 0,
    reminderUsers: [] as number[],
    remindDays: 30,
    remindType: 0,
    status: 1
  }
}

const emit = defineEmits(['success'])
const submitForm = async () => {
  await formRef.value.validate()

  // 验证必要字段
  if (!formData.value.maintenanceId) {
    message.error('专利主表ID不能为空')
    return
  }

  loading.value = true
  try {
    const submitData = {
      ...formData.value,
      maintenanceId: formData.value.maintenanceId!,
      models: typeof formData.value.models === 'string' ? formData.value.models.split(', ').filter(Boolean) : formData.value.models
    }

    if (formType.value === 'edit') {
      await MaintenanceReminderApi.updateMaintenanceReminder(submitData)
      message.success('修改成功')
    } else {
      await MaintenanceReminderApi.createMaintenanceReminder(submitData)
      message.success('创建成功')
    }
    visible.value = false
    emit('success')
  } finally {
    loading.value = false
  }
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
.user-selection-container {
  width: 100%;
}

.dialog-footer {
  text-align: right;
  padding: 20px 0;
}

:deep(.el-drawer__body) {
  padding: 0 20px;
}
</style>