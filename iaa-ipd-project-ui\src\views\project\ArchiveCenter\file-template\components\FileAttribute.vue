<template>
  <div class="height-full" v-if="props.file">
    <div class="attribute-header">
      <div class="flex !content-center">
        <img
          class="w-50px h-50px"
          :src="getImagePath(props.file?.uri|| props.file?.url || '', props.file?.hasFolder)"
        />
        <div class="flex-1">
          <div class="file-title">
            {{ props.file?.name }}
          </div>
          <div class="file-size" v-if="props.file">
            {{ (props.file?.size / 1024).toFixed(2) }}KB，
            {{ formatTime(props.file?.createTime, 'yyyy-MM-dd HH:mm') }}
          </div>
        </div>
      </div>
    </div>
    <el-tabs
      v-model="currentTab"
      :tab-position="props.direction"
      stretch
      v-if="props.file"
      @tab-change="tabChange"
    >
      <el-tab-pane name="info">
        <template #label>
          <div class="tab-title">
            <Icon icon="fa:info-circle" />
            <div>属性</div>
          </div>
        </template>
        <div class="tab-content">
          <el-form size="small" label-width="80px" label-position="left" v-if="props.file">
            <el-form-item label="大小">
              {{ (props.file?.size / 1024).toFixed(2) }}KB（{{ props.file?.size }} Byte）
            </el-form-item>
            <el-form-item label="创建时间">
              {{ formatTime(props.file?.createTime, 'yyyy-MM-dd HH:mm:ss') }}
            </el-form-item>
            <el-form-item label="修改时间">
              {{ formatTime(props.file?.updateTime, 'yyyy-MM-dd HH:mm:ss') }}
            </el-form-item>
            <el-divider />
            <el-form-item label="创建人">
              {{ props.file?.createName }}
            </el-form-item>
            <el-form-item label="修改人">
              {{ props.file?.updateName }}
            </el-form-item>
            <el-divider />
            <el-form-item label="MD5">
              {{ props.file?.id }}
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="!props.file?.hasFolder" name="version">
        <template #label>
          <div class="tab-title">
            <Icon icon="fa:500px" />
            <div>版本</div>
          </div>
        </template>
        <div style="position: sticky; top: 0; height: 40px; background-color: #fff" v-if="props.update">
          <el-button size="small" style="width: 100%" @click="emits('upload:file')">
            <img src="@/assets/opertion/upload.png" class="mr-10px w-20px h-20px" />
            更新版本
          </el-button>
        </div>
        <el-timeline>
          <el-timeline-item
            placement="top"
            v-for="(item, index) in props.versions"
            :key="index"
            :timestamp="`${formatTime(item.createTime, 'yyyy-MM-dd')}-版本：${index == 0 ? '当前' : 'V' + item.version}`"
          >
            <el-card shadow="never" class="file-version-card">
              <template #header>
                <el-link
                  :underline="false"
                  style="color: var(--el-color-primary-light-3)"
                  @click="emits('open', item)"
                >
                  {{ item.name }}
                </el-link>
              </template>
              描述：{{ item.remark }}
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
      <el-tab-pane name="comment">
        <template #label>
          <div class="tab-title">
            <Icon icon="ep:chat-dot-square" />
            <div>讨论</div>
          </div>
        </template>
        <Comment
          category="filetemplate"
          :allow="true"
          :limit="5"
          ref="commentRef"
          color="#fcfcfc"
          class="position-absolute bottom-0 top-0 !w-90%"
          :at="false"
          :sharp="false"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import { getImagePath } from '@/utils/icon'
import { formatTime } from '@/utils'

const props = defineProps({
  direction: propTypes.oneOf(['top', 'bottom', 'left', 'right'] as any).isRequired,
  file: propTypes.object.isRequired,
  versions: propTypes.oneOfType([Array<any>]).def([]),
  update: propTypes.bool.def(true)
})

const currentTab = ref('info')
const commentRef = ref()

const tabChange = async (name: any) => {
  await nextTick()
  if (name === 'comment') {
    commentRef.value?.listEvent((props.file.hasFolder ? 'folder' : '') + props.file.id)
  }
}

watch(
  () => props.file,
  () => {
    currentTab.value = 'info'
  },
  { immediate: true }
)

const emits = defineEmits(['upload:file', 'open'])
</script>

<style lang="scss" scoped>
.height-full {
  height: 100%;

  .attribute-header {
    height: 60px;
    background: linear-gradient(to top right, #fafbfb, #ecf7fc); // 使用具体的颜色代码
    border-bottom: 0.3px solid #f1f1f1;
    padding: 5px;

    .file-title {
      font-size: 14px;
      height: 35px;
      color: #747575;
      line-height: 1.2;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制为两行 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal; /* 允许换行 */
      word-break: break-all; /* 确保英文单词在必要时可以断开 */
    }

    .file-size {
      color: #b9b9b9;
      font-size: 12px;
    }
  }

  .el-tabs {
    height: calc(100% - 60px);
  }
}

:deep(.el-tabs__content) {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
}

:deep(.el-tabs__header.is-bottom) {
  border-top: 1px solid #f1f1f1;
  .el-tabs__item {
    height: 44px;
    padding: 4px 0 0 0;
  }
}

:deep(.el-tabs__item.is-bottom.is-active) {
  &::after {
    content: '';
    position: absolute;
    top: 0px;
    height: 2px;
    width: 60px;
    background-color: var(--el-color-primary);
  }
}
:deep(.el-tabs__nav-wrap.is-bottom)::after,
:deep(.el-tabs__active-bar.is-bottom) {
  display: none;
}

:deep(.el-form-item__label) {
  color: #999999;
}

:deep(.el-form-item__content) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical; /* 设置盒模型的方向 */
  display: -webkit-box; /* 使用 -webkit-box 布局 */
}

:deep(.el-form-item) {
  margin-bottom: 5px;
}

.file-version-card {
  :deep(.el-card__header) {
    padding: 5px;
  }

  :deep(.el-card__body) {
    padding: 5px;
  }
}
</style>
