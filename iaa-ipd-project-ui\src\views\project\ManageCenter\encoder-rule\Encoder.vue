<template>
  <ContentWrap>
    <el-tabs v-model="currentTab" @tab-change="onTabChange">
      <el-tab-pane label="申请编码" name="application" />
      <el-tab-pane label="我的编码" name="my" />
    </el-tabs>
    <template v-if="currentTab === 'application'">
      <el-form label-width="120" class="min-h-400px" v-loading="loading">
        <el-form-item label="方案">
          <el-select v-model="formData.ruleId" @change="onChangeSegment">
            <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item v-for="item in segmentList" :key="item.id" :label="item.name">
          <div v-if="item.segment === 'placeholder'" class="placeholder">{{ item.value }}</div>
          <el-select v-model="formData[item.id!]" v-else-if="item.segment === 'dict'">
            <el-option
              v-for="dict in getStrDictOptions(item.value!)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <el-input v-model="formData[item.id!]" v-else-if="item.segment === 'custom'" />
          <div v-else-if="item.segment === 'serial'" class="placeholder">以系统生成为准</div>
        </el-form-item>
      </el-form>
      <div class="w-100% text-center">
        <el-button type="primary" :loading="loading" @click="onGenerateCode">生成编码</el-button>
        <div class="text-30px">{{ code }}</div>
        <el-button
          type="warning"
          plain
          :loading="loading"
          v-if="code"
          @click="handleCopyCode(code)"
        >
          一键复制
        </el-button>
      </div>
    </template>
    <template v-else>
      <div class="h-[calc(100vh-250px)]">
        <vxe-table :data="encoderList" height="100%" border>
          <vxe-column title="方案规则" field="ruleName" />
          <vxe-column title="编码" field="code" />
          <vxe-column title="状态" field="status" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="success" v-if="row.status == 0">有效</el-tag>
              <el-tag type="danger" v-else>无效</el-tag>
            </template>
          </vxe-column>
          <vxe-column title="操作" width="100">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleCopyCode(row.code)"> 复制</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        size="small"
        :total="encoderTotal"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="onListEncoder"
      />
    </template>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { EncoderRuleApi, EncoderRuleSegmentVO, EncoderRuleVO } from '@/api/project/encoder/rule'
import { EncoderApi } from '@/api/project/encoder/encoder'
import { getStrDictOptions } from '@/utils/dict'
import { useUserStore } from '@/store/modules/user'
const currentTab = ref('application')

const list = ref<EncoderRuleVO[]>([])
const segmentList = ref<EncoderRuleSegmentVO[]>([])
const formData = ref<any>({})
const loading = ref(false)
const code = ref('')
const onList = async () => {
  const res = await EncoderRuleApi.getEncoderRuleList()
  list.value = res
}

const onChangeSegment = async () => {
  const res = await EncoderRuleApi.getEncoderRuleSegmentList(formData.value.ruleId)
  segmentList.value = res
  formData.value = { ruleId: formData.value.ruleId }
}

const onGenerateCode = async () => {
  loading.value = true
  try {
    code.value = ''
    const res = await EncoderApi.generateCode(formData.value)
    code.value = res
  } finally {
    loading.value = false
  }
}

const handleCopyCode = async (val: string) => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(val)
    } else {
      const textArea = document.createElement('textarea')
      textArea.value = val
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
    console.error('复制失败:', error)
  }
}

const encoderList = ref<any[]>([])
const encoderTotal = ref(0)
const queryParams = ref({
  pageNo: 1,
  pageSize: 30,
  creator: undefined,
  type: ''
})
const { getUser } = useUserStore()

const onListEncoder = async () => {
  loading.value = true
  try {
    queryParams.value.creator = getUser.id as any
    queryParams.value.type = 'my'
    const res = await EncoderApi.getEncoderNewPage(queryParams.value)
    encoderList.value = res.list
    encoderTotal.value = res.total
  } finally {
    loading.value = false
  }
}

const onTabChange = () => {
  formData.value = { ruleId: formData.value.ruleId }
  if (currentTab.value == 'application') {
    onList()
  } else {
    onListEncoder()
  }
}

onMounted(() => {
  onList()
})
</script>

<style lang="scss" scoped>
.placeholder {
  width: 100%;
  background-color: #f2f4f7;
  padding: 0 10px;
}
</style>
