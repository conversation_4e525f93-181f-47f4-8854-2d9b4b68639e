import request from '@/config/axios'

export interface MaintenanceReminderVO {
  id?: number // 主键
  maintenanceId: number // 专利主表ID
  reminderUsers: number[] // 提醒人ID列表
  remindDays: number // 提前多少天提醒
  remindType: number // 提醒类型（0-续费，1-续期等）
  status: number // 状态（1-启用，0-停用）
  applicationNo: string // 申请号
  publicNo?: string // 公开号
  patentName: string // 专利名称
  protectionPoint: string // 保护技术点
  models?: string[] | string // 保护的产品型号
  applicationDate: string // 申请日期
  authorizationDate?: string // 授权日期
  validityPeriod?: number // 有效期(年数)
  legalStatus?: number // 法律状态
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

export const MaintenanceReminderApi = {
  /** 分页获取专利维护定时提醒 */
  getMaintenanceReminderPage: (data: any) => {
    return request.post({ url: '/patent/maintenance-reminder/page', data })
  },
  /** 创建专利维护定时提醒 */
  createMaintenanceReminder: (data: MaintenanceReminderVO) => {
    return request.post({ url: '/patent/maintenance-reminder/create', data })
  },
  /** 更新专利维护定时提醒 */
  updateMaintenanceReminder: (data: MaintenanceReminderVO) => {
    return request.post({ url: '/patent/maintenance-reminder/update', data })
  },
  /** 删除专利维护定时提醒 */
  deleteMaintenanceReminder: (id: number) => {
    return request.get({ url: `/patent/maintenance-reminder/delete/${id}` })
  },
  /** 获得专利维护定时提醒 */
  getMaintenanceReminder: async (id: number) => {
    return await request.get({ url: `/patent/maintenance-reminder/get?id=` + id })
  }
}
