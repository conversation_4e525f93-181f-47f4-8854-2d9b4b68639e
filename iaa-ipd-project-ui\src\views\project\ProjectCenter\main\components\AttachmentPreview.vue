<template>
  <Dialog :title="dialogName" v-model="show" width="80%" class="audit-dialog">
    <ProcessInstanceDetail :processInstanceId="processInstanceId" />
  </Dialog>
</template>

<script lang="ts" setup>
import ProcessInstanceDetail from '@/views/bpm/processInstance/detail/index.vue'

const show = ref(false)
const dialogName = ref('')
const processInstanceId = ref('')

const openForm = (name: string, instanceId: string) => {
  show.value = true
  dialogName.value = name
  processInstanceId.value = instanceId
  console.log(instanceId)
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>

</style>
