<template>
  <ContentWrap class="h-full">
    <div class="head-container">
      <el-input v-model="categoryName" class="mb-20px" clearable placeholder="请输入文件分类名称">
        <template #prefix>
          <Icon icon="ep:search" />
        </template>
      </el-input>
    </div>
    <div class="body-container">
      <el-tree
        ref="treeRef"
        :data="props.categoryList"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :props="defaultProps"
        :default-expanded-keys="[100]"
        highlight-current
        node-key="id"
        @node-click="handleNodeClick"
      >
        <template #default="{ node }">
          <div class="flex align-center">
            <img src="@/assets/file/folder.png" class="mr-10px w-20px h-20px" />
            <span>{{ node.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { defaultProps } from '@/utils/tree'
import { propTypes } from '@/utils/propTypes'

const categoryName = ref('')
const treeRef = ref()

const props = defineProps({
  categoryList: propTypes.oneOfType<any[]>([]).isRequired
})

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理分类被点击 */
const handleNodeClick = async (row: any) => {
  emits('node-click', row.id)
}

const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(categoryName, (val) => {
  treeRef.value!.filter(val)
})
</script>
<style lang="scss" scoped>
.body-container {
  height: 100%;
  overflow: auto;
}
</style>
