<template>
  <Dialog v-model="visiable" title="选择评审要素" width="80%">
    <vxe-table
      :data="props.list"
      height="600px"
      show-overflow
      :header-cell-style="{
        padding: '0',
        height: '2vw',
        fontSize: '1rem',
        backgroundColor: '#fafafa'
      }"
      :cell-style="{
        padding: '0',
        height: '2vw',
        fontSize: '1rem',
      }"
      :radio-config="{ highlight: true, trigger: 'row' }"
      ref="tableRef"
      stripe
      round
      auto-resize
      :column-config="{ resizable: true, isHover: true }"
    >
      <vxe-column type="radio" width="60" align />
      <vxe-column title="评审项" field="item" width="200" show-overflow="tooltip" />
      <vxe-column title="评审要素" field="factor" show-overflow="tooltip" />
      <vxe-column title="评审操作指导" field="description" show-overflow="tooltip" />
    </vxe-table>
    <template #footer>
      <el-button type="primary" @click="submit">确定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { TechnicalFactorVO } from '@/api/project/technicalfactor'
import { propTypes } from '@/utils/propTypes'

const visiable = ref(false)
const tableRef = ref()

const props = defineProps({
  list: propTypes.oneOfType<TechnicalFactorVO[]>([]).isRequired
})

const emits = defineEmits(['success'])

const submit = () => {
  const record = tableRef.value?.getRadioRecord(false)
  emits('success', record)
  visiable.value = false
}

const openForm = () => {
  visiable.value = true
}

defineExpose({
  openForm
})
</script>
