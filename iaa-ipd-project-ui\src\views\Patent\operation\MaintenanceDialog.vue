<template>
  <Dialog v-model="visible" title="选择专利" width="80%">
    <div class="h-[calc(100vh-280px)]">
      <vxe-table
        ref="tableRef"
        height="100%"
        :header-cell-style="{
          padding: '0',
          height: '2.5rem',
          fontSize: '.9rem',
          backgroundColor: '#fafafa',
          color: 'var(--primary-text-color)'
        }"
        :row-style="{
          cursor: 'pointer'
        }"
        :cell-style="{
          padding: '0',
          height: '2.5rem',
          fontSize: '.9rem',
          color: 'var(--primary-text-color)',
          cursor: 'pointer'
        }"
        :header-cell-config="{ height: 40 }"
        :cell-config="{ height: 40 }"
        :data="list"
        border
        align="center"
        stripe
        @cell-dblclick="cellClickEvent"
      >
        <vxe-column title="申请状态" field="status" width="90">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.PATENT_MAINTENANCE_STATUS" :value="row.status" />
          </template>
        </vxe-column>
        <vxe-column title="申请号" field="applicationNo" min-width="120" />
        <vxe-column title="公开号" field="publicNo" min-width="120" />
        <vxe-column title="专利名称" field="patentName" min-width="120" />
        <vxe-column title="当前权力人" field="ownership" min-width="120" />
        <vxe-column title="保护技术点" field="protectionPoint" min-width="120" />
        <vxe-column title="保护的产品型号" field="models" min-width="120" />
        <vxe-column title="专利类型" field="patentType" width="90">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.PATENT_TYPE" :value="row.patentType" />
          </template>
        </vxe-column>
        <vxe-column title="申请日期" width="120" field="applicationDate" />
        <vxe-column title="授权日期" width="120" field="authorizationDate" />
        <vxe-column title="有效期(年数)" field="validityPeriod" min-width="100" />
        <vxe-column title="获取方式" field="acquisitionMethod" min-width="120" />
        <vxe-column title="法律状态" field="legalStatus" width="90">
          <template #default="{ row }">
            <dict-tag :type="DICT_TYPE.LEGAL_STATUS" :value="row.legalStatus" />
          </template>
        </vxe-column>
        <vxe-column title="代理机构" field="agency" min-width="120" />
        <vxe-column title="同族专利" field="families" min-width="120" />
        <vxe-column title="维保评估" field="maintenanceAssessment" min-width="120" />
        <vxe-column title="创建时间" min-width="150" field="createTime" />
      </vxe-table>
    </div>
    <!-- 分页 -->
    <Pagination
      size="small"
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE } from '@/utils/dict'
import { MaintenanceApi } from '@/api/patent/maintenance'

const visible = ref(false)
const total = ref(0)
const list = ref<any[]>([])
const loading = ref(false)
const emit = defineEmits(['success'])

const queryParams = ref({
  pageNo: 1,
  pageSize: 30
})

const getList = async () => {
  loading.value = true
  try {
    const data = await MaintenanceApi.getMaintenancePage(queryParams.value)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleList = () => {
  queryParams.value.pageNo = 1
  getList()
}

const openForm = () => {
  visible.value = true
  handleList()
}

//双击编辑
const cellClickEvent: any = ({ row }) => {
  //触发父组件事件
  emit('success', row)
  //关闭弹窗
  visible.value = false
}

defineExpose({
  openForm
})
</script>
